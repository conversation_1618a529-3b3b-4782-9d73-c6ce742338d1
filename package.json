{"name": "hirenetix-backend-core", "version": "0.0.2", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "db:seed:roles": "ts-node -r tsconfig-paths/register src/database/seeders/seed-roles-standalone.ts", "db:seed:manual": "npm run db:seed:roles", "db:seed:super-admin": "ts-node -r tsconfig-paths/register src/database/seeders/users/seed-super-admin.ts", "db:seed:permissions": "ts-node -r tsconfig-paths/register src/database/seeders/seed-permissions-standalone.ts", "db:seed:assign-permissions": "ts-node -r tsconfig-paths/register src/database/seeders/assign-admin-permissions.ts", "db:seed:job-templates": "ts-node -r tsconfig-paths/register src/database/seeders/jobs/job-templates.seeder.ts", "db:seed:all": "npm run db:seed:roles && npm run db:seed:super-admin && npm run db:seed:permissions && npm run db:seed:assign-permissions", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli", "migration:run": "npm run typeorm -- -d src/providers/database/postgres/data-source.ts migration:run", "migration:revert": "npm run typeorm -- -d src/providers/database/postgres/data-source.ts migration:revert", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.815.0", "@aws-sdk/client-ses": "^3.787.0", "@aws-sdk/client-textract": "^3.812.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.4.17", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^8.1.1", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@types/passport-jwt": "^4.0.1", "axios": "^1.9.0", "bcrypt": "^5.1.1", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto": "^1.0.1", "csv-stringify": "^6.5.2", "dotenv": "^16.4.7", "ioredis": "^5.6.0", "mammoth": "^1.9.0", "moment": "^2.30.1", "mongoose": "^8.13.2", "multer": "^2.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.14.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.22", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@hirenetix/(.*)$": "<rootDir>/$1"}}}