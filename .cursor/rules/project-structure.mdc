---
description:
globs:
alwaysApply: false
---
# Project Structure Guide

This project is a NestJS application, a framework for building efficient, scalable Node.js server-side applications.

## Key Files and Directories:

*   **`src/`**: This directory contains the main source code for the application.
    *   The typical entry point for a NestJS application is `[src/main.ts](mdc:src/main.ts)`.
*   **`package.json`**: Defines project metadata, dependencies, and scripts. See `[package.json](mdc:package.json)`.
*   **`nest-cli.json`**: Configuration file for the NestJS CLI. See `[nest-cli.json](mdc:nest-cli.json)`.
*   **`tsconfig.json`**: TypeScript compiler options for the project. See `[tsconfig.json](mdc:tsconfig.json)`.
*   **`dist/`**: This directory contains the compiled JavaScript code ready for deployment. It's usually generated by the build process.
*   **`node_modules/`**: Contains all the project's dependencies.
*   **`test/`**: Contains test files, typically for end-to-end (e2e) tests in a NestJS project.

## Common NestJS Structure within `src/`:

NestJS applications are typically organized into modules, controllers, and services:

*   **Modules (`*.module.ts`)**: Organize the application into cohesive blocks of functionality. The root module is usually `[src/app.module.ts](mdc:src/app.module.ts)`.
*   **Controllers (`*.controller.ts`)**: Handle incoming requests and return responses to the client.
*   **Services (`*.service.ts`)**: Contain the business logic and interact with data sources.
