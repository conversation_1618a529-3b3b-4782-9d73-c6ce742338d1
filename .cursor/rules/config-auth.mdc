---
description:
globs:
alwaysApply: false
---
# Configuration and Authentication

This application uses structured configuration and a JWT-based authentication system.

## Configuration Structure:

Configuration is organized by domain in `[src/config](mdc:src/config)`:

* **API**: `[src/config/api](mdc:src/config/api)` - API configuration settings
* **App**: `[src/config/app](mdc:src/config/app)` - Core application configuration
* **Auth**: `[src/config/auth](mdc:src/config/auth)` - Authentication configuration
* **Cache**: `[src/config/cache](mdc:src/config/cache)` - Caching configuration
* **Database**: `[src/config/database](mdc:src/config/database)` - Database connection settings
* **Queue**: `[src/config/queue](mdc:src/config/queue)` - Job queue configuration
* **Session**: `[src/config/session](mdc:src/config/session)` - Session management
* **Storage**: `[src/config/storage](mdc:src/config/storage)` - File storage configuration
* **Swagger**: `[src/config/swagger](mdc:src/config/swagger)` - API documentation

## Authentication System:

The application uses JWT-based authentication with the following components:

* **Auth Module**: `[src/authentication/auth.module.ts](mdc:src/authentication/auth.module.ts)` - Main authentication module
* **Auth Service**: `[src/authentication/auth.service.ts](mdc:src/authentication/auth.service.ts)` - Authentication business logic
* **Auth Controller**: `[src/authentication/auth.controller.ts](mdc:src/authentication/auth.controller.ts)` - Authentication endpoints
* **JWT Strategy**: `[src/authentication/jwt.strategy.ts](mdc:src/authentication/jwt.strategy.ts)` - Passport JWT strategy
* **Auth Guard**: `[src/authentication/jwt-auth.guard.ts](mdc:src/authentication/jwt-auth.guard.ts)` - Route protection

## Authentication Flow:

1. Users authenticate via login endpoints in Auth Controller
2. Auth Service validates credentials and issues JWT tokens
3. JWT tokens are used in subsequent requests
4. JWT Strategy validates tokens and extracts user information
5. Auth Guards protect routes requiring authentication
6. Role-based permissions likely control access to specific endpoints
