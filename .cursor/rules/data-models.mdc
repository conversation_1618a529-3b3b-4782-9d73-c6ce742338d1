---
description:
globs:
alwaysApply: false
---
# Data Models and Database

This application uses database models that are organized by domain entities. The data layer is separated from the business logic.

## Key Model Domains:

* **Users**: `[src/models/users](mdc:src/models/users)` - User-related data models
* **Organizations**: `[src/models/organisations](mdc:src/models/organisations)` - Organization entity models
* **Roles**: `[src/models/roles](mdc:src/models/roles)` - Role-based access control models
* **Departments**: `[src/models/departments](mdc:src/models/departments)` - Department-related models
* **Locations**: `[src/models/locations](mdc:src/models/locations)` - Location-related models
* **Shared**: `[src/models/shared](mdc:src/models/shared)` - Shared/common data models

## Database Management:

The application's database structure is managed through:

* **Migrations**: `[src/database/migrations](mdc:src/database/migrations)` - Database schema changes
* **Seeders**: `[src/database/seeders](mdc:src/database/seeders)` - Seed data for development/testing
* **Factories**: `[src/database/factories](mdc:src/database/factories)` - Factory patterns for test data generation
* **Configuration**: `[src/config/database](mdc:src/config/database)` - Database configuration

## Database Patterns:

* Models likely use TypeORM entity decorators (@Entity, @Column, etc.)
* Relations between entities are managed through TypeORM relationship decorators
* Entity lifecycle hooks may be utilized for data validation and transformation
* Repositories handle database operations following the repository pattern
