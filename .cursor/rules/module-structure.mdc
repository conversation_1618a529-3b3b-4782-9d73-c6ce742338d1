---
description:
globs:
alwaysApply: false
---
# Module Structure

This application follows a feature-based module organization pattern common in NestJS applications. Each module encapsulates related functionality.

## Key Modules:

* **Users**: `[src/modules/users](mdc:src/modules/users)` - Handles user management functionality
* **Authentication**: `[src/authentication](mdc:src/authentication)` - Manages authentication with JWT strategy
* **Roles & Permissions**: `[src/modules/roles-permissions](mdc:src/modules/roles-permissions)` - Role-based access control system
* **Organizations**: `[src/modules/organisations](mdc:src/modules/organisations)` - Organization management
* **Departments**: `[src/modules/departments](mdc:src/modules/departments)` - Department management within organizations
* **Locations**: `[src/modules/locations](mdc:src/modules/locations)` - Location-based functionality
* **Tenant**: `[src/modules/tenant](mdc:src/modules/tenant)` - Multi-tenant functionality
* **Super Admin**: `[src/modules/super-admin](mdc:src/modules/super-admin)` - Administrative functionality

## Module Structure Pattern

Each module typically follows this structure:
- `*.module.ts` - Module definition with imports, controllers, and providers
- `*.controller.ts` - REST API endpoints for the feature
- `*.service.ts` - Business logic implementation
- `dto/` - Data Transfer Objects for request/response validation
- `interfaces/` - TypeScript interfaces for the module

The application follows a clean architecture pattern with separation of concerns between controllers (API layer), services (business logic), and models (data layer).
