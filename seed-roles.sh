#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting database seed script...${NC}"

# Run the seed script with TypeScript Node
echo -e "${GREEN}Seeding roles and permissions...${NC}"
npm run db:seed:roles

# Check if the command was successful
if [ $? -eq 0 ]; then
  echo -e "${GREEN}Seed operation completed successfully!${NC}"
  exit 0
else
  echo -e "${RED}Seed operation failed${NC}"
  exit 1
fi 