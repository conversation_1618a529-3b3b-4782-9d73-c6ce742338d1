import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Param,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ConversationsService } from './conversations.service';
import {
  CreateConversationDto,
  ConversationResponseDto,
} from './dto/create-conversation.dto';
import { ThrottlerGuard } from '@nestjs/throttler';
import { ActiveConversationResponseDto } from './dto/get-conversation.dto';
import { JwtAuthGuard } from '../../authentication/jwt-auth.guard';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { RequirePermissions } from '../../common/decorators/metadata/permissions.decorator';
import {
  PermissionModules,
  PermissionActions,
} from '../../models/roles/constants/role.constants';

@ApiTags('Conversations')
@Controller('conversations')
export class ConversationsController {
  constructor(private readonly conversationsService: ConversationsService) {}

  @Post()
  @UseGuards(ThrottlerGuard)
  @ApiOperation({ summary: 'Create a new conversation' })
  @ApiResponse({
    status: 201,
    description: 'The conversation has been successfully created.',
    type: ConversationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 404,
    description: 'Candidate not found',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests',
  })
  async createConversation(
    @Body() createConversationDto: CreateConversationDto,
  ): Promise<ConversationResponseDto> {
    return this.conversationsService.createConversation(createConversationDto);
  }

  @Get('candidate/:candidateId/active')
  @ApiOperation({ summary: 'Get the last active conversation for a candidate' })
  @ApiResponse({
    status: 200,
    description:
      'Returns the last active conversation with messages if found, null otherwise.',
    type: ActiveConversationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid UUID format',
  })
  @ApiResponse({
    status: 404,
    description: 'Candidate not found',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests',
  })
  @UseGuards(ThrottlerGuard)
  async getLastActiveConversation(
    @Param('candidateId', ParseUUIDPipe) candidateId: string,
  ): Promise<ActiveConversationResponseDto | null> {
    return this.conversationsService.getLastActiveConversation(candidateId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get conversation by ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the conversation with messages',
    type: ConversationResponseDto,
  })
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions({
    module: PermissionModules.CANDIDATES,
    action: PermissionActions.VIEW_JOB_APPLICATIONS,
  })
  async getConversationById(
    @Param('id') id: string,
  ): Promise<ConversationResponseDto> {
    return this.conversationsService.getConversationById(id);
  }
}
