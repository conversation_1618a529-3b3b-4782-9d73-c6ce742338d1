import { ApiProperty } from '@nestjs/swagger';
import { MessageType } from '../../../models/conversations/entities/message.entity';

export class MessageResponseDto {
  @ApiProperty({
    description: 'Message ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Message content',
    example: 'Hello, how are you?',
  })
  content: string;

  @ApiProperty({
    description: 'Message type',
    enum: MessageType,
    example: 'text',
  })
  messageType: MessageType;

  @ApiProperty({
    description: 'Message metadata',
    example: { confidence: 0.95 },
    required: false,
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'When the message was sent',
    example: '2024-03-20T10:00:00Z',
  })
  sentAt: Date;

  @ApiProperty({
    description: 'Sender details',
    type: 'object',
    nullable: true,
    additionalProperties: false,
  })
  sender?: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Receiver details',
    type: 'object',
    nullable: true,
    additionalProperties: false,
  })
  receiver?: {
    id: string;
    name: string;
  };
}

export class ActiveConversationResponseDto {
  @ApiProperty({
    description: 'Conversation ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Candidate ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  candidateId: string;

  @ApiProperty({
    description: 'Conversation status',
    example: 'active',
    enum: ['active', 'closed'],
  })
  status: string;

  @ApiProperty({
    description: 'When the conversation started',
    example: '2024-03-20T10:00:00Z',
  })
  startedAt: Date;

  @ApiProperty({
    description: 'Candidate details',
    type: 'object',
    additionalProperties: false,
  })
  candidate: {
    id: string;
    name: string;
    email: string;
  };

  @ApiProperty({
    description: 'Messages in the conversation',
    type: [MessageResponseDto],
  })
  messages: MessageResponseDto[];
}
