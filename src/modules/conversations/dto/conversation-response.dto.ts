import { ApiProperty } from '@nestjs/swagger';
import { ConversationStatus } from '../../../models/conversations/entities/conversation.entity';

export class ConversationMessageDto {
  @ApiProperty({
    description: 'ID of the message',
  })
  id: string;

  @ApiProperty({
    description: 'Content of the message',
  })
  content: string;

  @ApiProperty({
    description: 'Type of the message',
  })
  type: string;

  @ApiProperty({
    description: 'Timestamp when the message was created',
  })
  createdAt: Date;
}

export class ConversationResponseDto {
  @ApiProperty({
    description: 'ID of the conversation',
  })
  id: string;

  @ApiProperty({
    description: 'ID of the candidate',
    nullable: true,
  })
  candidateId: string;

  @ApiProperty({
    description: 'Status of the conversation',
    enum: ConversationStatus,
  })
  status: ConversationStatus;

  @ApiProperty({
    description: 'When the conversation started',
  })
  startedAt: Date;

  @ApiProperty({
    description: 'When the conversation ended',
    nullable: true,
  })
  endedAt: Date;

  @ApiProperty({
    description: 'Array of messages in the conversation',
    type: [ConversationMessageDto],
  })
  messages: ConversationMessageDto[];

  @ApiProperty({
    description: 'Timestamp when the conversation was created',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp when the conversation was last updated',
  })
  updatedAt: Date;
}
