import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsUUID,
  IsOptional,
  IsNotEmpty,
  IsString,
  IsEnum,
  ValidateNested,
  ValidateIf,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MessageType } from '../../../models/conversations/entities/message.entity';
import { JobApplicationStatus } from '../../../models/jobs/entities/job-application.entity';

export class CreateMessageDto {
  @ApiProperty({
    description:
      'Message content (required for text and audio types, empty for image and file types)',
    example: 'Hello, how are you?',
  })
  @ValidateIf(
    (o) =>
      o.messageType === MessageType.TEXT || o.messageType === MessageType.AUDIO,
  )
  @IsNotEmpty({
    message: 'Content is required for text and audio message types',
  })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiProperty({
    description: 'Message type',
    enum: MessageType,
    example: MessageType.TEXT,
  })
  @IsEnum(MessageType)
  messageType: MessageType;

  @ApiProperty({
    description:
      'Message metadata (required for image and file types to store URLs and additional information)',
    example: {
      url: 'https://example.com/image.jpg',
      size: 1024,
      mimeType: 'image/jpeg',
    },
  })
  @ValidateIf(
    (o) =>
      o.messageType === MessageType.IMAGE || o.messageType === MessageType.FILE,
  )
  @IsNotEmpty({
    message: 'Metadata is required for image and file message types',
  })
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'ID of the sender',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  senderId?: string;

  @ApiPropertyOptional({
    description: 'ID of the receiver',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  receiverId?: string;

  @ApiPropertyOptional({
    description: 'ID of the question',
    example: 'q_experience',
  })
  @IsOptional()
  @IsString()
  questionId?: string;
}

export class CreateConversationDto {
  @ApiPropertyOptional({
    description: 'ID of the candidate',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  candidateId?: string;

  @ApiPropertyOptional({
    description: 'ID of the existing conversation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  conversationId?: string;

  @ApiPropertyOptional({
    description: 'ID of the job',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  jobId?: string;

  @ApiPropertyOptional({
    description: 'ID of the organisation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  organisationId?: string;

  @ApiProperty({
    description: 'Message to be sent',
    type: CreateMessageDto,
  })
  @ValidateNested()
  @Type(() => CreateMessageDto)
  message: CreateMessageDto;

  @ApiPropertyOptional({
    description: 'ID of the question',
    example: 'q_experience',
  })
  @IsString()
  questionId?: string;

  @ApiPropertyOptional({
    description: 'Application status',
    example: 'applied',
  })
  @IsOptional()
  @IsEnum(JobApplicationStatus)
  @IsString()
  applicationStatus?: string;
}

export class MessageDto {
  @ApiProperty({
    description: 'Message content',
    example: 'Hello, how are you?',
  })
  content: string;

  @ApiProperty({
    description: 'Message type',
    enum: MessageType,
    example: 'text',
  })
  messageType: MessageType;

  @ApiProperty({
    description: 'Message metadata',
    example: { confidence: 0.95 },
    required: false,
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Question ID',
    example: 1,
    required: false,
  })
  questionId?: number;

  @ApiProperty({
    description: 'Sender ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  senderId?: string;

  @ApiProperty({
    description: 'Receiver ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  receiverId?: string;
}

export class MessageResponseDto {
  @ApiProperty({
    description: 'Message ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Message content',
    example: 'Hello, how are you?',
  })
  content: string;

  @ApiProperty({
    description: 'Message type',
    enum: MessageType,
    example: 'text',
  })
  messageType: MessageType;

  @ApiProperty({
    description: 'Message metadata',
    example: { confidence: 0.95 },
    required: false,
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'When the message was sent',
    example: '2024-03-20T10:00:00Z',
  })
  sentAt: Date;

  @ApiProperty({
    description: 'Sender ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  senderId?: string;

  @ApiProperty({
    description: 'Receiver ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  receiverId?: string;

  @ApiProperty({
    description: 'Sender details',
    type: 'object',
    nullable: true,
    additionalProperties: false,
  })
  sender?: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Receiver details',
    type: 'object',
    nullable: true,
    additionalProperties: false,
  })
  receiver?: {
    id: string;
    name: string;
  };
}

export class ConversationResponseDto {
  @ApiProperty({
    description: 'Conversation ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Candidate ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  candidateId: string;

  @ApiProperty({
    description: 'Conversation status',
    example: 'active',
  })
  status: string;

  @ApiProperty({
    description: 'When the conversation started',
    example: '2024-03-20T10:00:00Z',
  })
  startedAt: Date;

  @ApiProperty({
    description: 'When the conversation ended',
    example: '2024-03-20T11:00:00Z',
    required: false,
  })
  endedAt?: Date;

  @ApiProperty({
    description: 'Whether this is a new conversation',
    example: true,
  })
  isNewConversation: boolean;

  @ApiProperty({
    description: 'Candidate details',
    type: 'object',
    additionalProperties: false,
  })
  candidate?: {
    id: string;
    name: string;
    email: string;
  };

  @ApiProperty({
    description: 'Messages in the conversation',
    type: [MessageResponseDto],
    required: false,
  })
  messages?: MessageResponseDto[];
}
