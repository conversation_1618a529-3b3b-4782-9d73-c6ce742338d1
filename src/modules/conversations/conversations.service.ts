import { Injectable, NotFoundException } from '@nestjs/common';
import { ConversationRepository } from '../../models/conversations/repositories/conversation.repository';
import {
  CreateConversationDto,
  ConversationResponseDto,
} from './dto/create-conversation.dto';
import { CandidateRepository } from '../../models/candidates/repositories/candidate.repository';
import { CANDIDATE_MESSAGES } from '../../common/constants/message.constants';
import { ActiveConversationResponseDto } from './dto/get-conversation.dto';
import { MessageRepository } from '../../models/conversations/repositories/message.repository';
import { ConversationStatus } from '../../models/conversations/entities/conversation.entity';
import { JobApplicationRepository } from '../../models/jobs/repositories/job-application.repository';
import { JobApplicationStatus } from '../../models/jobs/entities/job-application.entity';
import { JobPostRepository } from '../../models/jobs/repositories/job-post.repository';
import { JOB_POST_MESSAGES } from '../../common/constants/message.constants';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Conversation } from '../../models/conversations/entities/conversation.entity';
import { Message } from '../../models/conversations/entities/message.entity';
import { AiInsightsService } from '../jobs/ai-insights/ai-insights.service';

@Injectable()
export class ConversationsService {
  constructor(
    private readonly conversationRepository: ConversationRepository,
    private readonly candidateRepository: CandidateRepository,
    private readonly messageRepository: MessageRepository,
    private readonly jobApplicationRepository: JobApplicationRepository,
    private readonly jobPostRepository: JobPostRepository,
    @InjectRepository(Conversation)
    private readonly conversationRepositoryTypeorm: Repository<Conversation>,
    @InjectRepository(Message)
    private readonly messageRepositoryTypeorm: Repository<Message>,
    private readonly aiInsightsService: AiInsightsService,
  ) {}

  async createConversation(
    createConversationDto: CreateConversationDto,
  ): Promise<ConversationResponseDto> {
    let conversation: any;
    let candidate: any;
    let jobApplicationId: any;
    let isNewConversation = false;

    // If candidateId is provided, verify it exists
    if (createConversationDto.candidateId) {
      candidate = await this.candidateRepository.findById(
        createConversationDto.candidateId,
      );
      if (!candidate) {
        throw new NotFoundException(CANDIDATE_MESSAGES.CANDIDATE_NOT_FOUND);
      }
    }

    // If conversationId is provided, verify it exists
    if (createConversationDto.conversationId) {
      conversation = await this.conversationRepository.findByConversationId(
        createConversationDto.conversationId,
      );

      // If conversation is not found, create a new one
      if (!conversation) {
        isNewConversation = true;
        conversation = await this.conversationRepository.create({
          candidateId: createConversationDto?.candidateId,
          status: ConversationStatus.ACTIVE,
          conversationId: createConversationDto.conversationId,
        });

        // If this is a new conversation and we have jobId, create an application
        if (createConversationDto.jobId) {
          //Check if the jobId belongs to the tenant
          const job = await this.jobPostRepository.findPublicJobPostById(
            createConversationDto.jobId,
            createConversationDto.organisationId,
          );
          if (!job) {
            throw new NotFoundException(
              JOB_POST_MESSAGES.JOB_POST_NOT_FOUND_FOR_ORGANISATION,
            );
          }

          const jobApplication =
            await this.jobApplicationRepository.createJobApplication({
              candidateId: createConversationDto.candidateId,
              jobId: createConversationDto.jobId,
              status: JobApplicationStatus.INCOMPLETE,
              organisationId: createConversationDto.organisationId,
              conversationId: conversation.conversationId,
            });

          jobApplicationId = jobApplication.id;
        }
      } else {
        if (
          createConversationDto.applicationStatus &&
          createConversationDto.jobId
        ) {
          const jobApplications =
            await this.jobApplicationRepository.findByJobId(
              createConversationDto.jobId,
            );

          if (jobApplications) {
            jobApplications.forEach(async (jobApplication) => {
              await this.jobApplicationRepository.updateById(
                jobApplication.id,
                {
                  status:
                    createConversationDto.applicationStatus as JobApplicationStatus,
                },
              );
            });
          } else {
            throw new NotFoundException(
              JOB_POST_MESSAGES.JOB_POST_NOT_FOUND_FOR_ORGANISATION,
            );
          }
        }
      }
    }

    // Create the message
    const messageData: any = {
      conversationId: conversation.id,
      messageType: createConversationDto.message.messageType,
      metadata: createConversationDto.message.metadata,
      questionId: createConversationDto.message.questionId,
    };

    // For image and file types, keep content empty and ensure metadata contains URLs
    if (
      createConversationDto.message.messageType === 'image' ||
      createConversationDto.message.messageType === 'file'
    ) {
      messageData.content = '';
      if (!messageData.metadata) {
        messageData.metadata = {};
      }

      // If it's a resume file, trigger AI insights generation
      if (jobApplicationId && this.isResumeUrl(messageData.metadata.url)) {
        this.aiInsightsService.generateProfessionalProfile(
          jobApplicationId,
          messageData.metadata.url,
        );
      }
    } else {
      messageData.content = createConversationDto.message.content;
    }

    if (createConversationDto.message.senderId) {
      // Check if sender is a candidate
      const senderCandidate = await this.candidateRepository.findById(
        createConversationDto.message.senderId,
      );
      if (senderCandidate) {
        messageData.senderId = createConversationDto.message.senderId;
      }
    }

    if (createConversationDto.message.receiverId) {
      // Check if receiver is a candidate
      const receiverCandidate = await this.candidateRepository.findById(
        createConversationDto.message.receiverId,
      );
      if (receiverCandidate) {
        messageData.receiverId = createConversationDto.message.receiverId;
      }
    }

    // Create the message
    await this.messageRepository.create(messageData);

    return {
      id: conversation.id,
      candidateId: conversation.candidateId,
      status: conversation.status,
      startedAt: conversation.startedAt,
      endedAt: conversation.endedAt,
      isNewConversation,
      candidate: candidate
        ? {
            id: candidate.id,
            name: candidate.name,
            email: candidate.email,
          }
        : undefined,
    };
  }

  private isResumeUrl(url: string): boolean {
    const resumeExtensions = ['.pdf', '.doc', '.docx'];
    return resumeExtensions.some((ext) => url.toLowerCase().endsWith(ext));
  }

  /**
   * Get the last active conversation for a candidate
   * @param candidateId - The ID of the candidate
   * @returns The last active conversation or null if no conversation is found
   */
  async getLastActiveConversation(
    candidateId: string,
  ): Promise<ActiveConversationResponseDto | null> {
    // Check if candidate exists
    const candidate = await this.candidateRepository.findById(candidateId);
    if (!candidate) {
      throw new NotFoundException(CANDIDATE_MESSAGES.CANDIDATE_NOT_FOUND);
    }

    // Get the last active conversation
    const conversation =
      await this.conversationRepository.findLastActiveConversation(candidateId);

    if (!conversation) {
      return null;
    }

    // Get all messages for this conversation in order
    const messages = await this.messageRepository.findByConversationId(
      conversation.id,
    );

    return {
      id: conversation.id,
      candidateId: conversation.candidateId,
      status: conversation.status,
      startedAt: conversation.startedAt,
      candidate: {
        id: candidate.id,
        name: candidate.name,
        email: candidate.email,
      },
      messages: messages.map((message) => ({
        id: message.id,
        content: message.content,
        messageType: message.messageType,
        metadata: message.metadata,
        sentAt: message.sentAt,
        sender: message.sender
          ? {
              id: message.sender.id,
              name: message.sender.name,
            }
          : undefined,
        receiver: message.receiver
          ? {
              id: message.receiver.id,
              name: message.receiver.name,
            }
          : undefined,
      })),
    };
  }

  async getConversationById(id: string): Promise<ConversationResponseDto> {
    const conversation = await this.conversationRepositoryTypeorm.findOne({
      where: { conversationId: id },
      relations: ['messages', 'messages.sender', 'messages.receiver'],
      order: {
        messages: {
          createdAt: 'ASC',
        },
      },
    });

    if (!conversation) {
      throw new NotFoundException(`Conversation with ID "${id}" not found`);
    }

    // Get candidate details if available
    let candidateDetails;
    if (conversation.candidateId) {
      const candidate = await this.candidateRepository.findById(
        conversation.candidateId,
      );
      if (candidate) {
        candidateDetails = {
          id: candidate.id,
          name: candidate.name,
          email: candidate.email,
        };
      }
    }

    return {
      id: conversation.id,
      candidateId: conversation.candidateId,
      status: conversation.status,
      startedAt: conversation.startedAt,
      endedAt: conversation.endedAt,
      isNewConversation: false,
      candidate: candidateDetails,
      messages: conversation.messages.map((message) => ({
        id: message.id,
        content: message.content,
        messageType: message.messageType,
        metadata: message.metadata,
        sentAt: message.sentAt,
        senderId: message.senderId,
        receiverId: message.receiverId,
        sender: message.sender
          ? {
              id: message.sender.id,
              name: message.sender.name,
            }
          : undefined,
        receiver: message.receiver
          ? {
              id: message.receiver.id,
              name: message.receiver.name,
            }
          : undefined,
      })),
    };
  }
}
