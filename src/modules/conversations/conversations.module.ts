import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConversationsController } from './conversations.controller';
import { ConversationsService } from './conversations.service';
import { Conversation } from '../../models/conversations/entities/conversation.entity';
import { Message } from '../../models/conversations/entities/message.entity';
import { ConversationRepository } from '../../models/conversations/repositories/conversation.repository';
import { MessageRepository } from '../../models/conversations/repositories/message.repository';
import { CandidatesModule } from '../candidates/candidates.module';
import { JobApplicationsModule } from '../jobs/job-applications/job-applications.module';
import { AiJobPostModule } from '../jobs/ai-job-post/ai-job-post.module';
import { JobPostsModule } from '../jobs/job-posts/job-posts.module';
import { RolesModule } from '../roles-permissions/roles.module';
import { AiInsightsModule } from '../jobs/ai-insights/ai-insights.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Conversation, Message]),
    CandidatesModule,
    JobApplicationsModule,
    AiJobPostModule,
    JobPostsModule,
    RolesModule,
    AiInsightsModule,
  ],
  controllers: [ConversationsController],
  providers: [ConversationsService, ConversationRepository, MessageRepository],
  exports: [ConversationsService],
})
export class ConversationsModule {}
