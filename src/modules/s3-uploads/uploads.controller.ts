import {
  Controller,
  Post,
  Body,
  UseGuards,
  BadRequestException,
  Get,
  Query,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiProperty,
} from '@nestjs/swagger';
import {
  S3Service,
  PreSignedUrlOptions,
  GetPreSignedUrlOptions,
} from '../../common/providers/aws/s3.service';
import { ThrottlerGuard } from '@nestjs/throttler';
import { IsNotEmpty, IsString, IsOptional, IsNumber } from 'class-validator';
import { Transform } from 'class-transformer';

export class GeneratePreSignedUrlDto {
  @ApiProperty({
    description: 'Name of the file to be uploaded',
    example: 'resume.pdf',
  })
  @IsNotEmpty()
  @IsString()
  fileName: string;

  @ApiProperty({
    description: 'Content type of the file',
    example: 'application/pdf',
  })
  @IsNotEmpty()
  @IsString()
  contentType: string;

  @ApiProperty({
    description: 'Folder path in S3 bucket',
    example: 'candidates/resumes',
  })
  @IsNotEmpty()
  @IsString()
  folder: string;
}

export class GetPreSignedUrlDto {
  @ApiProperty({
    description: 'File key in S3 bucket',
    example: 'candidates/resumes/**********-resume.pdf',
  })
  @IsNotEmpty()
  @IsString()
  fileKey: string;

  @ApiProperty({
    description: 'URL expiration time in seconds (default: 3600)',
    example: 7200,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  expiresIn?: number;
}

export class PreSignedUrlResponseDto {
  @ApiProperty({
    description: 'Pre-signed URL for file upload',
    example: 'https://bucket.s3.region.amazonaws.com/path/to/file?signature...',
  })
  preSignedUrl: string;

  @ApiProperty({
    description: 'File key in S3 bucket',
    example: 'candidates/resumes/**********-resume.pdf',
  })
  fileKey: string;
}

export class GetPreSignedUrlResponseDto {
  @ApiProperty({
    description: 'Pre-signed URL for file access',
    example: 'https://bucket.s3.region.amazonaws.com/path/to/file?signature...',
  })
  preSignedUrl: string;
}

@ApiTags('Uploads')
@Controller('uploads')
@UseGuards(ThrottlerGuard)
export class UploadsController {
  constructor(private readonly s3Service: S3Service) {}

  @Post('presigned-url')
  @ApiOperation({ summary: 'Generate a pre-signed URL for file upload' })
  @ApiResponse({
    status: 201,
    description: 'Pre-signed URL generated successfully',
    type: PreSignedUrlResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests',
  })
  async generatePreSignedUrl(
    @Body() generatePreSignedUrlDto: GeneratePreSignedUrlDto,
  ): Promise<PreSignedUrlResponseDto> {
    try {
      const { fileName, contentType, folder } = generatePreSignedUrlDto;
      console.log('fileName :', fileName);

      // Validate content type for resumes
      if (folder.includes('resumes')) {
        const allowedTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];
        if (!allowedTypes.includes(contentType)) {
          throw new BadRequestException(
            'Invalid file type. Only PDF and Word documents are allowed for resumes.',
          );
        }
      }

      const options: PreSignedUrlOptions = {
        fileName,
        contentType,
        folder,
        expiresIn: 3600, // 1 hour
      };
      console.log('options :', options);

      return await this.s3Service.generatePreSignedUrl(options);
    } catch (error) {
      console.log('error :', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to generate pre-signed URL');
    }
  }

  @Get('presigned-url')
  @ApiOperation({ summary: 'Generate a pre-signed URL for file access' })
  @ApiResponse({
    status: 200,
    description: 'Pre-signed URL generated successfully',
    type: GetPreSignedUrlResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests',
  })
  async getPreSignedUrl(
    @Query() getPreSignedUrlDto: GetPreSignedUrlDto,
  ): Promise<GetPreSignedUrlResponseDto> {
    try {
      const { fileKey, expiresIn } = getPreSignedUrlDto;

      const options: GetPreSignedUrlOptions = {
        fileKey,
        expiresIn,
      };

      const preSignedUrl =
        await this.s3Service.generateGetPreSignedUrl(options);

      return { preSignedUrl };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to generate pre-signed URL');
    }
  }
}
