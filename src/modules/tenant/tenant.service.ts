import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';
import { OrganisationStatus } from '@hirenetix/models/organisations/constants/organisation.constants';

@Injectable()
export class TenantService {
  constructor(
    @InjectRepository(Organisation)
    private readonly organisationRepository: Repository<Organisation>,
  ) {}

  /**
   * Get tenant details by tenant ID
   */
  async getTenantById(tenantId: string): Promise<Organisation | null> {
    if (!tenantId) return null;

    return this.organisationRepository.findOne({
      where: { id: tenantId },
    });
  }

  /**
   * Validate if the tenant exists and is active
   */
  async validateTenant(tenantId: string): Promise<boolean> {
    if (!tenantId) return false;

    const tenant = await this.organisationRepository.findOne({
      where: {
        id: tenantId,
        status: OrganisationStatus.ACTIVE,
      },
    });

    return !!tenant;
  }
}
