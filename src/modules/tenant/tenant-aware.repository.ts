import { Repository, SelectQueryBuilder } from 'typeorm';
import { TenantContextStorage } from './tenant-context';

/**
 * Base repository class for tenant-aware entities
 * Extends this class to ensure tenant filtering is applied consistently
 */
export abstract class TenantAwareRepository<Entity> {
  protected abstract readonly repository: Repository<Entity>;

  /**
   * Creates a query builder that automatically applies tenant filtering
   */
  protected createQueryBuilder(alias: string): SelectQueryBuilder<Entity> {
    const queryBuilder = this.repository.createQueryBuilder(alias);
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (tenantId) {
      queryBuilder.andWhere(`${alias}.tenantId = :tenantId`, { tenantId });
    }

    return queryBuilder;
  }

  /**
   * Find with automatic tenant filtering
   */
  async find(options: any = {}): Promise<Entity[]> {
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (tenantId) {
      // Add tenant filter to where clause
      if (!options.where) {
        options.where = { tenantId };
      } else if (Array.isArray(options.where)) {
        options.where = options.where.map((where: any) => ({
          ...where,
          tenantId,
        }));
      } else {
        options.where.tenantId = tenantId;
      }
    }

    return this.repository.find(options);
  }

  /**
   * Find one with automatic tenant filtering
   */
  async findOne(options: any = {}): Promise<Entity | null> {
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (tenantId) {
      // Add tenant filter to where clause
      if (!options.where) {
        options.where = { tenantId };
      } else if (Array.isArray(options.where)) {
        options.where = options.where.map((where: any) => ({
          ...where,
          tenantId,
        }));
      } else {
        options.where.tenantId = tenantId;
      }
    }

    return this.repository.findOne(options);
  }

  /**
   * Find one with automatic tenant filtering
   */
  async findOneWithoutTenant(options: any = {}): Promise<Entity | null> {
    if (Array.isArray(options.where)) {
      options.where = options.where.map((where: any) => ({
        ...where,
      }));
    }
    return this.repository.findOne(options);
  }

  /**
   * Count with automatic tenant filtering
   */
  async count(options: any = {}): Promise<number> {
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (tenantId) {
      // Add tenant filter to where clause
      if (!options.where) {
        options.where = { tenantId };
      } else if (Array.isArray(options.where)) {
        options.where = options.where.map((where: any) => ({
          ...where,
          tenantId,
        }));
      } else {
        options.where.tenantId = tenantId;
      }
    }

    return this.repository.count(options);
  }
}
