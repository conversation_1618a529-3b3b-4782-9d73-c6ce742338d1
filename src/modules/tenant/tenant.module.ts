import { Module } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';
import { User } from '@hirenetix/models/users/entities/user.entity';
import { TenantService } from './tenant.service';
import { TenantInterceptor } from './tenant.interceptor';
import { TenantSubscriber } from './tenant.subscriber';

@Module({
  imports: [TypeOrmModule.forFeature([Organisation, User])],
  providers: [
    TenantService,
    TenantSubscriber,
    {
      provide: APP_INTERCEPTOR,
      useClass: TenantInterceptor,
    },
  ],
  exports: [TenantService],
})
export class TenantModule {}
