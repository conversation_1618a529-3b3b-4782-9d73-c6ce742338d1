import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  CallH<PERSON>ler,
  UnauthorizedException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { TenantContextStorage } from './tenant-context';
import { TenantService } from './tenant.service';
import { SystemRoles } from '../../models/roles/constants/role.constants';
import { ORGANISATION_MESSAGES } from '../../common/constants/message.constants';
@Injectable()
export class TenantInterceptor implements NestInterceptor {
  constructor(private readonly tenantService: TenantService) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();

    // Skip tenant context for public routes or auth routes
    if (
      request.path.startsWith('/auth') ||
      request.path.startsWith('/public')
    ) {
      return next.handle();
    }

    const user = request.user;

    // If no user (not authenticated), continue without tenant context
    if (!user) {
      return next.handle();
    }

    // If user is super admin, continue without tenant context
    if (user?.role?.[0]?.name === SystemRoles.SUPER_ADMIN) {
      return next.handle();
    }

    const tenantId = user.tenantId;
    const userId = user.id;

    // If authenticated but no tenant ID, throw error for secured routes
    if (!tenantId && !request.path.startsWith('/admin')) {
      throw new UnauthorizedException('No tenant context available');
    }

    // Validate tenant if one is provided
    if (tenantId) {
      const isValidTenant = await this.tenantService.validateTenant(tenantId);
      if (!isValidTenant) {
        throw new UnauthorizedException(ORGANISATION_MESSAGES.TENANT_NOT_FOUND);
      }
    }

    // Create tenant context
    return new Observable((subscriber) => {
      TenantContextStorage.run({ tenantId, userId }, () => {
        const result$ = next.handle();
        result$.subscribe(subscriber);
      });
    });
  }
}
