import {
  DataSource,
  EntitySubscriberInterface,
  InsertEvent,
  UpdateEvent,
  FindOptionsWhere,
  EventSubscriber,
  LoadEvent,
} from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { TenantContextStorage } from './tenant-context';

// List of entities that should be tenant-aware
const TENANT_ENTITIES = ['User', 'Department'];

// List of entities that should NOT be tenant-aware
const NON_TENANT_ENTITIES = [
  'Organisation', // Organisation itself is not tenant-aware
  'Role', // Roles are global
];

@Injectable()
@EventSubscriber()
export class TenantSubscriber implements EntitySubscriberInterface {
  constructor(@InjectDataSource() readonly dataSource: DataSource) {
    dataSource.subscribers.push(this);
  }

  /**
   * Return all entities we are listening to
   */
  listenTo() {
    return Object; // Listen to all entities, we'll filter in the handlers
  }

  /**
   * Check if this is a tenant-aware entity
   */
  private isTenantEntity(entityName: string): boolean {
    return (
      TENANT_ENTITIES.includes(entityName) &&
      !NON_TENANT_ENTITIES.includes(entityName)
    );
  }

  /**
   * Called before entity insertion
   */
  beforeInsert(event: InsertEvent<any>): void {
    const entityName = event.metadata.targetName;

    if (this.isTenantEntity(entityName)) {
      const tenantId = TenantContextStorage.getCurrentTenantId();

      if (tenantId && !event.entity.tenantId) {
        event.entity.tenantId = tenantId;
      }
    }
  }

  /**
   * Called before entity update
   */
  beforeUpdate(event: UpdateEvent<any>): void {
    const entityName = event.metadata.targetName;

    if (this.isTenantEntity(entityName) && event.entity) {
      // Prevent changing tenantId
      if (event.entity.tenantId !== undefined) {
        const currentTenantId = TenantContextStorage.getCurrentTenantId();

        // Only allow tenantId to be set to the current tenant context
        if (event.entity.tenantId !== currentTenantId) {
          event.entity.tenantId = currentTenantId;
        }
      }
    }
  }

  /**
   * Called before entity find
   */
  beforeFind(event: any): void {
    const entityName = event.metadata.targetName;

    if (this.isTenantEntity(entityName)) {
      const tenantId = TenantContextStorage.getCurrentTenantId();
      console.log('tenantId :', tenantId);

      if (tenantId) {
        if (!event.criteria) {
          event.criteria = { where: { tenantId } };
        } else if (!event.criteria.where) {
          event.criteria.where = { tenantId };
        } else if (Array.isArray(event.criteria.where)) {
          event.criteria.where = event.criteria.where.map(
            (where: FindOptionsWhere<any>) => ({
              ...where,
              tenantId,
            }),
          );
        } else {
          event.criteria.where.tenantId = tenantId;
        }
      }
    }
  }

  /**
   * Called after entities are loaded from the database
   * This is our last chance to filter out entities from other tenants
   */
  afterLoad(entity: any, event?: LoadEvent<any>): void {
    if (!event) return;

    const entityName = event.metadata.targetName;

    if (this.isTenantEntity(entityName)) {
      const tenantId = TenantContextStorage.getCurrentTenantId();

      // If we have a tenant context and this entity belongs to another tenant, set it to null
      // This provides an additional security layer
      if (tenantId && entity.tenantId && entity.tenantId !== tenantId) {
      }
    }
  }
}
