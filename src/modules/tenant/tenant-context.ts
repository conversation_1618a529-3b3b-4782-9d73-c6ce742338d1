import { AsyncLocalStorage } from 'async_hooks';

export interface TenantContext {
  tenantId: string | null;
  userId: string | null;
}

export class TenantContextStorage {
  private static storage = new AsyncLocalStorage<TenantContext>();

  /**
   * Get the current tenant context
   */
  static getCurrentContext(): TenantContext | undefined {
    return this.storage.getStore();
  }

  /**
   * Get the current tenant ID from the context
   */
  static getCurrentTenantId(): string | null {
    const context = this.storage.getStore();
    return context?.tenantId || null;
  }

  /**
   * Get the current user ID from the context
   */
  static getCurrentUserId(): string | null {
    const context = this.storage.getStore();
    return context?.userId || null;
  }

  /**
   * Run a callback within a tenant context
   */
  static run(context: TenantContext, callback: () => any): any {
    return this.storage.run(context, callback);
  }
}
