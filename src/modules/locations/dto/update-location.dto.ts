import { PartialType } from '@nestjs/swagger';
import { CreateLocationDto } from './create-location.dto';
import { IsEnum, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { LocationStatus } from './location-search.dto';

export class UpdateLocationDto extends PartialType(CreateLocationDto) {
  @ApiProperty({
    description: 'Status of the location',
    enum: LocationStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(LocationStatus)
  status?: LocationStatus;
}
