import { ApiProperty } from '@nestjs/swagger';
import { LocationSerializer } from '@hirenetix/models/locations/serializers/location.serializer';

export class PaginatedLocationsResponseDto {
  @ApiProperty({ type: [LocationSerializer] })
  data: LocationSerializer[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  totalPages: number;

  @ApiProperty()
  hasNextPage: boolean;

  @ApiProperty()
  hasPreviousPage: boolean;
}
