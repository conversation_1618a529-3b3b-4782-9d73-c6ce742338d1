import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateLocationDto {
  @ApiProperty({ description: 'Name of the location' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'City where the location is situated' })
  @IsNotEmpty()
  @IsString()
  city: string;

  @ApiProperty({
    description: 'State/Province/Region of the location',
    required: false,
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty({ description: 'Country of the location', required: false })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiProperty({ description: 'Unique code for the location' })
  @IsNotEmpty()
  @IsString()
  locationCode: string;

  @ApiProperty({ description: 'Full formatted address from Google Places API' })
  @IsNotEmpty()
  @IsString()
  address: string;

  @ApiProperty({ description: 'Latitude of the location', required: false })
  @IsOptional()
  @IsNumber()
  latitude?: number;

  @ApiProperty({ description: 'Longitude of the location', required: false })
  @IsOptional()
  @IsNumber()
  longitude?: number;

  @ApiProperty({ description: 'Google Place ID', required: false })
  @IsOptional()
  @IsString()
  placeId?: string;
}
