import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { Location } from '@hirenetix/models/locations/entities/location.entity';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';
import { LocationSearchDto } from './dto/location-search.dto';
import { TenantContextStorage } from '../tenant/tenant-context';
import { LocationsRepository } from '@hirenetix/models/locations/repositories/locations.repository';
import { LOCATION_MESSAGES } from '../../common/constants/message.constants';
import { PaginatedLocationsResponseDto } from './dto/paginated-locations-response.dto';
import { LocationSerializer } from '@hirenetix/models/locations/serializers/location.serializer';
import { UsersRepository } from '../users/users.repository';
import { JobPostRepository } from '@hirenetix/models/jobs/repositories/job-post.repository';

@Injectable()
export class LocationsService {
  constructor(
    private readonly locationsRepository: LocationsRepository,
    private readonly usersRepository: UsersRepository,
    private readonly jobPostRepository: JobPostRepository,
  ) {}

  async create(createLocationDto: CreateLocationDto): Promise<Location> {
    const organisationId = TenantContextStorage.getCurrentTenantId();

    if (!organisationId) {
      throw new BadRequestException(LOCATION_MESSAGES.ORGANISATION_ID_REQUIRED);
    }

    return await this.locationsRepository.create({
      ...createLocationDto,
      tenantId: organisationId,
    });
  }

  async findAll(
    searchParams: LocationSearchDto,
  ): Promise<PaginatedLocationsResponseDto> {
    const { page = 1, limit = 10 } = searchParams;
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!tenantId) {
      throw new BadRequestException(LOCATION_MESSAGES.ORGANISATION_ID_REQUIRED);
    }

    const [locations, total] =
      await this.locationsRepository.findWithPagination(tenantId, searchParams);

    const totalPages = Math.ceil(total / limit);

    return {
      data: LocationSerializer.serializeMany(locations),
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async findOne(id: string): Promise<Location> {
    const organisationId = TenantContextStorage.getCurrentTenantId();

    if (!organisationId) {
      throw new BadRequestException(LOCATION_MESSAGES.ORGANISATION_ID_REQUIRED);
    }

    const location = await this.locationsRepository.findById(id);

    if (!location || location.tenantId !== organisationId) {
      throw new NotFoundException(LOCATION_MESSAGES.LOCATION_NOT_FOUND);
    }

    return location;
  }

  async remove(id: string): Promise<void> {
    const organisationId = TenantContextStorage.getCurrentTenantId();

    if (!organisationId) {
      throw new BadRequestException(LOCATION_MESSAGES.ORGANISATION_ID_REQUIRED);
    }

    const location = await this.locationsRepository.findById(id);

    if (!location || location.tenantId !== organisationId) {
      throw new NotFoundException(LOCATION_MESSAGES.LOCATION_NOT_FOUND);
    }

    //Check if the location is used in any job posts or associated with any users
    const user = await this.usersRepository.findByLocationId(id);

    //check if the location is used in any job posts
    const jobPost = await this.jobPostRepository.findByLocationId(id);

    if (user || jobPost) {
      throw new BadRequestException(LOCATION_MESSAGES.LOCATION_IN_USE);
    }

    await this.locationsRepository.delete(id);
  }

  async update(
    id: string,
    updateLocationDto: UpdateLocationDto,
  ): Promise<Location> {
    const organisationId = TenantContextStorage.getCurrentTenantId();

    if (!organisationId) {
      throw new BadRequestException(LOCATION_MESSAGES.ORGANISATION_ID_REQUIRED);
    }

    const existingLocation = await this.locationsRepository.findById(id);

    if (!existingLocation || existingLocation.tenantId !== organisationId) {
      throw new NotFoundException(LOCATION_MESSAGES.LOCATION_NOT_FOUND);
    }

    // Check if location code is being updated and if it already exists
    if (
      updateLocationDto.locationCode &&
      updateLocationDto.locationCode !== existingLocation.locationCode
    ) {
      const locationWithCode = await this.locationsRepository.findByCode(
        updateLocationDto.locationCode,
      );
      if (locationWithCode && locationWithCode.id !== id) {
        throw new ConflictException(LOCATION_MESSAGES.LOCATION_CODE_EXISTS);
      }
    }

    return await this.locationsRepository.update(id, updateLocationDto);
  }

  async findByTenantId(tenantId: string): Promise<Location[]> {
    if (!tenantId) {
      throw new BadRequestException(LOCATION_MESSAGES.ORGANISATION_ID_REQUIRED);
    }

    return await this.locationsRepository.findByTenantId(tenantId);
  }
}
