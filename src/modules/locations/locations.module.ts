import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LocationsService } from './locations.service';
import { LocationsController } from './locations.controller';
import { PublicLocationsController } from './public-locations.controller';
import { Location } from '@hirenetix/models/locations/entities/location.entity';
import { LocationsRepository } from '@hirenetix/models/locations/repositories/locations.repository';
import { RolesModule } from '@hirenetix/modules/roles-permissions/roles.module';
import { UsersModule } from '@hirenetix/modules/users/users.module';
import { JobPost } from '@hirenetix/models/jobs/entities/job-post.entity';
import { JobPostRepository } from '@hirenetix/models/jobs/repositories/job-post.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([Location, JobPost]),
    RolesModule,
    forwardRef(() => UsersModule),
  ],
  controllers: [LocationsController, PublicLocationsController],
  providers: [LocationsService, LocationsRepository, JobPostRepository],
  exports: [LocationsService, LocationsRepository],
})
export class LocationsModule {}
