import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  HttpStatus,
  Put,
  Query,
} from '@nestjs/common';
import { LocationsService } from './locations.service';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';
import { LocationSearchDto, LocationStatus } from './dto/location-search.dto';
import { PaginatedLocationsResponseDto } from './dto/paginated-locations-response.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { LocationSerializer } from '../../models/locations/serializers/location.serializer';
import { LOCATION_MESSAGES } from '../../common/constants/message.constants';
import { RequirePermissions } from '../../common/decorators/metadata/permissions.decorator';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import {
  PermissionActions,
  PermissionModules,
} from '../../models/roles/constants/role.constants';
import { JwtAuthGuard } from '../../authentication/jwt-auth.guard';
import { JWT_AUTH_NAME } from '../../common/constants/general.constants';

@ApiTags('Locations')
@ApiBearerAuth(JWT_AUTH_NAME)
@Controller('locations')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class LocationsController {
  constructor(private readonly locationsService: LocationsService) {}

  /**
   * Create a new location
   * @param createLocationDto
   * @returns LocationSerializer
   */
  @Post()
  @RequirePermissions({
    action: PermissionActions.CREATE_LOCATION,
    module: PermissionModules.LOCATIONS,
  })
  @ApiOperation({ summary: 'Create a new location' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: LOCATION_MESSAGES.LOCATION_CREATED,
    type: LocationSerializer,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: LOCATION_MESSAGES.LOCATION_CODE_EXISTS,
  })
  async create(
    @Body() createLocationDto: CreateLocationDto,
  ): Promise<LocationSerializer> {
    const location = await this.locationsService.create(createLocationDto);
    return LocationSerializer.serialize(location);
  }

  /**
   * Get all locations for the current organisation with optional filters and pagination
   * @param searchParams Search, filter and pagination parameters
   * @returns PaginatedLocationsResponseDto
   */
  @Get()
  @RequirePermissions({
    action: PermissionActions.VIEW_ALL_LOCATIONS,
    module: PermissionModules.LOCATIONS,
  })
  @ApiOperation({ summary: 'Get all locations for the current organisation' })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search locations by name or city',
  })
  @ApiQuery({ name: 'status', required: false, enum: LocationStatus })
  @ApiQuery({ name: 'page', required: false, type: Number, minimum: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, minimum: 1 })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns paginated locations.',
    type: PaginatedLocationsResponseDto,
  })
  async findAll(
    @Query() searchParams: LocationSearchDto,
  ): Promise<PaginatedLocationsResponseDto> {
    return await this.locationsService.findAll(searchParams);
  }

  /**
   * Get a location by ID
   * @param id
   * @returns LocationSerializer
   */
  @Get(':id')
  @RequirePermissions({
    action: PermissionActions.VIEW_ALL_LOCATIONS,
    module: PermissionModules.LOCATIONS,
  })
  @ApiOperation({ summary: 'Get a location by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns the location.',
    type: LocationSerializer,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: LOCATION_MESSAGES.LOCATION_NOT_FOUND,
  })
  async findOne(@Param('id') id: string): Promise<LocationSerializer> {
    const location = await this.locationsService.findOne(id);
    return LocationSerializer.serialize(location);
  }

  /**
   * Update a location by ID
   * @param id
   * @param updateLocationDto
   * @returns LocationSerializer
   */
  @Put(':id')
  @RequirePermissions({
    action: PermissionActions.EDIT_LOCATION,
    module: PermissionModules.LOCATIONS,
  })
  @ApiOperation({ summary: 'Update a location' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: LOCATION_MESSAGES.LOCATION_UPDATED,
    type: LocationSerializer,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: LOCATION_MESSAGES.LOCATION_NOT_FOUND,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: LOCATION_MESSAGES.LOCATION_CODE_EXISTS,
  })
  async update(
    @Param('id') id: string,
    @Body() updateLocationDto: UpdateLocationDto,
  ): Promise<LocationSerializer> {
    const location = await this.locationsService.update(id, updateLocationDto);
    return LocationSerializer.serialize(location);
  }

  /**
   * Delete a location
   * @param id
   */
  @Delete(':id')
  @RequirePermissions({
    action: PermissionActions.DELETE_LOCATION,
    module: PermissionModules.LOCATIONS,
  })
  @ApiOperation({ summary: 'Delete a location' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: LOCATION_MESSAGES.LOCATION_DELETED,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: LOCATION_MESSAGES.LOCATION_NOT_FOUND,
  })
  async remove(@Param('id') id: string): Promise<void> {
    return await this.locationsService.remove(id);
  }
}
