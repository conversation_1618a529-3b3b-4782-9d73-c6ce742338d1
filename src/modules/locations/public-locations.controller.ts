import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { LocationsService } from './locations.service';
import { PublicLocationSerializer } from '../../models/locations/serializers/public-location.serializer';
import { LOCATION_MESSAGES } from '../../common/constants/message.constants';
import { ThrottlerGuard } from '@nestjs/throttler';

@ApiTags('Public Locations')
@Controller('public/locations')
@UseGuards(ThrottlerGuard)
export class PublicLocationsController {
  constructor(private readonly locationsService: LocationsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all locations for a specific organization' })
  @ApiQuery({
    name: 'tenantId',
    required: true,
    description: 'ID of the organization/tenant',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all active locations for the organization.',
    type: [PublicLocationSerializer],
  })
  @ApiResponse({
    status: 400,
    description: LOCATION_MESSAGES.ORGANISATION_ID_REQUIRED,
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests',
  })
  async getOrganizationLocations(
    @Query('tenantId') tenantId: string,
  ): Promise<PublicLocationSerializer[]> {
    const locations = await this.locationsService.findByTenantId(tenantId);
    return PublicLocationSerializer.serializeMany(locations);
  }
}
