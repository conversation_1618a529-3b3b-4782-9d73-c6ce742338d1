import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from '@hirenetix/models/users/entities/user.entity';
import {
  IUserCreate,
  IUserUpdate,
  IUserSearchQuery,
} from '@hirenetix/models/users/interfaces/user.interface';
import {
  SORT_FIELD,
  SORT_ORDER,
  UserStatus,
} from '@hirenetix/models/users/constants';
import { TenantAwareRepository } from '@hirenetix/modules/tenant/tenant-aware.repository';
import { PasswordHelper } from '@hirenetix/common/helpers/password.helper';
import { In } from 'typeorm';

@Injectable()
export class UsersRepository extends TenantAwareRepository<User> {
  constructor(
    @InjectRepository(User)
    protected readonly repository: Repository<User>,
  ) {
    super();
  }

  async findById(id: string): Promise<User | null> {
    return this.findOne({
      where: { id },
      relations: [
        'userRoleAssignments',
        'userRoleAssignments.role',
        'location',
      ],
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.findOneWithoutTenant({ where: { email } });
  }

  async findByPhone(phone: string): Promise<User | null> {
    return this.findOneWithoutTenant({ where: { phone } });
  }

  async findWithCount(query: IUserSearchQuery): Promise<[User[], number]> {
    const queryBuilder = this.createQueryBuilder('user');

    if (query.id) {
      queryBuilder.andWhere('user.id = :id', { id: query.id });
    }

    if (query.fullName) {
      queryBuilder.andWhere('user.full_name ILIKE :firstName', {
        fullName: `%${query.fullName}%`,
      });
    }

    if (query.email) {
      queryBuilder.andWhere('user.email ILIKE :email', {
        email: `%${query.email}%`,
      });
    }

    if (query.phone) {
      queryBuilder.andWhere('user.phone LIKE :phone', {
        phone: `%${query.phone}%`,
      });
    }

    // Allow explicit tenantId override if specified in query (for admin purposes)
    if (query.tenantId) {
      queryBuilder.andWhere('user.tenantId = :queryTenantId', {
        queryTenantId: query.tenantId,
      });
    }

    if (query.departmentId) {
      queryBuilder.andWhere('user.departmentId = :departmentId', {
        departmentId: query.departmentId,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('user.status = :status', { status: query.status });
    }

    if (query.city) {
      queryBuilder.andWhere('user.city ILIKE :city', {
        city: `%${query.city}%`,
      });
    }

    if (query.country) {
      queryBuilder.andWhere('user.country ILIKE :country', {
        country: `%${query.country}%`,
      });
    }

    if (query.jobTitle) {
      queryBuilder.andWhere('user.jobTitle ILIKE :jobTitle', {
        jobTitle: `%${query.jobTitle}%`,
      });
    }

    return queryBuilder.getManyAndCount();
  }

  async create(data: IUserCreate): Promise<User> {
    const user = new User();

    user.fullName = data.fullName;
    user.email = data.email;

    if (data.phone) {
      user.phone = data.phone;
    }

    if (data.tenantId) {
      user.tenantId = data.tenantId;
    }

    if (data.timeZone) {
      user.timeZone = data.timeZone;
    }

    if (data.locationId) {
      user.locationId = data.locationId;
    }

    user.status = UserStatus.ACTIVE;

    return this.repository.save(user);
  }

  async update(id: string, data: IUserUpdate): Promise<User | null> {
    const user = await this.findById(id);

    if (!user) {
      return null;
    }

    // Use repository.update instead of save to preserve fields not being updated
    await this.repository.update(id, data);

    // Fetch and return the updated user
    return this.findById(id);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected > 0;
  }

  async verifyPassword(user: User, password: string): Promise<boolean> {
    return PasswordHelper.comparePasswords(password, user.passwordHash);
  }

  /**
   * Set a new password for a user
   * @param userId - The ID of the user
   * @param password - The new plain text password
   * @returns The updated user
   */
  async setPassword(userId: string, password: string): Promise<User | null> {
    const user = await this.findById(userId);

    if (!user) {
      return null;
    }

    const passwordHash = await PasswordHelper.hashPassword(password);

    this.repository.update(user.id, {
      passwordHash,
      isPasswordResetRequired: false,
    });

    return await this.findById(userId);
  }

  /**
   * Set a password reset token for a user
   * @param email - The email of the user
   * @param token - The reset token to set
   * @param expires - The expiration date of the token
   * @returns The updated user or null if not found
   */
  async setPasswordResetToken(
    email: string,
    token: string,
    expires: Date,
  ): Promise<User | null> {
    const user = await this.findByEmail(email);

    if (!user) {
      return null;
    }

    await this.repository.update(user.id, {
      passwordResetToken: token,
      passwordResetExpires: expires,
    });

    return this.findByEmail(email);
  }

  /**
   * Find a user by their password reset token if it's valid
   * @param token - The reset token to check
   * @returns The user with the valid token or null
   */
  async findByResetToken(token: string): Promise<User | null> {
    // Find user with matching token that hasn't expired
    return this.findOne({
      where: {
        passwordResetToken: token,
      },
    });
  }

  /**
   * Clear the password reset token fields for a user
   * @param userId - The ID of the user
   */
  async clearPasswordResetToken(userId: string): Promise<void> {
    await this.repository.update(userId, {
      passwordResetToken: null,
      passwordResetExpires: null,
    });
  }

  /**
   * Search users with advanced filtering, sorting and pagination
   * @param options Search, filter, sort and pagination options
   * @returns Array of users and total count
   */
  async searchUsers(options: {
    tenantId: string;
    skip?: number;
    take?: number;
    search?: string;
    roles?: string;
    status?: UserStatus;
    startDate?: string;
    endDate?: string;
    sortColumn?: SORT_FIELD;
    sortOrderBy?: SORT_ORDER;
  }): Promise<[User[], number]> {
    const {
      tenantId,
      skip = 0,
      take = 10,
      search,
      roles,
      status,
      startDate,
      endDate,
      sortColumn = SORT_FIELD.CREATED_AT,
      sortOrderBy = SORT_ORDER.DESC,
    } = options;

    // First handle the count with a simple query that doesn't need ordering
    const countQuery = this.repository
      .createQueryBuilder('user')
      .where('user.tenantId = :tenantId', { tenantId });

    // Apply search on fullName, email, phone, or role name if provided for count
    if (search && search.trim() !== '') {
      const searchTerm = `%${search.trim()}%`;
      countQuery
        .leftJoin('user.userRoleAssignments', 'ura')
        .leftJoin('ura.role', 'role')
        .andWhere(
          '(user.full_name ILIKE :search OR ' +
            'user.email ILIKE :search OR ' +
            'user.phone ILIKE :search OR ' +
            'role.name ILIKE :search)',
          { search: searchTerm },
        );
    }

    // Filter by status if provided for count
    if (status) {
      countQuery.andWhere('user.status = :status', { status });
    }

    // Filter by date range if provided for count
    if (startDate) {
      countQuery.andWhere('user.created_at >= :startDate', { startDate });
    }

    if (endDate) {
      countQuery.andWhere('user.created_at <= :endDate', { endDate });
    }

    // For role filtering in count query
    if (roles && roles.trim() !== '') {
      const roleNames = roles.split(',').map((r) => r.trim());
      if (roleNames.length > 0) {
        countQuery.innerJoin(
          'user_role_assignments',
          'ura',
          'ura.user_id = user.id',
        );
        countQuery.innerJoin(
          'roles',
          'role',
          'role.id = ura.role_id AND role.name IN (:...roleNames)',
          { roleNames },
        );
      }
    }

    // Get count of distinct users
    const totalCount = await countQuery
      .select('COUNT(DISTINCT user.id)', 'count')
      .getRawOne()
      .then((result) => parseInt(result?.count || '0', 10));

    // If no users match, return empty array early
    if (totalCount === 0) {
      return [[], 0];
    }

    // Now build the query to get the actual users with pagination, avoiding DISTINCT with ORDER BY
    // Approach: Get user IDs first with a subquery, then get full user data

    // Step 1: Get the user IDs without DISTINCT but with a separate GROUP BY
    const userIdsQuery = this.repository
      .createQueryBuilder('user')
      .select('user.id');

    // Add sort column to select list
    if (sortColumn === SORT_FIELD.FULL_NAME) {
      userIdsQuery.addSelect('user.full_name');
    } else if (sortColumn === SORT_FIELD.STATUS) {
      userIdsQuery.addSelect('user.status');
    } else if (sortColumn === SORT_FIELD.CREATED_AT) {
      userIdsQuery.addSelect('user.created_at');
    }

    userIdsQuery.where('user.tenantId = :tenantId', { tenantId });

    // Apply search on fullName, email, phone, or role name
    if (search && search.trim() !== '') {
      const searchTerm = `%${search.trim()}%`;
      userIdsQuery
        .leftJoin('user.userRoleAssignments', 'ura')
        .leftJoin('ura.role', 'role')
        .andWhere(
          '(user.full_name ILIKE :search OR ' +
            'user.email ILIKE :search OR ' +
            'user.phone ILIKE :search OR ' +
            'role.name ILIKE :search)',
          { search: searchTerm },
        );
    }

    // Filter by status if provided for count
    if (status) {
      userIdsQuery.andWhere('user.status = :status', { status });
    }

    // Filter by date range if provided for count
    if (startDate) {
      userIdsQuery.andWhere('user.created_at >= :startDate', { startDate });
    }

    if (endDate) {
      userIdsQuery.andWhere('user.created_at <= :endDate', { endDate });
    }

    // For role filtering
    if (roles && roles.trim() !== '') {
      const roleNames = roles.split(',').map((r) => r.trim());
      if (roleNames.length > 0) {
        userIdsQuery.innerJoin(
          'user_role_assignments',
          'ura',
          'ura.user_id = user.id',
        );
        userIdsQuery.innerJoin(
          'roles',
          'role',
          'role.id = ura.role_id AND role.name IN (:...roleNames)',
          { roleNames },
        );
      }
    }

    // Use GROUP BY instead of DISTINCT to avoid ORDER BY issues
    userIdsQuery.groupBy('user.id');

    // Add sort column to GROUP BY
    if (sortColumn === SORT_FIELD.FULL_NAME) {
      userIdsQuery.addGroupBy('user.full_name');
    } else if (sortColumn === SORT_FIELD.STATUS) {
      userIdsQuery.addGroupBy('user.status');
    } else if (sortColumn === SORT_FIELD.CREATED_AT) {
      userIdsQuery.addGroupBy('user.created_at');
    }

    // Apply sorting
    if (sortColumn === SORT_FIELD.FULL_NAME) {
      userIdsQuery.orderBy('user.full_name', sortOrderBy);
    } else if (sortColumn === SORT_FIELD.STATUS) {
      userIdsQuery.orderBy('user.status', sortOrderBy);
    } else if (sortColumn === SORT_FIELD.CREATED_AT) {
      userIdsQuery.orderBy('user.created_at', sortOrderBy);
    } else {
      userIdsQuery.orderBy('user.full_name', sortOrderBy);
    }

    // Apply pagination
    userIdsQuery.offset(skip).limit(take);

    // Get the user IDs
    const userIdsResult = await userIdsQuery.getRawMany();
    const userIds = userIdsResult.map((result) => result.user_id);

    // If no user IDs after filtering and pagination, return empty array
    if (!userIds.length) {
      return [[], totalCount];
    }

    // Step 2: Get the full user data with roles and location
    const users = await this.repository.find({
      where: { id: In(userIds) },
      relations: [
        'userRoleAssignments',
        'userRoleAssignments.role',
        'location',
      ],
      order: {
        [sortColumn === SORT_FIELD.FULL_NAME
          ? 'fullName'
          : sortColumn === SORT_FIELD.STATUS
            ? 'status'
            : sortColumn === SORT_FIELD.CREATED_AT
              ? 'createdAt'
              : 'fullName']: sortOrderBy,
      },
    });

    return [users, totalCount];
  }

  async findByLocationId(locationId: string): Promise<User | null> {
    return this.findOne({
      where: { locationId },
    });
  }
}
