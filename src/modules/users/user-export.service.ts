import { Injectable } from '@nestjs/common';
import { stringify } from 'csv-stringify/sync';
import * as XLSX from 'xlsx';
import { UserSerializer } from '@hirenetix/models/users/serializers/user.serializer';
import { ExportFormat } from '@hirenetix/models/users/constants/user.constants';
import { TenantContextStorage } from '../tenant/tenant-context';
import * as moment from 'moment';
import { OrganisationsRepository } from '../organisations/organisations.repository';

@Injectable()
export class UserExportService {
  constructor(
    private readonly organisationsRepository: OrganisationsRepository,
  ) {}
  /**
   * Convert user data to CSV or XLSX buffer
   * @param users Array of serialized users
   * @param format Export format (csv or xlsx)
   * @returns Buffer containing the exported data
   */
  async exportUsers(
    users: UserSerializer[],
    format: ExportFormat,
  ): Promise<{ buffer: Buffer; fileName: string; contentType: string }> {
    // Extract the data we want to export and format it for export
    const data = users.map((user) => {
      const roles = user.roleDetails
        ? user.roleDetails.map((role) => role.name).join(', ')
        : '';

      return {
        'Full Name': user.fullName,
        Email: user.email,
        Phone: user.phone || '',
        Status: user.status.replace(/^./, (string) => string[0].toUpperCase()),
        'Created Date': moment(user.createdAt).format('YYYY-MM-DD'),
        Role: roles,
        'Time Zone': user.timeZone,
      };
    });

    const tenantId = TenantContextStorage.getCurrentTenantId();
    const organisation = await this.organisationsRepository.findById(tenantId);
    const brandName = organisation.name.replace(/ /g, '_');
    const fileName = `users_list_${brandName}_${moment().format('YYYY-MM-DD')}`;

    if (format === ExportFormat.CSV) {
      return this.exportToCsv(data, fileName);
    } else {
      return this.exportToXlsx(data, fileName);
    }
  }

  /**
   * Export data to CSV format
   * @param data Formatted data to export
   * @param fileName Base filename without extension
   * @returns Buffer containing CSV data with file information
   */
  private exportToCsv(
    data: Record<string, any>[],
    fileName: string,
  ): { buffer: Buffer; fileName: string; contentType: string } {
    const csvContent = stringify(data, {
      header: true,
      columns: Object.keys(data[0] || {}),
    });

    return {
      buffer: Buffer.from(csvContent),
      fileName: `${fileName}.csv`,
      contentType: 'text/csv',
    };
  }

  /**
   * Export data to XLSX format
   * @param data Formatted data to export
   * @param fileName Base filename without extension
   * @returns Buffer containing XLSX data with file information
   */
  private exportToXlsx(
    data: Record<string, any>[],
    fileName: string,
  ): { buffer: Buffer; fileName: string; contentType: string } {
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Users');

    const xlsxContent = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });

    return {
      buffer: xlsxContent,
      fileName: `${fileName}.xlsx`,
      contentType:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };
  }
}
