import {
  Controller,
  Post,
  Body,
  UseGuards,
  HttpStatus,
  HttpCode,
  Get,
  Query,
  Put,
  Param,
  Delete,
  Res,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '@hirenetix/authentication/jwt-auth.guard';
import {
  PermissionActions,
  PermissionModules,
} from '@hirenetix/models/roles/constants';
import { UserSerializer } from '@hirenetix/models/users/serializers/user.serializer';
import { AddOrgUserDto } from '@hirenetix/models/users/dto/add-org-user.dto';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiProduces,
} from '@nestjs/swagger';
import {
  API_OPERATIONS,
  API_RESPONSES,
  USER_MESSAGES,
  API_QUERY_PARAMETERS,
} from '@hirenetix/common/constants';
import { JWT_AUTH_NAME } from '@hirenetix/common/constants/general.constants';
import { UserSearchQueryDto } from '@hirenetix/models/users/dto/user-search-query.dto';
import { PaginatedUsersResultDto } from '@hirenetix/models/users/dto/paginated-users-result.dto';
import { UserStatus } from '@hirenetix/models/users/constants';
import {
  SORT_FIELD,
  SORT_ORDER,
  ExportFormat,
} from '@hirenetix/models/users/constants/user.constants';
import { EditUserDto } from '@hirenetix/models/users/dto/edit-user.dto';
import { Response } from 'express';
import { CurrentUser } from '@hirenetix/common/decorators/current-user.decorator';
import { User } from '@hirenetix/models/users/entities/user.entity';
import { CurrentUserSerializer } from '@hirenetix/models/users/serializers/current-user.serializer';
import { RequirePermissions } from '../../common/decorators/metadata/permissions.decorator';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * Get current user's details including organization, roles, and permissions
   */
  @Get('current-user')
  @ApiOperation({ summary: 'Get current user details' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Current user details retrieved successfully',
    type: CurrentUserSerializer,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'User not authenticated',
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard)
  async getCurrentUser(
    @CurrentUser() user: User,
  ): Promise<CurrentUserSerializer> {
    return this.usersService.getCurrentUser(user.id);
  }

  /**
   * Add a user to an organization
   * This API is protected and only users with ADMIN or SUPER_ADMIN roles can access it
   */
  @Post()
  @ApiOperation({ summary: API_OPERATIONS.ADD_USER_TO_ORGANIZATION })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: USER_MESSAGES.USER_ADDED_SUCCESSFULLY,
    type: UserSerializer,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: API_RESPONSES.EMAIL_OR_PHONE_NUMBER_ALREADY_REGISTERED,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: API_RESPONSES.INVALID_INPUT_DATA,
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions({
    action: PermissionActions.CREATE_USER,
    module: PermissionModules.USERS,
  })
  @HttpCode(HttpStatus.CREATED)
  async addUserToOrganization(
    @Body() addUserDto: AddOrgUserDto,
  ): Promise<UserSerializer> {
    return this.usersService.addUserToOrganization(addUserDto);
  }

  /**
   * Get all users for a specific tenant with pagination, filtering, search, and sorting
   * The response includes complete role details in the roleDetails property of each user
   * @param queryParams Parameters for pagination, filtering, search, and sorting
   * @returns Paginated array of users with metadata
   */
  @Get()
  @ApiOperation({
    summary:
      'Get all users for a specific tenant with pagination, filtering, search, and role details',
    description:
      'Returns users with their full role information including role IDs and names in the roleDetails property',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: API_QUERY_PARAMETERS.PAGE_NUMBER,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: API_QUERY_PARAMETERS.NUMBER_OF_ITEMS_PER_PAGE,
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description:
      'Search text that will be matched against user names, emails, and phone numbers',
    type: String,
  })
  @ApiQuery({
    name: 'roles',
    required: false,
    description: 'Filter users by specific role names (comma-separated)',
    type: String,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter users by status',
    enum: UserStatus,
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Filter users created after this date (ISO 8601 format)',
    type: String,
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'Filter users created before this date (ISO 8601 format)',
    type: String,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    description: 'Field to sort by',
    enum: SORT_FIELD,
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    description: 'Sort order (ascending or descending)',
    enum: SORT_ORDER,
  })
  @ApiQuery({
    name: 'export',
    required: false,
    description:
      'Flag to export the data instead of returning paginated results',
    type: Boolean,
  })
  @ApiQuery({
    name: 'exportFormat',
    required: false,
    description: 'Format for exporting data (csv or xlsx)',
    enum: ExportFormat,
  })
  @ApiResponse({
    status: 200,
    description: 'List of users for the tenant',
    type: PaginatedUsersResultDto,
  })
  @ApiProduces(
    'application/json',
    'text/csv',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @ApiResponse({
    status: 403,
    description: API_RESPONSES.FORBIDDEN_INSUFFICIENT_PERMISSIONS,
  })
  @ApiResponse({ status: 404, description: 'No users found' })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions({
    action: PermissionActions.VIEW_ALL_USERS,
    module: PermissionModules.USERS,
  })
  async getUsers(
    @Query() queryParams: UserSearchQueryDto,
    @Res() res: Response,
  ): Promise<void> {
    const result = await this.usersService.getUsers(queryParams);

    // Check if the result is a file export
    if ('buffer' in result) {
      res.setHeader('Content-Type', result.contentType);
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=${result.fileName}`,
      );

      res.send(result.buffer);
    } else {
      res.json(result);
    }
  }

  /**
   * Edit a user's details including name, role, phone, and timezone
   * This API is protected and only users with ADMIN or SUPER_ADMIN roles can access it
   */
  @Put(':id')
  @ApiOperation({ summary: "Edit a user's details" })
  @ApiParam({
    name: 'id',
    description: 'ID of the user to edit',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User updated successfully',
    type: UserSerializer,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Phone number already registered',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions({
    action: PermissionActions.EDIT_USER,
    module: PermissionModules.USERS,
  })
  async editUser(
    @Param('id') id: string,
    @Body() editUserDto: EditUserDto,
  ): Promise<UserSerializer> {
    return this.usersService.editUser(id, editUserDto);
  }

  /**
   * Get a specific user by ID
   * This API is protected and requires authentication
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get a specific user by ID' })
  @ApiParam({
    name: 'id',
    description: 'ID of the user to retrieve',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User retrieved successfully',
    type: UserSerializer,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid user ID format',
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions({
    action: PermissionActions.VIEW_ALL_USERS,
    module: PermissionModules.USERS,
  })
  async getUserById(@Param('id') id: string): Promise<UserSerializer> {
    return this.usersService.findById(id);
  }

  /**
   * Delete a user by ID
   * This API is protected and only users with ADMIN or SUPER_ADMIN roles can access it
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a user' })
  @ApiParam({
    name: 'id',
    description: 'ID of the user to delete',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'User deleted successfully' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Failed to delete user',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: API_RESPONSES.FORBIDDEN_INSUFFICIENT_PERMISSIONS,
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions({
    action: PermissionActions.DELETE_USER,
    module: PermissionModules.USERS,
  })
  @HttpCode(HttpStatus.OK)
  async deleteUser(
    @Param('id') id: string,
    @CurrentUser() currentUser: User,
  ): Promise<{ success: boolean; message: string }> {
    return this.usersService.deleteUser(id, currentUser);
  }
}
