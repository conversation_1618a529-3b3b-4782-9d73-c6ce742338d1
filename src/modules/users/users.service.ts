import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Inject,
  forwardRef,
  ForbiddenException,
} from '@nestjs/common';
import * as moment from 'moment';
import { randomBytes } from 'crypto';
import { UsersRepository } from './users.repository';
import {
  IUserCreate,
  IUserUpdate,
  IUserSearchQuery,
} from '../../models/users/interfaces/user.interface';
import { UserSerializer } from '../../models/users/serializers/user.serializer';
import { UserStatus } from '@hirenetix/models/users/constants';
import { UserRoleAssignmentsRepository } from '@hirenetix/models/roles/repositories/user-role-assignments.repository';
import { RolesRepository } from '../roles-permissions/roles.repository';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import {
  ORGANISATION_MESSAGES,
  ROLES_MESSAGES,
  USER_MESSAGES,
  AUTH_MESSAGES,
  LOCATION_MESSAGES,
} from '@hirenetix/common/constants/message.constants';
import { EmailService } from '../../providers/mail/email.service';
import { OTPService } from '@hirenetix/common/module/otp/otp.service';
import { OrganisationsService } from '../organisations/organisations.service';
import { TenantContextStorage } from '../tenant/tenant-context';
import { UserSearchQueryDto } from '../../models/users/dto/user-search-query.dto';
import { PaginatedUsersResultDto } from '../../models/users/dto/paginated-users-result.dto';
import {
  SORT_FIELD,
  SORT_ORDER,
  ExportFormat,
} from '../../models/users/constants/user.constants';
import { EditUserDto } from '../../models/users/dto/edit-user.dto';
import { UserExportService } from './user-export.service';
import { User } from '@hirenetix/models/users/entities/user.entity';
import { LocationsService } from '../locations/locations.service';
import { RoleType } from '@hirenetix/models/roles/constants';
import { CurrentUserSerializer } from '@hirenetix/models/users/serializers/current-user.serializer';

@Injectable()
export class UsersService {
  constructor(
    private readonly usersRepository: UsersRepository,
    private readonly userRoleAssignmentsRepository: UserRoleAssignmentsRepository,
    private readonly rolesRepository: RolesRepository,
    private readonly emailService: EmailService,
    @Inject(forwardRef(() => OTPService))
    private readonly otpService: OTPService,
    private readonly organisationsService: OrganisationsService,
    private readonly userExportService: UserExportService,
    private readonly locationsService: LocationsService,
  ) {}

  /**
   * Find a user by ID with associated roles
   * @param id - The ID of the user to find
   * @returns The user serializer
   * @throws NotFoundException if the user is not found
   */
  async findById(id: string): Promise<UserSerializer> {
    const tenantId = TenantContextStorage.getCurrentTenantId();

    // Get the user with role assignments and location
    const user = await this.usersRepository.findOne({
      where: { id, tenantId },
      relations: [
        'userRoleAssignments',
        'userRoleAssignments.role',
        'location',
      ],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    // The serializer will automatically handle roles and location
    return UserSerializer.serialize(user);
  }

  /**
   * Find a user by email
   * @param email - The email of the user to find
   * @returns The user entity
   * @throws NotFoundException if the user is not found
   */
  async findByEmail(email: string): Promise<User> {
    const user = await this.usersRepository.findByEmail(email);

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found`);
    }

    return user;
  }

  async find(
    query: IUserSearchQuery,
  ): Promise<{ data: UserSerializer[]; count: number }> {
    const [users, count] = await this.usersRepository.findWithCount(query);

    return {
      data: UserSerializer.serializeMany(users),
      count,
    };
  }

  async create(data: IUserCreate): Promise<UserSerializer> {
    // Check if user with the same email already exists
    const existingUserByEmail = await this.usersRepository.findByEmail(
      data.email,
    );
    if (existingUserByEmail) {
      throw new ConflictException(
        `User with email ${data.email} already exists`,
      );
    }

    const user = await this.usersRepository.create(data);

    return UserSerializer.serialize(user);
  }

  async update(id: string, data: IUserUpdate): Promise<UserSerializer> {
    // Check if user exists
    const existingUser = await this.usersRepository.findById(id);
    if (!existingUser) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    // Check if email is being updated and is not already in use
    if (data.email && data.email !== existingUser.email) {
      const existingUserByEmail = await this.usersRepository.findByEmail(
        data.email,
      );
      if (existingUserByEmail) {
        throw new ConflictException(
          `User with email ${data.email} already exists`,
        );
      }
    }

    // Check if phone is being updated and is not already in use
    if (data.phone && data.phone !== existingUser.phone) {
      const existingUserByPhone = await this.usersRepository.findByPhone(
        data.phone,
      );
      if (existingUserByPhone) {
        throw new ConflictException(
          `User with phone ${data.phone} already exists`,
        );
      }
    }

    const updatedUser = await this.usersRepository.update(id, data);

    return UserSerializer.serialize(updatedUser);
  }

  async delete(id: string): Promise<void> {
    const deleted = await this.usersRepository.delete(id);

    if (!deleted) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
  }

  async changeStatus(id: string, status: UserStatus): Promise<UserSerializer> {
    return this.update(id, { status });
  }

  /**
   * Verify a user's password
   * @param email - The email of the user to verify
   * @param password - The password to verify
   * @returns True if the password is correct, false otherwise
   */
  async verifyPassword(email: string, password: string): Promise<boolean> {
    const user = await this.usersRepository.findByEmail(email);

    if (!user) {
      return false;
    }

    return this.usersRepository.verifyPassword(user, password);
  }

  /**
   * Create a user with a role (permissions are checked by guards)
   */
  async createUserWithRole(data: IUserCreate): Promise<UserSerializer> {
    // Validate that organisation ID is provided
    if (!data.tenantId) {
      throw new BadRequestException(
        ORGANISATION_MESSAGES.ORGANISATION_ID_REQUIRED,
      );
    }

    //Check if tenant is exist
    await this.organisationsService.findById(data.tenantId);

    // Find and assign the role based on roleId
    let targetRole: Role | null = null;

    if (data.roleId) {
      // If roleId is provided, find role by ID
      targetRole = await this.rolesRepository.findById(data.roleId);
      if (!targetRole) {
        throw new NotFoundException(ROLES_MESSAGES.ROLE_NOT_FOUND);
      }

      // Validate location ID if role type is location
      if (targetRole.roleType === RoleType.LOCATION) {
        if (!data.locationId) {
          throw new BadRequestException(LOCATION_MESSAGES.LOCATION_ID_REQUIRED);
        }
        // Verify that the location exists and belongs to the organization
        const location = await this.locationsService.findOne(data.locationId);
        if (!location) {
          throw new NotFoundException(LOCATION_MESSAGES.LOCATION_NOT_FOUND);
        }
        if (location.tenantId !== data.tenantId) {
          throw new BadRequestException(
            LOCATION_MESSAGES.LOCATION_DOES_NOT_BELONG_TO_ORGANIZATION,
          );
        }
      } else if (data.locationId) {
        // If role is not location type but locationId is provided, throw error
        throw new BadRequestException(
          LOCATION_MESSAGES.LOCATION_ID_NOT_ALLOWED_FOR_NON_LOCATION_ROLES,
        );
      }
    } else {
      throw new BadRequestException(ROLES_MESSAGES.ROLE_ID_REQUIRED);
    }

    // Create the user
    const user = await this.create(data);

    // Generate OTP for email verification
    const otp = await this.otpService.generateOTP(user.id);

    // Send welcome email with OTP
    await this.emailService.sendWelcomeEmail(data.email, data.fullName, otp);

    await this.userRoleAssignmentsRepository.create({
      userId: user.id,
      roleId: targetRole.id,
    });

    return user;
  }

  /**
   * Change a user's password
   * @param email - The email of the user
   * @param newPassword - The new password
   * @returns The updated user serializer
   */
  async changePassword(
    email: string,
    newPassword: string,
  ): Promise<UserSerializer> {
    const user = await this.usersRepository.findByEmail(email);

    if (!user) {
      throw new NotFoundException(USER_MESSAGES.USER_NOT_FOUND(email));
    }

    //check if the email is verified
    if (!user.isEmailVerified) {
      throw new BadRequestException(USER_MESSAGES.EMAIL_NOT_VERIFIED);
    }

    //Check if the password reset is required
    if (!user.isPasswordResetRequired) {
      throw new BadRequestException(
        USER_MESSAGES.PASSWORD_NOT_ALLOWED_TO_CHANGED,
      );
    }

    const updatedUser = await this.usersRepository.setPassword(
      user.id,
      newPassword,
    );

    if (!updatedUser) {
      throw new BadRequestException(USER_MESSAGES.PASSWORD_CHANGE_FAILED);
    }

    return UserSerializer.serialize(updatedUser);
  }

  /**
   * Create a password reset token and send a reset email
   * @param email - The email of the user requesting password reset
   * @returns Boolean indicating success and a message
   */
  async createPasswordResetToken(
    email: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Check if user exists
      const user = await this.usersRepository.findByEmail(email);
      if (!user) {
        // Return success anyway to prevent email enumeration
        return {
          success: true,
          message:
            AUTH_MESSAGES.A_PASSWORD_RESET_LINK_HAS_BEEN_SENT_TO_YOUR_EMAIL,
        };
      }

      // Generate token and set expiration

      const resetToken = randomBytes(32).toString('hex');
      const resetExpires = moment().add(5, 'minutes').toDate();
      // Save token to user
      await this.usersRepository.setPasswordResetToken(
        email,
        resetToken,
        resetExpires,
      );

      const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

      // Send email
      await this.emailService.sendPasswordResetEmail(
        email,
        user.fullName,
        resetUrl,
      );

      return {
        success: true,
        message: AUTH_MESSAGES.PASSWORD_RESET_EMAIL_SENT,
      };
    } catch (e) {
      console.error('Error initiating password reset:', e);
      return {
        success: false,
        message: USER_MESSAGES.PASSWORD_RESET_FAILED,
      };
    }
  }

  validateResetToken = async (
    token: string,
  ): Promise<{ isValid: boolean; user: User | null }> => {
    const user = await this.usersRepository.findByResetToken(token);

    if (!user) {
      return { isValid: false, user: null };
    }

    if (moment(user.passwordResetExpires).isBefore(moment())) {
      return { isValid: false, user: null };
    }

    return { isValid: true, user };
  };

  /**
   * Reset a user's password using a reset token
   * @param token - The reset token from the email
   * @param newPassword - The new password
   * @returns The updated user serializer
   */
  async resetPassword(
    token: string,
    newPassword: string,
  ): Promise<UserSerializer> {
    // Find user by reset token
    const user = await this.usersRepository.findByResetToken(token);

    if (!user) {
      throw new BadRequestException(AUTH_MESSAGES.LINK_EXPIRED);
    }

    // Check if token is expired
    if (moment(user.passwordResetExpires).isBefore(moment())) {
      throw new BadRequestException(AUTH_MESSAGES.TOKEN_EXPIRED);
    }

    // Update password and clear reset token
    const updatedUser = await this.usersRepository.setPassword(
      user.id,
      newPassword,
    );

    if (!updatedUser) {
      throw new BadRequestException(USER_MESSAGES.PASSWORD_CHANGE_FAILED);
    }

    // Clear the reset token
    await this.usersRepository.clearPasswordResetToken(user.id);

    return UserSerializer.serialize(updatedUser);
  }

  /**
   * Add a user to an organization
   * @param data - User data including organization, role, and timezone
   * @returns The created user
   */
  async addUserToOrganization(data: IUserCreate): Promise<UserSerializer> {
    const tenantId = TenantContextStorage.getCurrentTenantId();
    // Validate that organisation ID is provided
    if (!tenantId) {
      throw new BadRequestException(
        ORGANISATION_MESSAGES.ORGANISATION_ID_REQUIRED,
      );
    }

    // Validate that role ID is provided
    if (!data.roleId) {
      throw new BadRequestException(ROLES_MESSAGES.ROLE_ID_REQUIRED);
    }

    // Check if user with the same email already exists
    if (data.email) {
      const existingUserByEmail = await this.usersRepository.findByEmail(
        data.email,
      );
      if (existingUserByEmail) {
        throw new ConflictException(USER_MESSAGES.EMAIL_ALREADY_REGISTERED);
      }
    }
    if (data.phone) {
      const existingUserByPhone = await this.usersRepository.findByPhone(
        data.phone,
      );
      if (existingUserByPhone) {
        throw new ConflictException(USER_MESSAGES.PHONE_ALREADY_REGISTERED);
      }
    }

    // Check if organization exists
    const organisation = await this.organisationsService.findById(tenantId);

    if (!organisation) {
      throw new NotFoundException(ORGANISATION_MESSAGES.ORGANISATION_NOT_FOUND);
    } else {
      data.tenantId = organisation.id;
    }

    // Check if role exists and validate location based on role type
    const role = await this.rolesRepository.findById(data.roleId);
    if (!role) {
      throw new NotFoundException(ROLES_MESSAGES.ROLE_NOT_FOUND);
    }

    // Validate location ID if role type is location
    if (role.roleType === RoleType.LOCATION) {
      if (!data.locationId) {
        throw new BadRequestException(LOCATION_MESSAGES.LOCATION_ID_REQUIRED);
      }
      // Verify that the location exists and belongs to the organization
      const location = await this.locationsService.findOne(data.locationId);
      if (!location) {
        throw new NotFoundException(LOCATION_MESSAGES.LOCATION_NOT_FOUND);
      }
      if (location.tenantId !== data.tenantId) {
        throw new BadRequestException(
          LOCATION_MESSAGES.LOCATION_DOES_NOT_BELONG_TO_ORGANIZATION,
        );
      }
    } else if (data.locationId) {
      // If role is not location type but locationId is provided, throw error
      throw new BadRequestException(
        LOCATION_MESSAGES.LOCATION_ID_NOT_ALLOWED_FOR_NON_LOCATION_ROLES,
      );
    }

    // Create the user
    const user = await this.create(data);

    // Generate OTP for email verification
    const otp = await this.otpService.generateOTP(user.id);

    // Send welcome email with OTP
    await this.emailService.sendWelcomeEmail(data.email, data.fullName, otp);

    // Assign the role to the user
    await this.userRoleAssignmentsRepository.create({
      userId: user.id,
      roleId: role.id,
    });

    return user;
  }

  /**
   * Get users for the current tenant with pagination, filtering, search, and sorting
   * @param queryParams Parameters for filtering, searching, pagination, and sorting
   * @returns Paginated array of users and metadata
   */
  async getUsers(
    queryParams: UserSearchQueryDto = {},
  ): Promise<
    | PaginatedUsersResultDto
    | { buffer: Buffer; fileName: string; contentType: string }
  > {
    const {
      page = 1,
      limit = 10,
      search,
      roles,
      status,
      startDate,
      endDate,
      sortBy = SORT_FIELD.CREATED_AT,
      sortOrder = SORT_ORDER.DESC,
      export: shouldExport = false,
    } = queryParams;

    // If export is requested, fetch all users without pagination
    if (shouldExport) {
      return this.exportUsers(queryParams);
    }

    const skip = (page - 1) * limit;
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!tenantId) {
      throw new BadRequestException(ORGANISATION_MESSAGES.TENANT_ID_REQUIRED);
    }

    // Map sort field to database column name
    let sortColumn: SORT_FIELD = SORT_FIELD.CREATED_AT; // Default
    if (sortBy === SORT_FIELD.STATUS) {
      sortColumn = SORT_FIELD.STATUS;
    } else if (sortBy === SORT_FIELD.CREATED_AT) {
      sortColumn = SORT_FIELD.CREATED_AT;
    }

    let sortOrderBy = SORT_ORDER.DESC;
    if (sortOrder === SORT_ORDER.ASC) {
      sortOrderBy = SORT_ORDER.ASC;
    }

    // Search users with all criteria
    const [users, total] = await this.usersRepository.searchUsers({
      tenantId,
      skip,
      take: limit,
      search,
      roles,
      status,
      startDate,
      endDate,
      sortColumn,
      sortOrderBy,
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limit);

    const result: PaginatedUsersResultDto = {
      data: UserSerializer.serializeMany(users),
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };

    return result;
  }

  /**
   * Export users data to CSV or XLSX format
   * @param queryParams Query parameters for filtering users
   * @returns Buffer with exported data and file information
   */
  async exportUsers(
    queryParams: UserSearchQueryDto,
  ): Promise<{ buffer: Buffer; fileName: string; contentType: string }> {
    const {
      search,
      roles,
      status,
      startDate,
      endDate,
      sortBy = SORT_FIELD.FULL_NAME,
      sortOrder = SORT_ORDER.ASC,
      exportFormat = ExportFormat.CSV,
    } = queryParams;

    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!tenantId) {
      throw new BadRequestException(ORGANISATION_MESSAGES.TENANT_ID_REQUIRED);
    }

    // Map sort field to database column name
    let sortColumn: SORT_FIELD = SORT_FIELD.FULL_NAME; // Default
    if (sortBy === SORT_FIELD.STATUS) {
      sortColumn = SORT_FIELD.STATUS;
    } else if (sortBy === SORT_FIELD.CREATED_AT) {
      sortColumn = SORT_FIELD.CREATED_AT;
    }

    let sortOrderBy = SORT_ORDER.ASC;
    if (sortOrder === SORT_ORDER.DESC) {
      sortOrderBy = SORT_ORDER.DESC;
    }

    // For exports, we don't use pagination (skip/take) to get all records
    const [users] = await this.usersRepository.searchUsers({
      tenantId,
      search,
      roles,
      status,
      startDate,
      endDate,
      sortColumn,
      sortOrderBy,
      // Skip pagination parameters
    });

    const serializedUsers = UserSerializer.serializeMany(users);

    // Export the data using the export service
    return this.userExportService.exportUsers(serializedUsers, exportFormat);
  }

  /**
   * Edit a user's details including role assignment
   * @param id - The ID of the user to edit
   * @param editUserDto - DTO with the fields to update
   * @returns The updated user
   */
  async editUser(
    id: string,
    editUserDto: EditUserDto,
  ): Promise<UserSerializer> {
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!tenantId) {
      throw new BadRequestException(ORGANISATION_MESSAGES.TENANT_ID_REQUIRED);
    }

    // Check if user exists and belongs to the current tenant
    const user = await this.usersRepository.findOne({
      where: { id, tenantId },
      relations: ['userRoleAssignments', 'userRoleAssignments.role'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    // Create update object with only the fields that are provided
    const updateData: IUserUpdate = {};

    if (editUserDto.fullName !== undefined) {
      updateData.fullName = editUserDto.fullName;
    }

    if (editUserDto.phone !== undefined) {
      // Check if the new phone is already used by another user
      if (editUserDto.phone && editUserDto.phone !== user.phone) {
        const existingUserByPhone = await this.usersRepository.findByPhone(
          editUserDto.phone,
        );
        if (existingUserByPhone && existingUserByPhone.id !== id) {
          throw new ConflictException(USER_MESSAGES.PHONE_ALREADY_REGISTERED);
        }
      }

      updateData.phone = editUserDto.phone;
    }

    if (editUserDto.timeZone !== undefined) {
      updateData.timeZone = editUserDto.timeZone;
    }

    if (editUserDto.status !== undefined) {
      updateData.status = editUserDto.status as UserStatus;
    }

    // Update the user basic details
    const updatedUser = await this.usersRepository.update(id, updateData);

    if (!updatedUser) {
      throw new BadRequestException('Failed to update user');
    }

    // Update role if provided
    if (editUserDto.roleId) {
      // Check if role exists
      const role = await this.rolesRepository.findById(editUserDto.roleId);
      if (!role) {
        throw new NotFoundException(ROLES_MESSAGES.ROLE_NOT_FOUND);
      }

      // Validate location ID if role type is location
      if (role.roleType === RoleType.LOCATION) {
        if (!editUserDto.locationId) {
          throw new BadRequestException(LOCATION_MESSAGES.LOCATION_ID_REQUIRED);
        }
        // Verify that the location exists and belongs to the organization
        const location = await this.locationsService.findOne(
          editUserDto.locationId,
        );
        if (!location) {
          throw new NotFoundException(LOCATION_MESSAGES.LOCATION_NOT_FOUND);
        }
        if (location.tenantId !== user.tenantId) {
          throw new BadRequestException(
            LOCATION_MESSAGES.LOCATION_DOES_NOT_BELONG_TO_ORGANIZATION,
          );
        }
      } else if (editUserDto.locationId) {
        // If role is not location type but locationId is provided, throw error
        throw new BadRequestException(
          LOCATION_MESSAGES.LOCATION_ID_NOT_ALLOWED_FOR_NON_LOCATION_ROLES,
        );
      }

      // Get current user roles
      const [currentRoles] = await this.userRoleAssignmentsRepository.find({
        userId: id,
      });

      // Remove all existing roles
      for (const roleAssignment of currentRoles) {
        await this.userRoleAssignmentsRepository.delete(roleAssignment.id);
      }

      // Create new role assignment
      await this.userRoleAssignmentsRepository.create({
        userId: id,
        roleId: editUserDto.roleId,
      });

      // Save locationId if role type is location, otherwise clear it
      if (role.roleType === RoleType.LOCATION) {
        await this.usersRepository.update(id, {
          locationId: editUserDto.locationId,
        });
      } else {
        await this.usersRepository.update(id, {
          locationId: null,
        });
      }
    } else if (editUserDto.locationId) {
      // If locationId is provided but no roleId, we need to check the user's current role type
      const currentRole = user.userRoleAssignments?.[0]?.role;
      if (!currentRole || currentRole.roleType !== RoleType.LOCATION) {
        throw new BadRequestException(
          LOCATION_MESSAGES.LOCATION_ID_NOT_ALLOWED_FOR_NON_LOCATION_ROLES,
        );
      }
      // Verify that the location exists and belongs to the organization
      const location = await this.locationsService.findOne(
        editUserDto.locationId,
      );
      if (!location) {
        throw new NotFoundException(LOCATION_MESSAGES.LOCATION_NOT_FOUND);
      }
      if (location.tenantId !== user.tenantId) {
        throw new BadRequestException(
          LOCATION_MESSAGES.LOCATION_DOES_NOT_BELONG_TO_ORGANIZATION,
        );
      }
      // Save locationId if current role type is location
      await this.usersRepository.update(id, {
        locationId: editUserDto.locationId,
      });
    }

    // Fetch the updated user with all related data
    const refreshedUser = await this.usersRepository.findById(id);
    return UserSerializer.serialize(refreshedUser);
  }

  /**
   * Delete a user by ID
   * @param id - The ID of the user to delete
   * @param currentUser - The currently logged in user
   * @returns Object indicating success status and message
   */
  async deleteUser(
    id: string,
    currentUser: User,
  ): Promise<{ success: boolean; message: string }> {
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!tenantId) {
      throw new BadRequestException(ORGANISATION_MESSAGES.TENANT_ID_REQUIRED);
    }

    // Prevent users from deleting their own account
    if (id === currentUser.id) {
      throw new ForbiddenException('You cannot delete your own account');
    }

    // Check if user exists and belongs to the current tenant
    const user = await this.usersRepository.findOne({
      where: { id, tenantId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    try {
      // Delete user role assignments first
      const userRoles =
        await this.userRoleAssignmentsRepository.getUserRoles(id);

      // If the user has role assignments, delete them
      if (userRoles.length > 0) {
        for (const roleAssignment of userRoles) {
          await this.userRoleAssignmentsRepository.delete(roleAssignment.id);
        }
      }

      // Now delete the user
      const deleted = await this.usersRepository.delete(id);

      if (!deleted) {
        throw new BadRequestException('Failed to delete user');
      }

      return {
        success: true,
        message: 'User deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }

      throw new BadRequestException(`Failed to delete user: ${error.message}`);
    }
  }

  /**
   * Get current user information with roles and other relations
   * @param userId - The ID of the current user
   * @returns The current user serializer with full information
   */
  async getCurrentUser(userId: string): Promise<CurrentUserSerializer> {
    // First get user with basic relations
    const user = await this.usersRepository.findOne({
      where: { id: userId },
      relations: [
        'userRoleAssignments',
        'userRoleAssignments.role',
        'userRoleAssignments.role.rolePermissions',
        'userRoleAssignments.role.rolePermissions.permission',
        'organisation',
      ],
    });

    if (!user) {
      throw new NotFoundException(USER_MESSAGES.USER_NOT_FOUND);
    }

    // If user has a department ID, load department separately
    if (user.departmentId) {
      const userWithDepartment = await this.usersRepository.findOne({
        where: { id: userId },
        relations: ['department'],
      });

      if (userWithDepartment?.department) {
        user.department = userWithDepartment.department;
      }
    }

    // If user has a location ID, load location separately
    if (user.locationId) {
      const userWithLocation = await this.usersRepository.findOne({
        where: { id: userId },
        relations: ['location'],
      });

      if (userWithLocation?.location) {
        user.location = userWithLocation.location;
      }
    }

    return CurrentUserSerializer.serialize(user);
  }
}
