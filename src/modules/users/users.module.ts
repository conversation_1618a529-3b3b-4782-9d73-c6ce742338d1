import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@hirenetix/models/users/entities/user.entity';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { RolesModule } from '../roles-permissions/roles.module';
import { CommonModule } from '@hirenetix/common/common.module';
import { UsersRepository } from './users.repository';
import { EmailModule } from '@hirenetix/providers/mail/email.module';
import { OTPModule } from '@hirenetix/common/module/otp/otp.module';
import { OrganisationsModule } from '../organisations/organisations.module';
import { UserExportService } from './user-export.service';
import { LocationsModule } from '../locations/locations.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    RolesModule,
    CommonModule,
    EmailModule,
    forwardRef(() => OTPModule),
    OrganisationsModule,
    LocationsModule,
  ],
  providers: [UsersRepository, UsersService, UserExportService],
  controllers: [UsersController],
  exports: [UsersService, UsersRepository, UserExportService],
})
export class UsersModule {}
