import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  Res,
} from '@nestjs/common';
import { JwtAuthGuard } from '@hirenetix/authentication/jwt-auth.guard';
import { RolesService } from './roles.service';
import { RoleSerializer } from '@hirenetix/models/roles/serializers/role.serializer';
import { RoleWithPermissionsSerializer } from '@hirenetix/models/roles/serializers/role-with-permissions.serializer';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
  ApiProduces,
} from '@nestjs/swagger';
import { CreateRoleDto } from '@hirenetix/models/roles/dto/create-role.dto';
import { UpdateRoleDto } from '@hirenetix/models/roles/dto/update-role.dto';
import { UpdateRoleResultDto } from '@hirenetix/models/roles/dto/update-role-result.dto';
import {
  ExportFormat,
  PermissionActions,
  PermissionModules,
} from '@hirenetix/models/roles/constants/role.constants';
import {
  API_OPERATIONS,
  API_QUERY_PARAMETERS,
  API_RESPONSES,
} from '../../common/constants/swagger.constants';
import { CheckRoleNameUniquenessDto } from '@hirenetix/models/roles/dto/check-role-name-uniqueness.dto';
import { RoleNameUniquenessResultDto } from '@hirenetix/models/roles/dto/role-name-uniqueness-result.dto';
import { DeleteRoleResultDto } from '@hirenetix/models/roles/dto/delete-role-result.dto';
import { JWT_AUTH_NAME } from '../../common/constants/general.constants';
import { RoleSearchQueryDto } from '@hirenetix/models/roles/dto/role-search-query.dto';
import { PaginatedRolesResultDto } from '@hirenetix/models/roles/dto/paginated-roles-result.dto';
import { Response } from 'express';
import { RequirePermissions } from '../../common/decorators/metadata/permissions.decorator';

@ApiTags('Roles')
@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  /**
   * Get all roles for a specific tenant with pagination, filtering, and search
   * @param queryParams Parameters for pagination, filtering, and search
   * @returns Paginated array of roles with metadata
   */
  @Get()
  @ApiOperation({ summary: API_OPERATIONS.GET_ALL_ROLES_FOR_A_SPECIFIC_TENANT })
  @ApiQuery({
    name: 'page',
    required: false,
    description: API_QUERY_PARAMETERS.PAGE_NUMBER,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: API_QUERY_PARAMETERS.NUMBER_OF_ITEMS_PER_PAGE,
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description:
      API_QUERY_PARAMETERS.SEARCH_TEXT_THAT_WILL_BE_MATCHED_AGAINST_ROLE_NAMES_AND_DESCRIPTIONS,
    type: String,
  })
  @ApiQuery({
    name: 'modules',
    required: false,
    description: API_QUERY_PARAMETERS.FILTER_ROLES_BY_SPECIFIC_MODULE,
    type: [String],
    isArray: true,
  })
  @ApiQuery({
    name: 'export',
    required: false,
    description:
      'Flag to export the data instead of returning paginated results',
    type: Boolean,
  })
  @ApiQuery({
    name: 'exportFormat',
    required: false,
    description: 'Format for exporting data (csv or xlsx)',
    enum: ExportFormat,
  })
  @ApiResponse({
    status: 200,
    description: API_RESPONSES.LIST_OF_ROLES_FOR_THE_TENANT,
    type: PaginatedRolesResultDto,
  })
  @ApiProduces(
    'application/json',
    'text/csv',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @ApiResponse({
    status: 403,
    description: API_RESPONSES.FORBIDDEN_INSUFFICIENT_PERMISSIONS,
  })
  @ApiResponse({ status: 404, description: API_RESPONSES.NO_ROLES_FOUND })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard)
  @RequirePermissions({
    action: PermissionActions.VIEW_ROLES,
    module: PermissionModules.ROLES,
  })
  async getRoles(
    @Query() queryParams: RoleSearchQueryDto,
    @Res() res: Response,
  ): Promise<void> {
    const result = await this.rolesService.getRolesByTenantId(queryParams);

    // Check if the result is a file export
    if ('buffer' in result) {
      // Set headers for file download
      res.setHeader('Content-Type', result.contentType);
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=${result.fileName}`,
      );

      // Send the buffer as a file download
      res.send(result.buffer);
    } else {
      // Normal JSON response
      res.json(result);
    }
  }

  /**
   * Get a specific role by ID with its permissions
   * @param id The ID of the role to retrieve
   * @returns The role with its permissions
   */
  @Get(':id')
  @ApiOperation({ summary: API_OPERATIONS.GET_ROLE_BY_ID_WITH_PERMISSIONS })
  @ApiParam({
    name: 'id',
    description: 'ID of the role to retrieve',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: API_RESPONSES.ROLE_WITH_PERMISSIONS_RETRIEVED,
    type: RoleWithPermissionsSerializer,
  })
  @ApiResponse({
    status: 400,
    description: API_RESPONSES.TENANT_ID_REQUIRED,
  })
  @ApiResponse({
    status: 403,
    description: API_RESPONSES.FORBIDDEN_INSUFFICIENT_PERMISSIONS,
  })
  @ApiResponse({
    status: 404,
    description: API_RESPONSES.ROLE_NOT_FOUND,
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard)
  @RequirePermissions({
    action: PermissionActions.VIEW_ROLES,
    module: PermissionModules.ROLES,
  })
  async getRoleById(
    @Param('id') id: string,
  ): Promise<RoleWithPermissionsSerializer> {
    return await this.rolesService.getRoleById(id);
  }

  /**
   * Check if a role name is unique within the current tenant's organization
   * @param checkRoleNameUniquenessDto The DTO containing the role name to check
   * @returns An object indicating whether the role name is unique
   */
  @Post('check-role-name')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: API_OPERATIONS.CHECK_ROLE_NAME_UNIQUENESS,
  })
  @ApiResponse({
    status: 200,
    description: API_RESPONSES.ROLE_NAME_UNIQUENESS_RESULT,
    type: RoleNameUniquenessResultDto,
  })
  @ApiResponse({
    status: 400,
    description: API_RESPONSES.TENANT_ID_REQUIRED,
  })
  @ApiResponse({
    status: 403,
    description: API_RESPONSES.FORBIDDEN_INSUFFICIENT_PERMISSIONS,
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard)
  async checkRoleNameUniqueness(
    @Body() checkRoleNameUniquenessDto: CheckRoleNameUniquenessDto,
  ): Promise<RoleNameUniquenessResultDto> {
    return await this.rolesService.checkRoleNameUniqueness(
      checkRoleNameUniquenessDto,
    );
  }

  /**
   * Create a new role for the current tenant
   * Only admin or higher roles can create new roles
   * Optionally assign permissions to the role during creation
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: API_OPERATIONS.CREATE_A_NEW_ROLE_FOR_THE_CURRENT_TENANT,
    description: API_OPERATIONS.CREATE_A_NEW_ROLE_WITH_PERMISSIONS,
  })
  @ApiResponse({
    status: 201,
    description: API_RESPONSES.ROLE_CREATED_SUCCESSFULLY,
    type: RoleSerializer,
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard)
  @RequirePermissions({
    action: PermissionActions.ADD_ROLES,
    module: PermissionModules.ROLES,
  })
  async createRole(
    @Body() createRoleDto: CreateRoleDto,
  ): Promise<RoleSerializer> {
    console.log('createRoleDto :', createRoleDto);
    return await this.rolesService.createRole(createRoleDto);
  }

  /**
   * Delete a role by ID
   * Only admin or higher roles can delete roles
   * System roles cannot be deleted
   * @param id The ID of the role to delete
   * @returns An object indicating whether the role was successfully deleted
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: API_OPERATIONS.DELETE_ROLE,
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the role to delete',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: API_RESPONSES.ROLE_DELETED_SUCCESSFULLY,
    type: DeleteRoleResultDto,
  })
  @ApiResponse({
    status: 400,
    description: API_RESPONSES.TENANT_ID_REQUIRED,
  })
  @ApiResponse({
    status: 403,
    description: API_RESPONSES.SYSTEM_ROLE_DELETION_NOT_ALLOWED,
  })
  @ApiResponse({
    status: 404,
    description: API_RESPONSES.ROLE_NOT_FOUND,
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard)
  @RequirePermissions({
    action: PermissionActions.DELETE_ROLES,
    module: PermissionModules.ROLES,
  })
  async deleteRole(@Param('id') id: string): Promise<DeleteRoleResultDto> {
    return await this.rolesService.deleteRole(id);
  }

  /**
   * Update a role by ID
   * Only admin or higher roles can update roles
   * System roles cannot be updated
   * @param id The ID of the role to update
   * @param updateRoleDto The data to update the role with
   * @returns An object indicating whether the role was successfully updated
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: API_OPERATIONS.UPDATE_ROLE,
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the role to update',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: API_RESPONSES.ROLE_UPDATED_SUCCESSFULLY,
    type: UpdateRoleResultDto,
  })
  @ApiResponse({
    status: 400,
    description: API_RESPONSES.TENANT_ID_REQUIRED,
  })
  @ApiResponse({
    status: 403,
    description: API_RESPONSES.SYSTEM_ROLE_UPDATE_NOT_ALLOWED,
  })
  @ApiResponse({
    status: 404,
    description: API_RESPONSES.ROLE_NOT_FOUND,
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard)
  @RequirePermissions({
    action: PermissionActions.EDIT_ROLES,
    module: PermissionModules.ROLES,
  })
  async updateRole(
    @Param('id') id: string,
    @Body() updateRoleDto: UpdateRoleDto,
  ): Promise<UpdateRoleResultDto> {
    return await this.rolesService.updateRole(id, updateRoleDto);
  }
}
