import { Injectable } from '@nestjs/common';
import { PermissionsRepository } from '@hirenetix/models/roles/repositories/permissions.repository';
import { UserRoleAssignmentsRepository } from '@hirenetix/models/roles/repositories/user-role-assignments.repository';
import { PermissionSerializer } from '@hirenetix/models/roles/serializers/permission.serializer';
import { SystemRoles } from '@hirenetix/models/roles/constants/role.constants';
import { UserPermissionsResponseDto } from '@hirenetix/models/roles/dto/user-permissions-response.dto';

@Injectable()
export class PermissionsService {
  constructor(
    private readonly permissionsRepository: PermissionsRepository,
    private readonly userRoleAssignmentsRepository: UserRoleAssignmentsRepository,
  ) {}

  /**
   * Get all permissions in the system
   * @returns Array of all permissions
   */
  async getAllPermissions(): Promise<PermissionSerializer[]> {
    const permissions = await this.permissionsRepository.findAll();
    return PermissionSerializer.serializeMany(permissions);
  }

  async getUserPermissionsByModule(
    userId: string,
  ): Promise<UserPermissionsResponseDto> {
    // Get user role assignments with related data
    const userRoleAssignments =
      await this.userRoleAssignmentsRepository.getUserRoles(userId);

    // Initialize permissions object
    const permissionsByModule: Record<string, string[]> = {};

    // If user has super admin role, get all permissions
    if (
      userRoleAssignments.some(
        (assignment) => assignment.role?.name === SystemRoles.SUPER_ADMIN,
      )
    ) {
      const allPermissions = await this.permissionsRepository.findAll();

      // Group permissions by module
      allPermissions.forEach((permission) => {
        if (!permissionsByModule[permission.module]) {
          permissionsByModule[permission.module] = [];
        }
        permissionsByModule[permission.module].push(permission.name);
      });
    } else {
      // Get permissions from user roles
      userRoleAssignments.forEach((assignment) => {
        assignment.role.rolePermissions.forEach((rolePermission) => {
          const permission = rolePermission.permission;
          const module = permission.module;

          if (!permissionsByModule[module]) {
            permissionsByModule[module] = [];
          }

          if (!permissionsByModule[module].includes(permission.name)) {
            permissionsByModule[module].push(permission.name);
          }
        });
      });
    }

    return { permissions: permissionsByModule };
  }
}
