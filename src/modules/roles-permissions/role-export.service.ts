import { Injectable } from '@nestjs/common';
import { stringify } from 'csv-stringify/sync';
import * as XLSX from 'xlsx';
import { RoleWithPermissionsSerializer } from '@hirenetix/models/roles/serializers/role-with-permissions.serializer';
import { ExportFormat } from '@hirenetix/models/roles/constants/role.constants';
import { RoleSerializer } from '@hirenetix/models/roles/serializers/role.serializer';
import * as moment from 'moment';
@Injectable()
export class RoleExportService {
  /**
   * Convert role data to CSV or XLSX buffer
   * @param roles Array of serialized roles
   * @param format Export format (csv or xlsx)
   * @param brandName Organization brand name for filename
   * @returns Buffer containing the exported data
   */
  async exportRoles(
    roles: RoleSerializer[] | RoleWithPermissionsSerializer[],
    format: ExportFormat,
    brandName?: string,
  ): Promise<{ buffer: Buffer; fileName: string; contentType: string }> {
    // Extract the data we want to export and format it for export
    const data = roles.map((role) => {
      // Format the permissions as a comma-separated string
      const permissionsString = role.permissions
        ? role.permissions.join(', ')
        : '';

      return {
        'Role Title': role.name,
        'Role Description': role.description || '',
        'Allowed Permissions': permissionsString,
      };
    });

    // Use brand name in filename if provided, otherwise use a generic name
    const brand = brandName ? `_${brandName.replace(/\s+/g, '_')}` : '';

    // Format date as YYYY-MM-DD for the filename
    const dateStr = moment().format('YYYY-MM-DD'); // YYYY-MM-DD format

    const fileName = `roles${brand}_${dateStr}`;

    if (format === ExportFormat.CSV) {
      return this.exportToCsv(data, fileName);
    } else {
      return this.exportToXlsx(data, fileName);
    }
  }

  /**
   * Export data to CSV format
   * @param data Formatted data to export
   * @param fileName Base filename without extension
   * @returns Buffer containing CSV data with file information
   */
  private exportToCsv(
    data: Record<string, any>[],
    fileName: string,
  ): { buffer: Buffer; fileName: string; contentType: string } {
    const csvContent = stringify(data, {
      header: true,
      columns: Object.keys(data[0] || {}),
    });

    return {
      buffer: Buffer.from(csvContent),
      fileName: `${fileName}.csv`,
      contentType: 'text/csv',
    };
  }

  /**
   * Export data to XLSX format
   * @param data Formatted data to export
   * @param fileName Base filename without extension
   * @returns Buffer containing XLSX data with file information
   */
  private exportToXlsx(
    data: Record<string, any>[],
    fileName: string,
  ): { buffer: Buffer; fileName: string; contentType: string } {
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Roles');

    const xlsxContent = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });

    return {
      buffer: xlsxContent,
      fileName: `${fileName}.xlsx`,
      contentType:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };
  }
}
