import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import { Permission } from '@hirenetix/models/roles/entities/permission.entity';
import { RolePermission } from '@hirenetix/models/roles/entities/role-permission.entity';
import { UserRoleAssignment } from '@hirenetix/models/roles/entities/user-role-assignment.entity';
import { RolesController } from './roles.controller';
import { RolesService } from './roles.service';
import { RolesRepository } from './roles.repository';
import { PermissionsRepository } from '@hirenetix/models/roles/repositories/permissions.repository';
import { RolePermissionsRepository } from '@hirenetix/models/roles/repositories/role-permissions.repository';
import { UserRoleAssignmentsRepository } from '@hirenetix/models/roles/repositories/user-role-assignments.repository';
import { PermissionsController } from './permissions.controller';
import { PermissionsService } from './permissions.service';
import { RoleExportService } from './role-export.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Role,
      Permission,
      RolePermission,
      UserRoleAssignment,
    ]),
  ],
  controllers: [RolesController, PermissionsController],
  providers: [
    RolesService,
    RolesRepository,
    PermissionsRepository,
    RolePermissionsRepository,
    UserRoleAssignmentsRepository,
    PermissionsService,
    RoleExportService,
  ],
  exports: [
    RolesService,
    RolesRepository,
    UserRoleAssignmentsRepository,
    PermissionsRepository,
    PermissionsService,
    RoleExportService,
  ],
})
export class RolesModule {}
