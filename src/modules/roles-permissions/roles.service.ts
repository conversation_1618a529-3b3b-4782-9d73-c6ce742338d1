import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { RoleSerializer } from '../../models/roles/serializers/role.serializer';
import { RoleWithPermissionsSerializer } from '../../models/roles/serializers/role-with-permissions.serializer';
import { RolesRepository } from './roles.repository';
import { CreateRoleDto } from '@hirenetix/models/roles/dto/create-role.dto';
import { UpdateRoleDto } from '@hirenetix/models/roles/dto/update-role.dto';
import { UpdateRoleResultDto } from '@hirenetix/models/roles/dto/update-role-result.dto';
import { TenantContextStorage } from '@hirenetix/modules/tenant/tenant-context';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import { PermissionsRepository } from '@hirenetix/models/roles/repositories/permissions.repository';
import { RolePermissionsRepository } from '@hirenetix/models/roles/repositories/role-permissions.repository';
import { DataSource } from 'typeorm';
import {
  ORGANISATION_MESSAGES,
  PERMISSIONS_MESSAGES,
  ROLES_MESSAGES,
} from '../../common/constants/message.constants';
import { RoleNameUniquenessResultDto } from '@hirenetix/models/roles/dto/role-name-uniqueness-result.dto';
import { CheckRoleNameUniquenessDto } from '@hirenetix/models/roles/dto/check-role-name-uniqueness.dto';
import { DeleteRoleResultDto } from '@hirenetix/models/roles/dto/delete-role-result.dto';
import {
  ExportFormat,
  SystemRoles,
} from '@hirenetix/models/roles/constants/role.constants';
import { RoleSearchQueryDto } from '@hirenetix/models/roles/dto/role-search-query.dto';
import { PaginatedRolesResultDto } from '@hirenetix/models/roles/dto/paginated-roles-result.dto';
import { RoleExportService } from './role-export.service';
import { UserRoleAssignment } from '@hirenetix/models/roles/entities/user-role-assignment.entity';
@Injectable()
export class RolesService {
  constructor(
    private readonly rolesRepository: RolesRepository,
    private readonly permissionsRepository: PermissionsRepository,
    private readonly rolePermissionsRepository: RolePermissionsRepository,
    private readonly dataSource: DataSource,
    private readonly roleExportService: RoleExportService,
  ) {}

  /**
   * Get roles for the current tenant with pagination, filtering, and search
   * @param queryParams Parameters for filtering, searching and pagination
   * @returns Paginated array of roles and metadata or exported file
   */
  async getRolesByTenantId(
    queryParams: RoleSearchQueryDto = {},
  ): Promise<
    | PaginatedRolesResultDto
    | { buffer: Buffer; fileName: string; contentType: string }
  > {
    const {
      page = 1,
      limit = 10,
      search,
      modules,
      type = '',
      minimal = false,
      export: shouldExport = false,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      exportFormat = ExportFormat.CSV,
    } = queryParams;

    // If export is requested, fetch all roles for export
    if (shouldExport) {
      return this.exportRoles(queryParams);
    }

    const skip = (page - 1) * limit;

    const tenantId = TenantContextStorage.getCurrentTenantId();

    // Create query builder for roles
    const queryBuilder = this.dataSource
      .getRepository(Role)
      .createQueryBuilder('role')
      .where('(role.tenantId = :tenantId OR role.custom = false)', {
        tenantId,
      });

    if (!minimal) {
      // Full data mode
      queryBuilder
        .leftJoinAndSelect('role.rolePermissions', 'rolePermission')
        .leftJoinAndSelect('rolePermission.permission', 'permission');
    } else {
      // Minimal mode
      queryBuilder.select(['role.id', 'role.name']);
      queryBuilder
        .leftJoin('role.rolePermissions', 'rolePermission')
        .leftJoin('rolePermission.permission', 'permission');
    }

    // Apply search on name, description, and permission description if provided
    if (search && search.trim() !== '') {
      const searchTerm = `%${search.trim()}%`;
      if (minimal) {
        // In minimal mode, only search by name
        queryBuilder.andWhere('role.name ILIKE :search', {
          search: searchTerm,
        });
      } else {
        // In full mode, search by name, description, permission
        queryBuilder.andWhere(
          '(role.name ILIKE :search OR ' +
            'role.description ILIKE :search OR ' +
            'permission.description ILIKE :search OR ' +
            'permission.module ILIKE :search)',
          { search: searchTerm },
        );
      }
    }

    // Filter by module if provided
    if (modules && modules.length > 0) {
      queryBuilder.andWhere('permission.module IN (:...modules)', { modules });
    }

    // Filter by type if provided
    if (type) {
      queryBuilder.andWhere('role.roleType = :type', { type });
    }

    // Clone the query builder for counting total results
    const countQueryBuilder = queryBuilder.clone();

    // Get total count without pagination - must be before adding pagination
    const total = await countQueryBuilder
      .select('COUNT(DISTINCT role.id)', 'count')
      .getRawOne()
      .then((result) => parseInt(result.count, 10));

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    queryBuilder.skip(skip).take(limit);

    queryBuilder.orderBy('role.createdAt', 'DESC');

    queryBuilder.distinct(true);

    const roles = await queryBuilder.getMany();

    const result: PaginatedRolesResultDto = {
      data: roles.map((role) => RoleSerializer.serialize(role, true)),
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };

    return result;
  }

  /**
   * Export roles to CSV or XLSX format
   * @param queryParams Query parameters for filtering roles
   * @returns Buffer with exported data and file information
   */
  async exportRoles(
    queryParams: RoleSearchQueryDto,
  ): Promise<{ buffer: Buffer; fileName: string; contentType: string }> {
    const { search, modules, exportFormat = ExportFormat.CSV } = queryParams;

    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!tenantId) {
      throw new BadRequestException(ORGANISATION_MESSAGES.TENANT_ID_REQUIRED);
    }

    // Create query builder for roles - use the same logic as in getRolesByTenantId
    // but without pagination to get all matching roles
    const queryBuilder = this.dataSource
      .getRepository(Role)
      .createQueryBuilder('role')
      .where('role.tenantId = :tenantId', { tenantId });

    // Always include permissions for export
    queryBuilder
      .leftJoinAndSelect('role.rolePermissions', 'rolePermission')
      .leftJoinAndSelect('rolePermission.permission', 'permission');

    // Apply search if provided
    if (search && search.trim() !== '') {
      const searchTerm = `%${search.trim()}%`;
      queryBuilder.andWhere(
        '(role.name ILIKE :search OR ' +
          'role.description ILIKE :search OR ' +
          'permission.description ILIKE :search OR ' +
          'permission.module ILIKE :search)',
        { search: searchTerm },
      );
    }

    // Filter by module if provided
    if (modules && modules.length > 0) {
      queryBuilder.andWhere('permission.module IN (:...modules)', { modules });
    }

    queryBuilder.orderBy('role.name', 'ASC');
    queryBuilder.distinct(true);

    const roles = await queryBuilder.getMany();

    // Get the organization name for the filename
    const organization = await this.dataSource
      .query('SELECT name FROM organisations WHERE id = $1', [tenantId])
      .then((result) => (result.length ? result[0] : null));

    const brandName = organization?.name || 'company';

    // Serialize roles with permissions
    const serializedRoles = roles.map((role) =>
      RoleSerializer.serialize(role, true),
    );

    // Export the data using the export service
    return this.roleExportService.exportRoles(
      serializedRoles,
      exportFormat,
      brandName,
    );
  }

  /**
   * Check if a role name is unique within the current tenant's organization
   * @param checkRoleNameUniquenessDto The DTO containing the role name to check
   * @returns An object indicating whether the role name is unique and a message
   */
  async checkRoleNameUniqueness(
    checkRoleNameUniquenessDto: CheckRoleNameUniquenessDto,
  ): Promise<RoleNameUniquenessResultDto> {
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!tenantId) {
      throw new BadRequestException(ORGANISATION_MESSAGES.TENANT_ID_REQUIRED);
    }

    // Check if role with same name already exists for this tenant or is a system role (custom=false)
    const existingRole = await this.dataSource
      .getRepository(Role)
      .createQueryBuilder('role')
      .where('(role.tenantId = :tenantId OR role.custom = false)', { tenantId })
      .andWhere('LOWER(role.name) = LOWER(:name)', {
        name: checkRoleNameUniquenessDto.name,
      })
      .getOne();

    if (existingRole) {
      return {
        isUnique: false,
        message: ROLES_MESSAGES.ROLE_NAME_EXISTS,
      };
    }

    return {
      isUnique: true,
      message: 'Role name is available',
    };
  }

  /**
   * Create a new role for the current tenant
   * @param createRoleDto The data for the new role
   * @returns The newly created role
   */
  async createRole(createRoleDto: CreateRoleDto): Promise<RoleSerializer> {
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!tenantId) {
      throw new BadRequestException(ORGANISATION_MESSAGES.TENANT_ID_REQUIRED);
    }

    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check if role with same name already exists for this tenant
      const existingRole = await this.rolesRepository.findOne({
        where: {
          name: createRoleDto.name,
          tenantId,
        },
      });

      if (existingRole) {
        throw new ConflictException(ROLES_MESSAGES.ROLE_NAME_EXISTS);
      }

      // Create new role entity
      const role = new Role();
      role.name = createRoleDto.name;
      role.description = createRoleDto.description;
      role.tenantId = tenantId;
      role.custom = true; // Tenant-created roles are always custom
      role.roleType = createRoleDto.roleType;

      // Save the role
      const savedRole = await queryRunner.manager.save(role);

      // Process permissions if they exist
      if (createRoleDto.permissions && createRoleDto.permissions.length > 0) {
        // Find permissions by name
        for (const permissionName of createRoleDto.permissions) {
          const permission =
            await this.permissionsRepository.findByName(permissionName);

          if (!permission) {
            throw new NotFoundException(
              PERMISSIONS_MESSAGES.PERMISSION_NOT_FOUND,
            );
          }

          // Create role-permission association
          const rolePermission = {
            roleId: savedRole.id,
            permissionId: permission.id,
          };

          await queryRunner.manager.save('role_permissions', rolePermission);
        }
      }

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Retrieve the role with its permissions
      const roleWithPermissions = await this.rolesRepository.findOne({
        where: { id: savedRole.id },
        relations: ['rolePermissions', 'rolePermissions.permission'],
      });

      if (!roleWithPermissions) {
        throw new Error(ROLES_MESSAGES.FAILED_TO_RETRIEVE_NEWLY_CREATED_ROLE);
      }

      return RoleSerializer.serialize(roleWithPermissions, true);
    } catch (error) {
      // Rollback the transaction if there's an error
      await queryRunner.rollbackTransaction();

      if (
        error instanceof ConflictException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }

      throw new BadRequestException(
        ROLES_MESSAGES.FAILED_TO_CREATE_ROLE + ' ' + error.message,
      );
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Delete a role by ID
   * @param id The ID of the role to delete
   * @returns An object indicating whether the role was successfully deleted
   */
  async deleteRole(id: string): Promise<DeleteRoleResultDto> {
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!tenantId) {
      throw new BadRequestException(ORGANISATION_MESSAGES.TENANT_ID_REQUIRED);
    }

    // Check if role exists
    const role = await this.rolesRepository.findOne({
      where: { id, tenantId },
    });

    if (!role) {
      throw new NotFoundException(ROLES_MESSAGES.ROLE_NOT_FOUND);
    }

    // Prevent deletion of system roles
    if (
      !role.custom ||
      role.name === SystemRoles.ADMIN ||
      role.name === SystemRoles.SUPER_ADMIN
    ) {
      throw new ForbiddenException(
        ROLES_MESSAGES.SYSTEM_ROLE_DELETION_NOT_ALLOWED,
      );
    }

    // Check if the role is assigned to any users
    const userRoleAssignments = await this.dataSource
      .getRepository(UserRoleAssignment)
      .find({ where: { roleId: id } });

    if (userRoleAssignments.length > 0) {
      throw new ForbiddenException(ROLES_MESSAGES.ROLE_ASSIGNED_TO_USERS);
    }

    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Delete role permissions first (due to foreign key constraints)
      await queryRunner.manager.delete('role_permissions', { roleId: id });

      // Delete user role assignments
      await queryRunner.manager.delete('user_role_assignments', { roleId: id });

      // Delete the role
      const deleteResult = await queryRunner.manager.delete('roles', { id });

      // Commit the transaction
      await queryRunner.commitTransaction();

      if (deleteResult.affected && deleteResult.affected > 0) {
        return {
          success: true,
          message: ROLES_MESSAGES.ROLE_DELETED_SUCCESSFULLY,
        };
      } else {
        return {
          success: false,
          message: ROLES_MESSAGES.FAILED_TO_DELETE_ROLE,
        };
      }
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();

      throw new BadRequestException(
        ROLES_MESSAGES.FAILED_TO_DELETE_ROLE + ': ' + error.message,
      );
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Get a role by ID with its permissions
   * @param id The ID of the role to fetch
   * @returns The role with its permissions
   */
  async getRoleById(id: string): Promise<RoleWithPermissionsSerializer> {
    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!tenantId) {
      throw new BadRequestException(ORGANISATION_MESSAGES.TENANT_ID_REQUIRED);
    }

    const role = await this.rolesRepository.findOne({
      where: { id },
      relations: ['rolePermissions', 'rolePermissions.permission'],
    });

    if (!role) {
      throw new NotFoundException(ROLES_MESSAGES.ROLE_NOT_FOUND);
    }

    // Use the new serializer that provides detailed permission information
    return RoleWithPermissionsSerializer.serialize(role);
  }

  /**
   * Update a role by ID
   * @param id The ID of the role to update
   * @param updateRoleDto The data to update the role with
   * @returns An object indicating whether the role was successfully updated
   */
  async updateRole(
    id: string,
    updateRoleDto: UpdateRoleDto,
  ): Promise<UpdateRoleResultDto> {
    // Check if any update data was provided
    if (
      !updateRoleDto.description &&
      !updateRoleDto.permissions &&
      !updateRoleDto.roleType
    ) {
      return {
        success: false,
        message: ROLES_MESSAGES.NO_CHANGES_DETECTED,
      };
    }

    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!tenantId) {
      throw new BadRequestException(ORGANISATION_MESSAGES.TENANT_ID_REQUIRED);
    }

    // Check if role exists
    const role = await this.rolesRepository.findOne({
      where: { id, tenantId },
      relations: ['rolePermissions', 'rolePermissions.permission'],
    });

    if (!role) {
      throw new NotFoundException(ROLES_MESSAGES.ROLE_NOT_FOUND);
    }

    // Prevent updates to system roles
    if (
      !role.custom ||
      role.name === SystemRoles.ADMIN ||
      role.name === SystemRoles.SUPER_ADMIN
    ) {
      throw new ForbiddenException(
        ROLES_MESSAGES.SYSTEM_ROLE_UPDATE_NOT_ALLOWED,
      );
    }

    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Update description if provided
      if (updateRoleDto.description !== undefined) {
        role.description = updateRoleDto.description;
      }

      // Update roleType if provided
      if (updateRoleDto.roleType !== undefined) {
        role.roleType = updateRoleDto.roleType;
      }

      // Save the role changes
      await queryRunner.manager.save(role);

      // Update permissions if provided
      if (updateRoleDto.permissions) {
        // Delete existing role permissions
        await queryRunner.manager.delete('role_permissions', { roleId: id });

        // Add new permissions
        for (const permissionName of updateRoleDto.permissions) {
          const permission =
            await this.permissionsRepository.findByName(permissionName);

          if (!permission) {
            throw new NotFoundException(
              PERMISSIONS_MESSAGES.PERMISSION_NOT_FOUND + ': ' + permissionName,
            );
          }

          // Create new role-permission association
          const rolePermission = {
            roleId: role.id,
            permissionId: permission.id,
          };

          await queryRunner.manager.save('role_permissions', rolePermission);
        }
      }

      // Commit the transaction
      await queryRunner.commitTransaction();

      return {
        success: true,
        message: ROLES_MESSAGES.ROLE_UPDATED_SUCCESSFULLY,
      };
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(
        ROLES_MESSAGES.FAILED_TO_UPDATE_ROLE + ': ' + error.message,
      );
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }
}
