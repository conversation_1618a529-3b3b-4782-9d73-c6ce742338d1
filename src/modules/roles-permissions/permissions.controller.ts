import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@hirenetix/authentication/jwt-auth.guard';
import { PermissionsService } from './permissions.service';
import { PermissionSerializer } from '@hirenetix/models/roles/serializers/permission.serializer';
import { UserPermissionsResponseDto } from '@hirenetix/models/roles/dto/user-permissions-response.dto';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  API_OPERATIONS,
  API_RESPONSES,
} from '../../common/constants/swagger.constants';
import { JWT_AUTH_NAME } from '../../common/constants/general.constants';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '@hirenetix/models/users/entities/user.entity';

@ApiTags('Permissions')
@Controller('permissions')
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) {}

  /**
   * Get all permissions in the system
   * Only admin or higher roles can access the list of permissions
   * @returns Array of all permissions
   */
  @Get()
  @ApiOperation({
    summary: API_OPERATIONS.GET_ALL_PERMISSIONS_IN_THE_SYSTEM,
  })
  @ApiResponse({
    status: 200,
    description: API_RESPONSES.LIST_OF_PERMISSIONS,
    type: [PermissionSerializer],
  })
  @ApiResponse({
    status: 403,
    description: API_RESPONSES.FORBIDDEN_INSUFFICIENT_PERMISSIONS,
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard)
  async getAllPermissions(): Promise<PermissionSerializer[]> {
    return await this.permissionsService.getAllPermissions();
  }

  /**
   * Get current user's permissions grouped by module
   * @returns Object with permissions grouped by module
   */
  @Get('my-permissions')
  @ApiOperation({
    summary: 'Get current user permissions grouped by module',
  })
  @ApiResponse({
    status: 200,
    description: 'User permissions grouped by module',
    type: UserPermissionsResponseDto,
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async getMyPermissions(
    @CurrentUser() user: User,
  ): Promise<UserPermissionsResponseDto> {
    return await this.permissionsService.getUserPermissionsByModule(user.id);
  }
}
