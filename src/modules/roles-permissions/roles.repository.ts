import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import { TenantAwareRepository } from '@hirenetix/modules/tenant/tenant-aware.repository';

@Injectable()
export class RolesRepository extends TenantAwareRepository<Role> {
  constructor(
    @InjectRepository(Role)
    protected readonly repository: Repository<Role>,
  ) {
    super();
  }

  async findById(id: string): Promise<Role | null> {
    return this.findOne({ where: { id } });
  }

  async findByTenantId(): Promise<Role[]> {
    return this.find({});
  }

  /**
   * Create a new role for the current tenant
   * @param role The role entity to create
   * @returns The created role entity
   */
  async createRoleByTenantId(role: Role): Promise<Role> {
    return this.repository.save(role);
  }
}
