import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  // ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { LANDING_PAGE_MESSAGES } from '../../common/constants/message.constants';
import { ContactResponseSerializer } from '../../models/users/serializers/contact-form.serializer';
import { SubmitContactFormDto } from '../../models/users/dto/contact-form.dto';
import { ContactService } from './contact.service';
import { ThrottlerGuard } from '@nestjs/throttler';

@ApiTags('Contact Form Response Handler')
@Controller('contact-form')
// @ApiBearerAuth()
export class ContactController {
  constructor(private readonly contactService: ContactService) {}

  @Post('submit')
  @UseGuards(ThrottlerGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle contact form submission' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: LANDING_PAGE_MESSAGES.CONTACT_RESPONSE_CREATED_SUCCESSFULLY,
    type: ContactResponseSerializer,
  })
  async submitContactForm(
    @Body() submitContactFormDto: SubmitContactFormDto,
  ): Promise<any> {
    return {
      message: LANDING_PAGE_MESSAGES.CONTACT_RESPONSE_CREATED_SUCCESSFULLY,
      data: await this.contactService.submitContactResponse(
        submitContactFormDto,
      ),
    };
  }
}
