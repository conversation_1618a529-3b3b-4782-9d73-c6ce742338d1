import { Modu<PERSON> } from '@nestjs/common';
import { ContactController } from './contact.controller';
import { ContactService } from './contact.service';
import { EmailModule } from '../../providers/mail/email.module';
import { EmailTemplateModule } from '../../providers/mail/templates/email-template.module';

@Module({
  imports: [EmailModule, EmailTemplateModule],
  controllers: [ContactController],
  providers: [ContactService],
  exports: [ContactService],
})
export class ContactModule {}
