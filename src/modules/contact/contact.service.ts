import { BadRequestException, Injectable } from '@nestjs/common';
import { SubmitContactFormDto } from '../../models/users/dto/contact-form.dto';
import { ContactResponseSerializer } from '../../models/users/serializers/contact-form.serializer';
import { EmailService } from '../../providers/mail/email.service';
import { EmailTemplateService } from '../../providers/mail/templates/email-template.service';
import { CaptchaHelper } from '../../common/helpers/captcha.helper';

@Injectable()
export class ContactService {
  constructor(
    private readonly emailService: EmailService,
    private readonly emailTemplateService: EmailTemplateService,
  ) {}

  async submitContactResponse(
    createContactResponseDto: SubmitContactFormDto,
  ): Promise<ContactResponseSerializer> {
    const contactResponse = {
      ...createContactResponseDto,
    };

    // Verify captcha
    const captchaVerified = CaptchaHelper.verifyCaptcha(
      createContactResponseDto.captcha,
      createContactResponseDto.captchaImageKey,
    );

    if (!captchaVerified) {
      throw new BadRequestException(
        'Captcha verification failed. Please try again.',
      );
    }

    await this.emailService.sendEmail(
      '<EMAIL>',
      'New Contact Form Submission',
      this.emailTemplateService.generateContactFormSubmissionEmail(
        contactResponse,
      ),
    );

    return ContactResponseSerializer.serialize(contactResponse);
  }
}
