import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { DepartmentsRepository } from './departments.repository';
import { DepartmentSerializer } from '@hirenetix/models/departments/serializers/department.serializer';
import { OrganisationsRepository } from '@hirenetix/modules/organisations/organisations.repository';
import {
  IDepartmentCreate,
  IDepartmentSearchQuery,
  IDepartmentUpdate,
} from '@hirenetix/models/departments/interfaces/department.interface';

@Injectable()
export class DepartmentsService {
  constructor(
    private readonly departmentsRepository: DepartmentsRepository,
    private readonly organisationsRepository: OrganisationsRepository,
  ) {}

  async findById(
    id: string,
    includeOrganisation = false,
  ): Promise<DepartmentSerializer> {
    const department = await this.departmentsRepository.findById(
      id,
      includeOrganisation,
    );

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    return DepartmentSerializer.serialize(department, includeOrganisation);
  }

  async find(
    query: IDepartmentSearchQuery,
    includeOrganisation = false,
  ): Promise<{ data: DepartmentSerializer[]; count: number }> {
    const [departments, count] = await this.departmentsRepository.find(
      query,
      includeOrganisation,
    );

    return {
      data: DepartmentSerializer.serializeMany(
        departments,
        includeOrganisation,
      ),
      count,
    };
  }

  async create(data: IDepartmentCreate): Promise<DepartmentSerializer> {
    // Check if organisation exists
    const organisation = await this.organisationsRepository.findById(
      data.organisationId,
    );
    if (!organisation) {
      throw new BadRequestException(
        `Organisation with ID ${data.organisationId} not found`,
      );
    }

    // Check if department with the same name already exists in the organisation
    const existingDepartment = await this.departmentsRepository.findByName(
      data.name,
      data.organisationId,
    );
    if (existingDepartment) {
      throw new ConflictException(
        `Department with name '${data.name}' already exists in this organisation`,
      );
    }

    const department = await this.departmentsRepository.create(data);

    return DepartmentSerializer.serialize(department);
  }

  async update(
    id: string,
    data: IDepartmentUpdate,
  ): Promise<DepartmentSerializer> {
    // Check if department exists
    const existingDepartment = await this.departmentsRepository.findById(
      id,
      true,
    );
    if (!existingDepartment) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    // Check if name is being updated and is not already in use in the same organisation
    if (data.name && data.name !== existingDepartment.name) {
      const departmentWithSameName =
        await this.departmentsRepository.findByName(
          data.name,
          existingDepartment.organisationId,
        );
      if (departmentWithSameName) {
        throw new ConflictException(
          `Department with name '${data.name}' already exists in this organisation`,
        );
      }
    }

    const updatedDepartment = await this.departmentsRepository.update(id, data);

    return DepartmentSerializer.serialize(updatedDepartment);
  }

  async delete(id: string): Promise<void> {
    // Check if department exists
    const existingDepartment = await this.departmentsRepository.findById(id);
    if (!existingDepartment) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    // Check if department has users
    const userCount =
      await this.departmentsRepository.countUsersInDepartment(id);
    if (userCount > 0) {
      throw new BadRequestException(
        `Cannot delete department that has ${userCount} users assigned to it`,
      );
    }

    const deleted = await this.departmentsRepository.delete(id);

    if (!deleted) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }
  }
}
