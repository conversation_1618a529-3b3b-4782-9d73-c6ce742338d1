import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Department } from '@hirenetix/models/departments/entities/department.entity';
import {
  IDepartmentCreate,
  IDepartmentUpdate,
  IDepartmentSearchQuery,
} from '@hirenetix/models/departments/interfaces/department.interface';

@Injectable()
export class DepartmentsRepository {
  constructor(
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
  ) {}

  async findById(id: string, relations = false): Promise<Department | null> {
    return this.departmentRepository.findOne({
      where: { id },
      relations: relations ? ['organisation', 'users'] : [],
    });
  }

  async findByName(
    name: string,
    organisationId: string,
  ): Promise<Department | null> {
    return this.departmentRepository.findOne({
      where: {
        name,
        organisationId,
      },
    });
  }

  async find(
    query: IDepartmentSearchQuery,
    relations = false,
  ): Promise<[Department[], number]> {
    const queryBuilder =
      this.departmentRepository.createQueryBuilder('department');

    if (relations) {
      queryBuilder.leftJoinAndSelect('department.organisation', 'organisation');
      queryBuilder.leftJoinAndSelect('department.users', 'users');
    }

    if (query.id) {
      queryBuilder.andWhere('department.id = :id', { id: query.id });
    }

    if (query.organisationId) {
      queryBuilder.andWhere('department.organisationId = :organisationId', {
        organisationId: query.organisationId,
      });
    }

    if (query.name) {
      queryBuilder.andWhere('department.name ILIKE :name', {
        name: `%${query.name}%`,
      });
    }

    return queryBuilder.getManyAndCount();
  }

  async create(data: IDepartmentCreate): Promise<Department> {
    const department = new Department();

    department.organisationId = data.organisationId;
    department.name = data.name;
    department.description = data.description;

    return this.departmentRepository.save(department);
  }

  async update(
    id: string,
    data: IDepartmentUpdate,
  ): Promise<Department | null> {
    const department = await this.findById(id);

    if (!department) {
      return null;
    }

    if (data.name) {
      department.name = data.name;
    }

    if (data.description !== undefined) {
      department.description = data.description;
    }

    return this.departmentRepository.save(department);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.departmentRepository.delete(id);
    return result.affected > 0;
  }

  async countUsersInDepartment(id: string): Promise<number> {
    const department = await this.departmentRepository.findOne({
      where: { id },
      relations: ['users'],
    });

    return department?.users?.length || 0;
  }
}
