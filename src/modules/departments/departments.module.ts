import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Department } from '@hirenetix/models/departments/entities/department.entity';
import { DepartmentsRepository } from './departments.repository';
import { DepartmentsService } from './departments.service';
import { DepartmentsController } from './departments.controller';
import { OrganisationsModule } from '@hirenetix/modules/organisations/organisations.module';

@Module({
  imports: [TypeOrmModule.forFeature([Department]), OrganisationsModule],
  providers: [DepartmentsRepository, DepartmentsService],
  controllers: [DepartmentsController],
  exports: [DepartmentsService, DepartmentsRepository],
})
export class DepartmentsModule {}
