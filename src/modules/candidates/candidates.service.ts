import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CandidateRepository } from '../../models/candidates/repositories/candidate.repository';
import { CreateCandidateDto } from '../../models/candidates/dto/create-candidate.dto';
import { CANDIDATE_MESSAGES } from '../../common/constants/message.constants';
import { UpdateCandidateDto } from '../../models/candidates/dto/update-candidate.dto';
import { CandidateSerializer } from '../../models/candidates/serializers/candidate.serializer';
import { CandidateSearchDto } from '../../models/candidates/dto/candidate-search.dto';
import { PaginatedCandidatesResponseDto } from '../../models/candidates/dto/paginated-candidates-response.dto';

@Injectable()
export class CandidatesService {
  constructor(private readonly candidateRepository: CandidateRepository) {}

  async createCandidate(
    createCandidateDto: CreateCandidateDto,
  ): Promise<CandidateSerializer> {
    // Check if candidate with same email exists
    const existingEmail = await this.candidateRepository.findByEmail(
      createCandidateDto.email,
    );
    if (existingEmail) {
      throw new BadRequestException(
        CANDIDATE_MESSAGES.CANDIDATE_EMAIL_ALREADY_EXISTS,
      );
    }

    // Create new candidate
    const candidate = await this.candidateRepository.create({
      ...createCandidateDto,
    });

    const savedCandidate = await this.candidateRepository.findById(
      candidate.id,
    );
    return CandidateSerializer.serialize(savedCandidate);
  }

  async updateCandidate(
    id: string,
    updateCandidateDto: UpdateCandidateDto,
  ): Promise<CandidateSerializer> {
    const candidate = await this.candidateRepository.findById(id);
    if (!candidate) {
      throw new NotFoundException(CANDIDATE_MESSAGES.CANDIDATE_NOT_FOUND);
    }

    const updatedCandidate = await this.candidateRepository.update(
      id,
      updateCandidateDto,
    );
    return CandidateSerializer.serialize(updatedCandidate);
  }

  async deleteCandidate(id: string): Promise<void> {
    const candidate = await this.candidateRepository.findById(id);
    if (!candidate) {
      throw new NotFoundException(CANDIDATE_MESSAGES.CANDIDATE_NOT_FOUND);
    }

    await this.candidateRepository.delete(id);
  }

  async getAllCandidates(
    searchParams: CandidateSearchDto,
  ): Promise<PaginatedCandidatesResponseDto> {
    const { page = 1, limit = 10 } = searchParams;
    const [candidates, total] = await this.candidateRepository.findAll(
      page,
      limit,
    );

    const totalPages = Math.ceil(total / limit);

    return {
      data: CandidateSerializer.serializeMany(candidates),
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async getCandidateById(id: string): Promise<CandidateSerializer> {
    const candidate = await this.candidateRepository.findById(id);
    if (!candidate) {
      throw new NotFoundException(CANDIDATE_MESSAGES.CANDIDATE_NOT_FOUND);
    }
    return CandidateSerializer.serialize(candidate);
  }
}
