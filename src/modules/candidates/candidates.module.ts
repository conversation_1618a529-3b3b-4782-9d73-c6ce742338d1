import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CandidatesController } from './candidates.controller';
import { CandidatesService } from './candidates.service';
import { Candidate } from '../../models/candidates/entities/candidate.entity';
import { CandidateRepository } from '../../models/candidates/repositories/candidate.repository';
import { RolesModule } from '../roles-permissions/roles.module';

@Module({
  imports: [TypeOrmModule.forFeature([Candidate]), RolesModule],
  controllers: [CandidatesController],
  providers: [CandidatesService, CandidateRepository],
  exports: [CandidatesService, CandidateRepository],
})
export class CandidatesModule {}
