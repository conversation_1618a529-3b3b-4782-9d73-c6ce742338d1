import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CandidatesService } from './candidates.service';
import { CreateCandidateDto } from '../../models/candidates/dto/create-candidate.dto';
import { UpdateCandidateDto } from '../../models/candidates/dto/update-candidate.dto';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { PermissionActions } from '../../models/roles/constants/role.constants';
import { JwtAuthGuard } from '../../authentication/jwt-auth.guard';
import { RequirePermissions } from '../../common/decorators/metadata/permissions.decorator';
import { PermissionModules } from '../../models/roles/constants/role.constants';
import { CandidateSerializer } from '../../models/candidates/serializers/candidate.serializer';
import { CandidateSearchDto } from '../../models/candidates/dto/candidate-search.dto';
import { PaginatedCandidatesResponseDto } from '../../models/candidates/dto/paginated-candidates-response.dto';
import { CANDIDATE_MESSAGES } from '../../common/constants/message.constants';
import { CandidateStatus } from '../../models/candidates/entities/candidate.entity';

@ApiTags('Candidates')
@Controller('candidates')
@ApiBearerAuth()
export class CandidatesController {
  constructor(private readonly candidatesService: CandidatesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new candidate' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: CANDIDATE_MESSAGES.CANDIDATE_CREATED_SUCCESSFULLY,
    type: CandidateSerializer,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: CANDIDATE_MESSAGES.CANDIDATE_EMAIL_ALREADY_EXISTS,
  })
  async createCandidate(
    @Body() createCandidateDto: CreateCandidateDto,
  ): Promise<CandidateSerializer> {
    return this.candidatesService.createCandidate(createCandidateDto);
  }

  @Get()
  @RequirePermissions({
    module: PermissionModules.USERS,
    action: PermissionActions.VIEW_ALL_USERS,
  })
  @ApiOperation({ summary: 'Get all candidates' })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search candidates by name or email',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: CandidateStatus,
    description: 'Filter candidates by status',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 10)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns paginated candidates.',
    type: PaginatedCandidatesResponseDto,
  })
  async getAllCandidates(
    @Query() searchParams: CandidateSearchDto,
  ): Promise<PaginatedCandidatesResponseDto> {
    return this.candidatesService.getAllCandidates(searchParams);
  }

  @Get(':id')
  @RequirePermissions({
    module: PermissionModules.USERS,
    action: PermissionActions.VIEW_ALL_USERS,
  })
  @ApiOperation({ summary: 'Get a candidate by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns the candidate.',
    type: CandidateSerializer,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: CANDIDATE_MESSAGES.CANDIDATE_NOT_FOUND,
  })
  async getCandidateById(
    @Param('id') id: string,
  ): Promise<CandidateSerializer> {
    return this.candidatesService.getCandidateById(id);
  }

  @Put(':id')
  @RequirePermissions({
    module: PermissionModules.USERS,
    action: PermissionActions.EDIT_USER,
  })
  @ApiOperation({ summary: 'Update a candidate' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: CANDIDATE_MESSAGES.CANDIDATE_UPDATED_SUCCESSFULLY,
    type: CandidateSerializer,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: CANDIDATE_MESSAGES.CANDIDATE_NOT_FOUND,
  })
  async updateCandidate(
    @Param('id') id: string,
    @Body() updateCandidateDto: UpdateCandidateDto,
  ): Promise<CandidateSerializer> {
    return this.candidatesService.updateCandidate(id, updateCandidateDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions({
    module: PermissionModules.USERS,
    action: PermissionActions.DELETE_USER,
  })
  @ApiOperation({ summary: 'Delete a candidate' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: CANDIDATE_MESSAGES.CANDIDATE_DELETED_SUCCESSFULLY,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: CANDIDATE_MESSAGES.CANDIDATE_NOT_FOUND,
  })
  async deleteCandidate(@Param('id') id: string): Promise<void> {
    return this.candidatesService.deleteCandidate(id);
  }
}
