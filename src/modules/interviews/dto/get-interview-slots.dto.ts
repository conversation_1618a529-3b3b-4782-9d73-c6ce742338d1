import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsDate } from 'class-validator';
import { Type } from 'class-transformer';

export class GetInterviewSlotsDto {
  @ApiProperty({
    description: 'Start date for filtering slots',
    example: '2024-03-20',
    required: false,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDate?: Date;

  @ApiProperty({
    description: 'End date for filtering slots',
    example: '2024-03-25',
    required: false,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDate?: Date;

  adjustDates(): void {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset to midnight for accurate comparison
    if (this.startDate && this.startDate < today) {
      this.startDate = today;
    }
    if (this.endDate && this.endDate < today) {
      this.endDate = today;
    }
    if (this.startDate && this.endDate && this.startDate > this.endDate) {
      this.endDate = this.startDate;
    }
  }
}
