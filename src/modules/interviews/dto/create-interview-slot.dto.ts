import {
  IsArray,
  IsDate,
  IsNotEmpty,
  ValidateNested,
  Validate,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  IsBoolean,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TimeSlotDto } from './time-slot.dto';
import { ApiProperty } from '@nestjs/swagger';
import * as moment from 'moment';

@ValidatorConstraint({ name: 'isFutureDate', async: false })
class FutureDateValidator implements ValidatorConstraintInterface {
  validate(date: Date) {
    const today = moment().utc().startOf('day');
    return moment(date).isAfter(today);
  }

  defaultMessage() {
    return 'Date must be a future date';
  }
}

export class InterviewModesDto {
  @ApiProperty({
    description: 'Whether phone interviews are available',
    example: true,
  })
  @IsBoolean()
  phone: boolean;

  @ApiProperty({
    description: 'Whether video interviews are available',
    example: true,
  })
  @IsBoolean()
  video: boolean;

  @ApiProperty({
    description: 'Whether in-person interviews are available',
    example: true,
  })
  @IsBoolean()
  in_person: boolean;
}

@ValidatorConstraint({ name: 'noOverlappingTimeSlots', async: false })
class NoOverlappingTimeSlotsValidator implements ValidatorConstraintInterface {
  validate(timeSlots: TimeSlotDto[]): boolean {
    if (!Array.isArray(timeSlots)) return false;

    // Convert each slot to minutes from midnight
    const toMinutes = (time: string) => {
      const [hours, minutes] = time.split(':').map(Number);
      return hours * 60 + minutes;
    };

    // Sort by start time
    const sortedSlots = timeSlots
      .map((slot) => ({
        start: toMinutes(slot.startTime),
        end: toMinutes(slot.endTime),
      }))
      .sort((a, b) => a.start - b.start);

    for (let i = 0; i < sortedSlots.length - 1; i++) {
      const current = sortedSlots[i];
      const next = sortedSlots[i + 1];

      if (current.end > next.start) {
        return false; // Overlap found
      }
    }

    return true; // No overlaps
  }

  defaultMessage() {
    return 'Time slots cannot overlap';
  }
}

export class CreateInterviewSlotDto {
  @ApiProperty({
    description: 'Date for the interview slots (must be today or future date)',
    example: '2024-03-20',
  })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  @Validate(FutureDateValidator)
  date: Date;

  @ApiProperty({
    description: 'Array of time slots for interviews',
    type: [TimeSlotDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TimeSlotDto)
  @Validate(NoOverlappingTimeSlotsValidator)
  timeSlots: TimeSlotDto[];

  @ApiProperty({
    description: 'Available interview modes',
    type: InterviewModesDto,
    example: {
      phone: true,
      video: true,
      in_person: true,
    },
  })
  @IsObject()
  @ValidateNested()
  @Type(() => InterviewModesDto)
  interviewModes: InterviewModesDto;
}
