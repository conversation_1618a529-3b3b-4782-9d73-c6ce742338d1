import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class TimeSlotDto {
  @ApiProperty({
    description: 'Start time of the interview slot in HH:mm format',
    example: '10:00',
  })
  @IsString()
  @IsNotEmpty()
  startTime: string;

  @ApiProperty({
    description: 'End time of the interview slot in HH:mm format',
    example: '11:00',
  })
  @IsString()
  @IsNotEmpty()
  endTime: string;
}
