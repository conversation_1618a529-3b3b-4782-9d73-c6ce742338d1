import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseU<PERSON><PERSON>ipe,
  HttpCode,
  HttpStatus,
  UseGuards,
  Request,
} from '@nestjs/common';
import { InterviewSlotsService } from './interview-slots.service';
import { CreateInterviewSlotDto } from './dto/create-interview-slot.dto';
import { UpdateInterviewSlotDto } from './dto/update-interview-slot.dto';
import { GetInterviewSlotsDto } from './dto/get-interview-slots.dto';
import { DeleteInterviewSlotsDto } from './dto/delete-interview-slots.dto';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { JwtAuthGuard } from '../../authentication/jwt-auth.guard';
import { RequirePermissions } from '../../common/decorators/metadata/permissions.decorator';
import { PermissionActions } from '../../models/roles/constants/role.constants';
import { PermissionModules } from '../../models/roles/constants/role.constants';
import { InterviewSlotResponse } from './interfaces/interview-slot.interface';
import { InterviewSlotResponseSerializer } from './serializers/interview-slot.serializer';
import { ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Interview Slots')
@UseGuards(JwtAuthGuard, PermissionsGuard)
@Controller('interview-slots')
export class InterviewSlotsController {
  constructor(private readonly interviewSlotsService: InterviewSlotsService) {}

  @Post()
  @RequirePermissions({
    action: PermissionActions.CREATE_JOB_APPLICATIONS,
    module: PermissionModules.JOBS,
  })
  create(
    @Body() createInterviewSlotDto: CreateInterviewSlotDto,
    @Request() req,
  ): Promise<InterviewSlotResponse> {
    return this.interviewSlotsService.create(
      createInterviewSlotDto,
      req.user.id,
    );
  }

  @Get()
  @RequirePermissions({
    action: PermissionActions.CREATE_JOB_APPLICATIONS,
    module: PermissionModules.JOBS,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns available interview slots',
    type: InterviewSlotResponseSerializer,
  })
  findAll(
    @Query() query: GetInterviewSlotsDto,
  ): Promise<InterviewSlotResponseSerializer> {
    return this.interviewSlotsService.findAll(query);
  }

  @Get(':id')
  @RequirePermissions({
    action: PermissionActions.CREATE_JOB_APPLICATIONS,
    module: PermissionModules.JOBS,
  })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.interviewSlotsService.findOne(id);
  }

  @Patch(':id')
  @RequirePermissions({
    action: PermissionActions.CREATE_JOB_APPLICATIONS,
    module: PermissionModules.JOBS,
  })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateInterviewSlotDto: UpdateInterviewSlotDto,
  ) {
    return this.interviewSlotsService.update(id, updateInterviewSlotDto);
  }

  @Delete(':id')
  @RequirePermissions({
    action: PermissionActions.CREATE_JOB_APPLICATIONS,
    module: PermissionModules.JOBS,
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.interviewSlotsService.remove(id);
  }

  @Post(':id/book')
  @RequirePermissions({
    action: PermissionActions.CREATE_JOB_APPLICATIONS,
    module: PermissionModules.JOBS,
  })
  bookSlot(@Param('id', ParseUUIDPipe) id: string) {
    return this.interviewSlotsService.bookSlot(id);
  }

  @Post('delete-by-date')
  @RequirePermissions({
    action: PermissionActions.CREATE_JOB_APPLICATIONS,
    module: PermissionModules.JOBS,
  })
  @ApiResponse({
    status: 200,
    description: 'Deletes all unbooked interview slots for a specific date',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Interview slots deleted successfully',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  deleteByDate(@Body() deleteDto: DeleteInterviewSlotsDto) {
    return this.interviewSlotsService.deleteByDate(deleteDto);
  }
}
