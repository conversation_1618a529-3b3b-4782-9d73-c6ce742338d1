import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InterviewSlot } from '../../models/interviews/entities/interview-slot.entity';
import { InterviewSlotRepository } from '../../models/interviews/repositories/interview-slot.repository';
import { InterviewSlotsController } from './interview-slots.controller';
import { InterviewSlotsService } from './interview-slots.service';
import { RolesModule } from '../roles-permissions/roles.module';

@Module({
  imports: [TypeOrmModule.forFeature([InterviewSlot]), RolesModule],
  controllers: [InterviewSlotsController],
  providers: [InterviewSlotsService, InterviewSlotRepository],
  exports: [InterviewSlotsService],
})
export class InterviewSlotsModule {}
