import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InterviewSlotRepository } from '../../models/interviews/repositories/interview-slot.repository';
import { InterviewSlot } from '../../models/interviews/entities/interview-slot.entity';
import { CreateInterviewSlotDto } from './dto/create-interview-slot.dto';
import { UpdateInterviewSlotDto } from './dto/update-interview-slot.dto';
import { InterviewSlotResponse } from './interfaces/interview-slot.interface';
import { GetInterviewSlotsDto } from './dto/get-interview-slots.dto';
import { DeleteInterviewSlotsDto } from './dto/delete-interview-slots.dto';
import { Between, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';

@Injectable()
export class InterviewSlotsService {
  constructor(
    private readonly interviewSlotRepository: InterviewSlotRepository,
  ) {}

  async create(
    createDto: CreateInterviewSlotDto,
    userId: string,
  ): Promise<InterviewSlotResponse> {
    const existingSlots = await this.interviewSlotRepository.find({
      where: {
        date: createDto.date,
      },
    });

    if (existingSlots.length > 0) {
      const bookedSlots = existingSlots.filter((slot) => slot.isBooked);
      if (bookedSlots.length > 0) {
        throw new BadRequestException(
          'Cannot modify date with booked interview slots',
        );
      }

      await this.interviewSlotRepository.delete({
        date: createDto.date,
        isBooked: false,
      });
    }

    const slots = createDto.timeSlots.map((timeSlot) =>
      this.interviewSlotRepository.create({
        date: createDto.date,
        startTime: timeSlot.startTime,
        endTime: timeSlot.endTime,
        interviewModes: {
          phone: createDto.interviewModes.phone,
          video: createDto.interviewModes.video,
          in_person: createDto.interviewModes.in_person,
        },
        userId,
      }),
    );

    const savedSlots = await this.interviewSlotRepository.save(slots);

    return {
      success: true,
      message: 'Interview slots created successfully',
      data: {
        date: createDto.date,
        timeSlots: savedSlots,
        interviewModes: createDto.interviewModes,
      },
    };
  }

  async findAll(query: GetInterviewSlotsDto): Promise<any> {
    query.adjustDates();
    const { startDate, endDate } = query;
    let dateFilter = {};

    if (startDate && endDate) {
      dateFilter = { date: Between(startDate, endDate) };
    } else if (startDate) {
      dateFilter = { date: MoreThanOrEqual(startDate) };
    } else if (endDate) {
      dateFilter = { date: LessThanOrEqual(endDate) };
    }

    const slots = await this.interviewSlotRepository.find({
      where: dateFilter,
      order: {
        date: 'DESC',
        startTime: 'ASC',
      },
    });

    const grouped: Record<
      string,
      {
        date: string;
        timeSlots: { id: string; start: string; end: string }[];
        interviewModes: { phone: boolean; video: boolean; in_person: boolean };
      }
    > = {};

    for (const slot of slots) {
      const { date, startTime, endTime, interviewModes } = slot;

      // Ensure consistent key format for grouping
      const isoDate = new Date(date).toISOString();

      if (!grouped[isoDate]) {
        grouped[isoDate] = {
          date: isoDate,
          timeSlots: [],
          interviewModes: { phone: false, video: false, in_person: false },
        };
      }

      // Push time slot
      grouped[isoDate].timeSlots.push({
        id: slot.id,
        start: startTime.slice(0, 5),
        end: endTime.slice(0, 5),
      });

      // Merge interview modes
      grouped[isoDate].interviewModes.phone ||= interviewModes.phone;
      grouped[isoDate].interviewModes.video ||= interviewModes.video;
      grouped[isoDate].interviewModes.in_person ||= interviewModes.in_person;
    }

    console.log('grouped :', grouped);
    return {
      success: true,
      message: 'Interview slots retrieved successfully',
      data: Object.values(grouped),
    };
  }

  async findOne(id: string): Promise<InterviewSlot> {
    const slot = await this.interviewSlotRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!slot) {
      throw new NotFoundException(`Interview slot with ID ${id} not found`);
    }

    return slot;
  }

  async update(
    id: string,
    updateDto: UpdateInterviewSlotDto,
  ): Promise<InterviewSlot> {
    const slot = await this.findOne(id);
    Object.assign(slot, updateDto);
    return this.interviewSlotRepository.save(slot);
  }

  async remove(id: string): Promise<void> {
    const slot = await this.findOne(id);
    await this.interviewSlotRepository.remove(slot);
  }

  async bookSlot(id: string): Promise<InterviewSlot> {
    const slot = await this.findOne(id);

    if (slot.isBooked) {
      throw new Error('This slot is already booked');
    }

    slot.isBooked = true;
    return this.interviewSlotRepository.save(slot);
  }

  async deleteByDate(
    deleteDto: DeleteInterviewSlotsDto,
  ): Promise<{ success: boolean; message: string }> {
    const existingSlots = await this.interviewSlotRepository.find({
      where: {
        date: deleteDto.date,
      },
    });

    if (existingSlots.length === 0) {
      throw new NotFoundException(
        'No interview slots found for the specified date',
      );
    }

    const bookedSlots = existingSlots.filter((slot) => slot.isBooked);
    if (bookedSlots.length > 0) {
      throw new BadRequestException(
        'Cannot delete date with booked interview slots',
      );
    }

    await this.interviewSlotRepository.delete({
      date: deleteDto.date,
      isBooked: false,
    });

    return {
      success: true,
      message: 'Interview slots deleted successfully',
    };
  }
}
