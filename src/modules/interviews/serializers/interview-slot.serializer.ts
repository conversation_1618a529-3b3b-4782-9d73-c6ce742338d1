import { ApiProperty, getSchemaPath } from '@nestjs/swagger';

export class InterviewModeSerializer {
  @ApiProperty({ example: true })
  phone: boolean;

  @ApiProperty({ example: true })
  video: boolean;

  @ApiProperty({ example: true })
  in_person: boolean;
}

export class InterviewSlotSerializer {
  @ApiProperty({ example: 'uuid-1' })
  id: string;

  @ApiProperty({ example: '09:00' })
  startTime: string;

  @ApiProperty({ example: '10:00' })
  endTime: string;

  @ApiProperty({ type: InterviewModeSerializer })
  interviewModes: InterviewModeSerializer;

  @ApiProperty({ example: false })
  isBooked: boolean;
}

export class InterviewSlotResponseSerializer {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Interview slots retrieved successfully' })
  message: string;

  @ApiProperty({
    type: 'object',
    properties: {
      startDate: { type: 'string', format: 'date', example: '2024-03-20' },
      endDate: { type: 'string', format: 'date', example: '2024-03-25' },
      timeSlots: {
        type: 'array',
        items: { $ref: getSchemaPath(InterviewSlotSerializer) },
      },
    },
  })
  data: {
    startDate: Date;
    endDate: Date;
    timeSlots: InterviewSlotSerializer[];
  };
}
