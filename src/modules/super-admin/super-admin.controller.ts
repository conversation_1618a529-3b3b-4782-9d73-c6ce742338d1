import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>Auth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@hirenetix/authentication/jwt-auth.guard';
import { RolesGuard } from '@hirenetix/common/guards/roles.guard';
import { SystemRoles } from '@hirenetix/models/roles/constants/role.constants';
import { Roles } from '@hirenetix/common/decorators/metadata/roles.decorator';
import { OrganisationSerializer } from '@hirenetix/models/organisations/serializers/organisation.serializer';
import { IOrganisationCreate } from '@hirenetix/models/organisations/interfaces/organisation.interface';
import { CreateOrganisationDto } from '@hirenetix/models/organisations/dto/create-organisation.dto';
import { OrganisationsService } from '@hirenetix/modules/organisations/organisations.service';
import { IUserCreate } from '@hirenetix/models/users/interfaces/user.interface';
import { UserSerializer } from '@hirenetix/models/users/serializers/user.serializer';
import { UsersService } from '@hirenetix/modules/users/users.service';
import { CreateAdminUserDto } from '../../models/users/dto/create-admin-user.dto';
import { JWT_AUTH_NAME } from '../../common/constants/general.constants';

@Controller('super-admin')
export class SuperAdminController {
  constructor(
    private readonly organisationsService: OrganisationsService,
    private readonly usersService: UsersService,
  ) {}

  /**
   * Create organisation - Only super_admin can access this endpoint.
   * This API is protected and only users with the super_admin role can create organisations
   */
  @Post('organisation')
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(SystemRoles.SUPER_ADMIN)
  @HttpCode(HttpStatus.CREATED)
  async createBySuperAdmin(
    @Body() createOrganisationDto: CreateOrganisationDto,
  ): Promise<OrganisationSerializer> {
    const organisationData: IOrganisationCreate = {
      name: createOrganisationDto.name,
      description: createOrganisationDto.description,
      address: createOrganisationDto.address,
    };

    return this.organisationsService.create(organisationData);
  }

  /**
   * Create admin user - Only super_admin users can create users with admin role
   * Admin users must have an organisation ID
   */
  @Post('user')
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(SystemRoles.SUPER_ADMIN)
  @HttpCode(HttpStatus.CREATED)
  async createUser(
    @Body() createUserDto: CreateAdminUserDto,
  ): Promise<UserSerializer> {
    // Convert DTO to the interface expected by the service
    const userData: IUserCreate = {
      fullName: createUserDto.fullName,
      email: createUserDto.email,
      tenantId: createUserDto.tenantId,
      roleId: createUserDto.roleId,
    };

    return this.usersService.createUserWithRole(userData);
  }
}
