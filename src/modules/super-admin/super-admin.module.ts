import { Module } from '@nestjs/common';
import { SuperAdminController } from './super-admin.controller';
import { OrganisationsModule } from '@hirenetix/modules/organisations/organisations.module';
import { RolesModule } from '@hirenetix/models/roles/roles.module';
import { UsersModule } from '@hirenetix/modules/users/users.module';

@Module({
  imports: [OrganisationsModule, RolesModule, UsersModule],
  controllers: [SuperAdminController],
})
export class SuperAdminModule {}
