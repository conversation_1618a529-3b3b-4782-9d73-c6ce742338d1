import { Modu<PERSON> } from '@nestjs/common';
import { JobPostsModule } from './job-posts/job-posts.module';
import { JobTemplatesModule } from './job-templates/job-templates.module';
import { SectionDefinitionsModule } from './section-definitions/section-definitions.module';
import { ConversationalJobFlowModule } from './conversational-job-flow/conversational-job-flow.module';

@Module({
  imports: [
    JobPostsModule,
    JobTemplatesModule,
    SectionDefinitionsModule,
    ConversationalJobFlowModule,
  ],
  exports: [
    JobPostsModule,
    JobTemplatesModule,
    SectionDefinitionsModule,
    ConversationalJobFlowModule,
  ],
})
export class JobsModule {}
