import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobApplication } from '../../../models/jobs/entities/job-application.entity';
import { JobApplicationRepository } from '../../../models/jobs/repositories/job-application.repository';
import { JobApplicationsController } from './job-applications.controller';
import { JobApplicationsService } from './job-applications.service';
import { RolesModule } from '../../roles-permissions/roles.module';
import { TenantModule } from '../../tenant/tenant.module';
import { Message } from '../../../models/conversations/entities/message.entity';
import { MessageRepository } from '../../../models/conversations/repositories/message.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([JobApplication, Message]),
    RolesModule,
    TenantModule,
  ],
  controllers: [JobApplicationsController],
  providers: [
    JobApplicationsService,
    JobApplicationRepository,
    MessageRepository,
  ],
  exports: [JobApplicationsService, JobApplicationRepository],
})
export class JobApplicationsModule {}
