import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { JobApplicationStatus } from '../../../../models/jobs/entities/job-application.entity';

export class UpdateJobApplicationDto {
  @ApiProperty({
    enum: JobApplicationStatus,
    description: 'New status for the job application',
  })
  @IsNotEmpty()
  @IsEnum(JobApplicationStatus, {
    message:
      'Status must be one of: in_review, applied, incomplete, interview_scheduled, offered, rejected, hired',
  })
  status: JobApplicationStatus;
}
