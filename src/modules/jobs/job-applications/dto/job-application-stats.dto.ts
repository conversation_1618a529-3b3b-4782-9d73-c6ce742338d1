import { ApiProperty } from '@nestjs/swagger';
import { JobApplicationStatus } from '../../../../models/jobs/entities/job-application.entity';

export class JobApplicationStatsDto {
  @ApiProperty({
    description: 'Status of the job application',
    enum: JobApplicationStatus,
    example: JobApplicationStatus.INCOMPLETE,
  })
  status: JobApplicationStatus;

  @ApiProperty({
    description: 'Name of the job application',
    example: '<PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'Count of applications with this status',
    example: 5,
  })
  count: number;
}

export class JobApplicationStatsResponseDto {
  @ApiProperty({ type: [JobApplicationStatsDto] })
  statusCounts: JobApplicationStatsDto[];

  @ApiProperty()
  totalApplications: number;
}
