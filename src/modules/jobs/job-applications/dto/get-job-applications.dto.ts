import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsEnum,
  IsDateString,
  IsInt,
  Min,
  IsIn,
} from 'class-validator';
import { JobApplicationStatus } from '../../../../models/jobs/entities/job-application.entity';
import { Transform } from 'class-transformer';

const ALLOWED_SORT_FIELDS = ['appliedDate', 'status'] as const;
type SortField = (typeof ALLOWED_SORT_FIELDS)[number];

export class JobApplicationQueryDto {
  @ApiProperty({ required: false })
  @IsOptional()
  search?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({ required: false, enum: JobApplicationStatus })
  @IsOptional()
  @IsEnum(JobApplicationStatus)
  status?: JobApplicationStatus;

  @ApiProperty({
    required: false,
    enum: ALLOWED_SORT_FIELDS,
    default: 'appliedDate',
  })
  @IsOptional()
  @IsIn(ALLOWED_SORT_FIELDS)
  @Transform(({ value }) => value || 'appliedDate')
  sortBy?: SortField = 'appliedDate';

  @ApiProperty({ required: false, enum: ['ASC', 'DESC'], default: 'DESC' })
  @IsOptional()
  @Transform(({ value }) => value || 'DESC')
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  @ApiProperty({ required: false, default: 1, minimum: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value) || 1)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ required: false, default: 10, minimum: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value) || 10)
  @IsInt()
  @Min(1)
  limit?: number = 10;
}

export class JobApplicationResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  candidateId: number;

  @ApiProperty()
  candidateName: string;

  @ApiProperty()
  jobTitle: string;

  @ApiProperty()
  jobLocation: string;

  @ApiProperty()
  appliedDate: Date;

  @ApiProperty()
  candidatePhone: string;

  @ApiProperty()
  candidateEmail: string;

  @ApiProperty({ enum: JobApplicationStatus })
  status: JobApplicationStatus;
}

export class PaginatedJobApplicationResponseDto {
  @ApiProperty({ type: [JobApplicationResponseDto] })
  data: JobApplicationResponseDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  totalPages: number;
}
