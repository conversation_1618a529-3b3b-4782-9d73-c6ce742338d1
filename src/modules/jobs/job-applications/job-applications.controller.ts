import {
  Controller,
  Get,
  Query,
  Patch,
  Param,
  Body,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JobApplicationsService } from './job-applications.service';
import {
  JobApplicationQueryDto,
  PaginatedJobApplicationResponseDto,
} from './dto/get-job-applications.dto';
import { UpdateJobApplicationDto } from './dto/update-job-application.dto';
import { JobApplicationStatsResponseDto } from './dto/job-application-stats.dto';
import { JwtAuthGuard } from '../../../authentication/jwt-auth.guard';
import { PermissionsGuard } from '../../../common/guards/permissions.guard';
import { RequirePermissions } from '../../../common/decorators/metadata/permissions.decorator';
import {
  PermissionModules,
  PermissionActions,
} from '../../../models/roles/constants/role.constants';
import { TenantContextStorage } from '../../tenant/tenant-context';
import { JobApplicationDetailsSerializer } from '../../../models/jobs/serializers/job-application-details.serializer';

@ApiTags('Job Applications')
@Controller('job-applications')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class JobApplicationsController {
  constructor(
    private readonly jobApplicationsService: JobApplicationsService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get paginated job applications with search, filter and sort',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated job applications',
    type: PaginatedJobApplicationResponseDto,
  })
  @RequirePermissions({
    module: PermissionModules.CANDIDATES,
    action: PermissionActions.VIEW_JOB_APPLICATIONS,
  })
  async getJobApplications(
    @Query() query: JobApplicationQueryDto,
  ): Promise<PaginatedJobApplicationResponseDto> {
    return this.jobApplicationsService.getJobApplications(query);
  }

  @Patch(':id/status')
  @ApiOperation({
    summary: 'Update job application status',
  })
  @ApiResponse({
    status: 200,
    description: 'Job application status updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Job application not found',
  })
  @RequirePermissions({
    module: PermissionModules.CANDIDATES,
    action: PermissionActions.EDIT_JOB_APPLICATIONS,
  })
  async updateStatus(
    @Param('id') id: string,
    @Body() updateDto: UpdateJobApplicationDto,
  ): Promise<void> {
    await this.jobApplicationsService.updateJobApplicationStatus(id, updateDto);
  }

  @Get('stats')
  @ApiOperation({
    summary: 'Get job application statistics',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns job application statistics',
    type: JobApplicationStatsResponseDto,
  })
  @RequirePermissions({
    module: PermissionModules.CANDIDATES,
    action: PermissionActions.VIEW_JOB_APPLICATIONS,
  })
  async getApplicationStats(): Promise<JobApplicationStatsResponseDto> {
    const organisationId = TenantContextStorage.getCurrentTenantId();
    return this.jobApplicationsService.getApplicationStats(organisationId);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get job application details by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns job application details',
    type: JobApplicationDetailsSerializer,
  })
  @RequirePermissions({
    module: PermissionModules.CANDIDATES,
    action: PermissionActions.VIEW_JOB_APPLICATIONS,
  })
  async getJobApplicationDetails(
    @Param('id') id: string,
  ): Promise<JobApplicationDetailsSerializer> {
    return this.jobApplicationsService.getJobApplicationDetails(id);
  }
}
