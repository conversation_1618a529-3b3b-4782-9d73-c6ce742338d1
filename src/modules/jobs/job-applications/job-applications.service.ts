import { Injectable, NotFoundException } from '@nestjs/common';
import { JobApplicationRepository } from '../../../models/jobs/repositories/job-application.repository';
import { UpdateJobApplicationDto } from './dto/update-job-application.dto';
import { JobApplicationStatsResponseDto } from './dto/job-application-stats.dto';
import {
  JobApplicationQueryDto,
  PaginatedJobApplicationResponseDto,
} from './dto/get-job-applications.dto';
import { JobApplicationDetailsSerializer } from '../../../models/jobs/serializers/job-application-details.serializer';
import { MessageRepository } from '../../../models/conversations/repositories/message.repository';
@Injectable()
export class JobApplicationsService {
  constructor(
    private readonly jobApplicationRepository: JobApplicationRepository,
    private readonly messageRepository: MessageRepository,
  ) {}

  async getJobApplications(
    query: JobApplicationQueryDto,
  ): Promise<PaginatedJobApplicationResponseDto> {
    const [applications, total] =
      await this.jobApplicationRepository.findJobApplications(query);

    const mappedApplications = applications.map((application) => ({
      id: application.id,
      candidateId: application.candidate?.candidateId || 0,
      candidateName: application.candidate?.name || '',

      jobTitle: application.job?.title || '',
      jobLocation: application.job?.location?.city || '',
      appliedDate: application.createdAt,
      candidateEmail: application.candidate?.email || '',
      candidatePhone: application.candidate?.phone || '',
      status: application.status,
    }));

    return {
      data: mappedApplications,
      total,
      page: query.page,
      limit: query.limit,
      totalPages: Math.ceil(total / query.limit),
    };
  }

  async updateJobApplicationStatus(
    id: string,
    updateDto: UpdateJobApplicationDto,
  ) {
    const jobApplication = await this.jobApplicationRepository.findById(id);
    if (!jobApplication) {
      throw new Error('Job application not found');
    }
    return this.jobApplicationRepository.updateById(id, {
      status: updateDto.status,
    });
  }

  async getApplicationStats(
    organisationId: string,
  ): Promise<JobApplicationStatsResponseDto> {
    const statusCounts =
      await this.jobApplicationRepository.getApplicationStatsByOrganisation(
        organisationId,
      );

    const totalApplications = statusCounts.reduce(
      (sum, stat) => sum + stat.count,
      0,
    );

    return {
      statusCounts: [
        ...statusCounts,
        {
          status: 'all' as any,
          name: 'All',
          count: totalApplications,
        },
      ],
      totalApplications,
    };
  }

  async getJobApplicationDetails(
    id: string,
  ): Promise<JobApplicationDetailsSerializer> {
    const application =
      await this.jobApplicationRepository.findByIdWithRelations(id);

    if (!application) {
      throw new NotFoundException(`Job application with ID "${id}" not found`);
    }

    return JobApplicationDetailsSerializer.serialize(application);
  }
}
