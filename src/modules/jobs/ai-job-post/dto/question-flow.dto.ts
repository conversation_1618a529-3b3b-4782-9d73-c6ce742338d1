import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsUUID } from 'class-validator';

export class QuestionFlowDto {
  @ApiPropertyOptional({
    description: 'Existing session ID to continue the flow',
  })
  @IsOptional()
  @IsUUID()
  sessionId?: string;

  @ApiPropertyOptional({
    description: 'Response from the user',
  })
  @IsOptional()
  response?: string;

  @ApiPropertyOptional({
    description: 'Current step of the question flow',
  })
  @IsOptional()
  currentStep?: string;
}
