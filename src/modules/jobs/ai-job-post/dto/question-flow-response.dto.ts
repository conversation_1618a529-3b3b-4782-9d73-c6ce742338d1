import { ApiProperty } from '@nestjs/swagger';

class QuestionFlowDto {
  @ApiProperty({
    description: 'The step ID of the question',
  })
  stepId: string;

  @ApiProperty({
    description: 'The message of the question',
  })
  message: string;

  @ApiProperty({
    description: 'The type of the question',
  })
  type: string;

  @ApiProperty({
    description: 'The required status of the question',
  })
  required: boolean;
}

export class QuestionFlowResponseDto {
  @ApiProperty({
    description: 'The session ID of the conversation',
  })
  sessionId: string;

  @ApiProperty({
    description: 'The current step of the conversation',
  })
  currentStep: string;

  @ApiProperty({
    description: 'The next question of the conversation',
  })
  nextQuestion: QuestionFlowDto;

  @ApiProperty({
    description: 'The status of the conversation',
  })
  status: string;

  @ApiProperty({
    description: 'The job post of the conversation',
  })
  jobPost?: any;
}
