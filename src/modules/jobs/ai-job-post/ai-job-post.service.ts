import { Injectable, NotFoundException } from '@nestjs/common';
import { QuestionFlowDto } from './dto/question-flow.dto';
import { Question, questions } from './ai-job-post-question/questions';
import { JobPostConversationRepository } from '../../../models/jobs/repositories/job-post-conversation.repository';
import { QuestionFlowResponseDto } from './dto/question-flow-response.dto';
import { DeepseekService } from '../../../common/providers/deepseek/deepseek.service';
import { JobPostMessageRepository } from '../../../models/jobs/repositories/job-post-message.repository';
import { OpenAIService } from '../../../common/providers/openai/openai.service';
@Injectable()
export class AiJobPostService {
  private questions: Record<string, Question>;
  private questionSequence: string[];

  constructor(
    private readonly conversationRepository: JobPostConversationRepository,
    private readonly deepSeekService: DeepseekService,
    private readonly openaiService: OpenAIService,
    private readonly jobPostMessageRepository: JobPostMessageRepository,
  ) {
    this.loadQuestions();
  }

  //Load the questions
  private loadQuestions() {
    // Convert array to record for easier lookup
    this.questions = questions.reduce(
      (acc, question) => {
        acc[question.stepId] = question;
        return acc;
      },
      {} as Record<string, Question>,
    );

    // Store the sequence of questions
    this.questionSequence = questions.map((q) => q.stepId);
  }

  //Validate the user response
  private async validateUserResponse(
    stepId: string,
    response: string,
  ): Promise<any> {
    const question = this.questions[stepId];
    const prompt = `
    You are an intelligent assistant responsible for validating job-related answers provided by a user during a job post creation process.
      Your task is to analyze the user's response to a job-related question, determine whether it is meaningful and relevant, and return a structured JSON output based on the validation result.
      ---
      🔹 Validation Instructions:
      - Consider whether the response is sufficiently detailed and relevant to the question.
      - Accept concise but valid answers (e.g., "Bachelor's degree in Marketing" or "3+ years experience in React").
      - Reject vague, incomplete, irrelevant, or non-professional answers (e.g., "idk", "fun", "cooking" for a software job).
      - If the response is valid, clean and normalize the content (capitalization, minor rephrasing).
      - If invalid, include a clear, professional error message guiding the user on how to improve the response.
      ---
      📝 Input:

      - **Question**: 🎯 ${question.message}
      - **User Response**: ${response}
      ---
      🎯 Output JSON format (no extra text or commentary):

      {
        "isValidResponse": true/false,
        "outcome": "<Normalized cleaned response or null>",
        "errorMessage": "<Clear explanation or null>"
      }
      `;

    if (process.env.AI_PROVIDER === 'deepseek') {
      const deepSeekResponse =
        await this.deepSeekService.validateUserResponse(prompt);
      console.log('deepSeekResponse :', deepSeekResponse);

      return deepSeekResponse;
    } else {
      const openAIResponse =
        await this.openaiService.validateUserResponse(prompt);
      console.log('openAIResponse :', openAIResponse);
      return openAIResponse;
    }
  }

  //Get the next question based on the current step
  private async getNextQuestion(currentStep: string): Promise<Question | null> {
    // If no current step, return the first question
    if (!currentStep) {
      return this.questions[this.questionSequence[0]] || null;
    }

    // Find the index of the current step in the sequence
    const currentIndex = this.questionSequence.indexOf(currentStep);

    // If current step not found or it's the last question, return null
    if (
      currentIndex === -1 ||
      currentIndex === this.questionSequence.length - 1
    ) {
      return null;
    }

    // Return the next question in the sequence
    return this.questions[this.questionSequence[currentIndex + 1]] || null;
  }

  //Get the current step of the conversation
  private async getFirstStepQuestion(): Promise<Question> {
    return this.questions[this.questionSequence[0]];
  }

  async startQuestionFlow(
    userId: string,
    questionFlowDto: QuestionFlowDto,
  ): Promise<QuestionFlowResponseDto> {
    if (questionFlowDto.sessionId) {
      const existingConversation =
        await this.conversationRepository.findBySessionId(
          questionFlowDto.sessionId,
        );

      if (!existingConversation) {
        throw new NotFoundException('Conversation session not found');
      }

      //validate the response using deepseek service
      const validatedResponse = await this.validateUserResponse(
        questionFlowDto.currentStep,
        questionFlowDto.response,
      );

      if (!validatedResponse.isValidResponse) {
        const currentQuestion = this.questions[questionFlowDto.currentStep];

        //Create a job post message
        await this.jobPostMessageRepository.createJobPostMessage({
          sessionId: existingConversation.id,
          stepId: questionFlowDto.currentStep,
          userResponse: questionFlowDto.response,
          jobAgentResponse: validatedResponse.errorMessage,
        });

        return {
          sessionId: existingConversation.id,
          currentStep: questionFlowDto.currentStep,
          nextQuestion: {
            stepId: currentQuestion.stepId,
            message: validatedResponse.errorMessage,
            type: currentQuestion.type,
            required: currentQuestion.required,
          },
          status: existingConversation.status,
        };
      } else {
        console.log('validatedResponse :', validatedResponse);
        //Create a job post message
        await this.jobPostMessageRepository.createJobPostMessage({
          sessionId: existingConversation.id,
          stepId: questionFlowDto.currentStep,
          userResponse: questionFlowDto.response,
          jobAgentResponse: validatedResponse.errorMessage,
          outcome: validatedResponse.outcome,
        });
      }

      // Get the current step and next question based on conversation state
      const currentStep =
        questionFlowDto.currentStep || existingConversation.currentStep;
      const nextQuestion = await this.getNextQuestion(currentStep);

      //Generate the job post

      if (nextQuestion.stepId === 'final_response') {
        //Fetch the job post content from the job post message
        const jobPostContent =
          await this.jobPostMessageRepository.findOutcomesBySessionId(
            existingConversation.id,
          );

        //Create an object with stepId as key and userResponse as value
        const jobPostContentObject = jobPostContent.reduce(
          (acc, message) => {
            acc[message.stepId] = message.outcome;
            return acc;
          },
          {} as Record<string, string>,
        );

        const jobPost = await this.deepSeekService.generateJobPostUsingAI(
          JSON.stringify(jobPostContentObject),
        );

        return {
          sessionId: existingConversation.id,
          currentStep: nextQuestion.stepId,
          nextQuestion,
          status: existingConversation.status,
          jobPost,
        };
      }

      return {
        sessionId: existingConversation.id,
        currentStep: nextQuestion.stepId,
        nextQuestion,
        status: existingConversation.status,
      };
    }

    // Get the first question
    const firstQuestion = await this.getFirstStepQuestion();

    // Create new conversation session
    const newConversation =
      await this.conversationRepository.createConversation({
        userId,
        status: 'in_progress',
        currentStep: firstQuestion.stepId,
      });

    return {
      sessionId: newConversation.id,
      currentStep: firstQuestion.stepId,
      nextQuestion: firstQuestion,
      status: newConversation.status,
    };
  }
}
