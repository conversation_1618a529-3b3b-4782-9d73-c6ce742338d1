import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON><PERSON>, Api<PERSON>earerAuth, ApiHeader } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../authentication/jwt-auth.guard';
import { PermissionsGuard } from '../../../common/guards/permissions.guard';
import { RequirePermissions } from '../../../common/decorators/metadata/permissions.decorator';
import {
  PermissionModules,
  PermissionActions,
} from '../../../models/roles/constants/role.constants';
import { AiJobPostService } from './ai-job-post.service';
import { QuestionFlowDto } from './dto/question-flow.dto';
import { CurrentUser } from '../../../common/decorators/current-user.decorator';
import { User } from '../../../models/users/entities/user.entity';

@ApiTags('AI Job Post')
@Controller('jobs/ai-job-post')
@UseGuards(JwtAuthGuard, PermissionsGuard)
@ApiBearerAuth()
@ApiHeader({
  name: 'x-session-id',
  description: 'Session ID for continuing an existing conversation',
  required: false,
})
export class AiJobPostController {
  constructor(private readonly aiJobPostService: AiJobPostService) {}

  @Post('question-flow')
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.CREATE_JOBS,
  })
  async startQuestionFlow(
    @CurrentUser() user: User,
    @Body() questionFlowDto: QuestionFlowDto,
  ) {
    return this.aiJobPostService.startQuestionFlow(user.id, questionFlowDto);
  }
}
