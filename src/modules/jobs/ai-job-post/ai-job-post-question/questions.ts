export interface Question {
  stepId: string;
  message: string;
  type: string;
  required: boolean;
}

export const questions: Question[] = [
  {
    stepId: 'job_title',
    message: "👋 Hi there! Let's start with the basics. What's the job title?",
    type: 'text',
    required: true,
  },
  {
    stepId: 'qualifications',
    message: '🎯 What are the essential qualifications needed for this role?',
    type: 'text',
    required: true,
  },
  {
    stepId: 'experience',
    message: '💼 How much experience should the candidate have?',
    type: 'text',
    required: true,
  },
  {
    stepId: 'employment_type',
    message: '🕒 Is this a Full-time or Part-time position?',
    type: 'text',
    required: true,
  },
  {
    stepId: 'final_response',
    message:
      "Great! I'm now generating your job post with all this information. Hang tight for a few seconds...",
    type: 'text',
    required: false,
  },
];
