import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AiJobPostController } from './ai-job-post.controller';
import { AiJobPostService } from './ai-job-post.service';
import { JobPostConversation } from '../../../models/jobs/entities/job-post-conversation.entity';
import { JobPostMessage } from '../../../models/jobs/entities/job-post-message.entity';
import { JobPostConversationRepository } from '../../../models/jobs/repositories/job-post-conversation.repository';
import { DeepseekModule } from '../../../common/providers/deepseek/deepseek.module';
import { RolesModule } from '../../../modules/roles-permissions/roles.module';
import { JobPostMessageRepository } from '../../../models/jobs/repositories/job-post-message.repository';
import { OpenAIModule } from '../../../common/providers/openai/openai.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([JobPostConversation, JobPostMessage]),
    DeepseekModule,
    OpenAIModule,
    RolesModule,
  ],
  controllers: [AiJobPostController],
  providers: [
    AiJobPostService,
    JobPostConversationRepository,
    JobPostMessageRepository,
  ],
  exports: [AiJobPostService, JobPostConversationRepository],
})
export class AiJobPostModule {}
