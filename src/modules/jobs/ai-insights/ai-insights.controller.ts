import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AiInsightsService } from './ai-insights.service';

@ApiTags('AI Insights')
@Controller('ai-insights')
export class AiInsightsController {
  constructor(private readonly aiInsightsService: AiInsightsService) {}

  @Get('application/:id')
  @ApiOperation({
    summary: 'Get AI insights for a job application',
    description:
      'Retrieves the AI-generated insights for a specific job application',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the AI insights for the job application',
  })
  async getApplicationInsights(@Param('id') applicationId: string) {
    return this.aiInsightsService.getApplicationInsights(applicationId);
  }

  @Get('generate')
  @ApiOperation({
    summary: 'Generate AI insights for all pending applications',
    description: 'Generates AI insights for all pending applications',
  })
  async generateInsightsForAllPendingApplications() {
    return this.aiInsightsService.generateInsightsForPendingApplications();
  }
}
