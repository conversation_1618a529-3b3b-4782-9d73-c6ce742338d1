import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobApplication } from '../../../models/jobs/entities/job-application.entity';
import { MessageRepository } from '../../../models/conversations/repositories/message.repository';
import { OpenAIService } from '../../../common/providers/openai/openai.service';
import { JobApplicationInsights } from '../../../models/jobs/entities/job-application-insights.entity';
import { JobApplicationInsightsStatus } from '../../../models/jobs/entities/job-application-insights.entity';
import { JobPostsService } from '../job-posts/job-posts.service';
import { Cron } from '@nestjs/schedule';
import { CronExpression } from '@nestjs/schedule';
import { TextractService } from '../../../common/providers/aws-textract/textract.service';
@Injectable()
export class AiInsightsService {
  private readonly logger = new Logger(AiInsightsService.name);
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 5000; // 5 seconds
  private readonly CUTOFF_TIME = 30 * 60 * 1000; // 30 minutes

  constructor(
    @InjectRepository(JobApplication)
    private readonly jobApplicationRepository: Repository<JobApplication>,
    @InjectRepository(JobApplicationInsights)
    private readonly jobApplicationInsightsRepository: Repository<JobApplicationInsights>,
    private readonly messageRepository: MessageRepository,
    private readonly openAIService: OpenAIService,
    private readonly jobPostsService: JobPostsService,
    private readonly textractService: TextractService,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async generateInsightsForPendingApplications() {
    try {
      this.logger.log('Starting insights generation for pending applications');
      const applicationsQuery = this.jobApplicationRepository
        .createQueryBuilder('application')
        .leftJoinAndSelect('application.insights', 'insights')
        .innerJoin(
          'conversations',
          'conversation',
          'conversation.conversation_id = application.conversation_id',
        )
        .innerJoin(
          '(SELECT conversation_id, MAX(sent_at) as last_message_time FROM messages GROUP BY conversation_id)',
          'last_message',
          'last_message.conversation_id = conversation.id',
        )
        .addSelect('conversation.id', 'conversation_id')
        .where('(insights.id IS NULL OR insights.status IN (:...statuses))', {
          statuses: [
            JobApplicationInsightsStatus.PENDING,
            JobApplicationInsightsStatus.PROCESSING,
          ],
        })
        .andWhere('application.conversation_id IS NOT NULL')
        .andWhere('last_message.last_message_time <= :cutoffTime', {
          cutoffTime: new Date(Date.now() - this.CUTOFF_TIME),
        })
        .orderBy('last_message.last_message_time', 'ASC')
        .take(5);

      const applications = await applicationsQuery.getRawMany();
      this.logger.log(`Found ${applications.length} applications to process`);

      for (const application of applications) {
        try {
          this.logger.debug(
            `Processing application ${application.application_id}`,
            { applicationId: application.application_id },
          );

          if (!application.insights_id) {
            this.logger.debug(
              `Creating new insights for application ${application.application_id}`,
            );
            const insights = this.jobApplicationInsightsRepository.create({
              jobApplicationId: application.application_id,
              status: JobApplicationInsightsStatus.PROCESSING,
            });
            await this.jobApplicationInsightsRepository.save(insights);
          } else {
            this.logger.debug(
              `Updating existing insights for application ${application.application_id} to PROCESSING`,
            );
            await this.jobApplicationInsightsRepository.update(
              { jobApplicationId: application.application_id },
              {
                status: JobApplicationInsightsStatus.PROCESSING,
              },
            );
          }

          // Get all messages for the conversation
          const messages =
            await this.messageRepository.findMessagesByConversationId(
              application.conversation_id,
            );

          if (messages.length > 0) {
            this.logger.debug(
              `Found ${messages.length} messages for application ${application.application_id}`,
            );
            const { formattedMessage, resumeUrl } =
              this.formatMessagesForAI(messages);

            // get job post details
            this.logger.debug(
              `Fetching job post details for application ${application.application_id}`,
            );
            const jobDetails = await this.getJobPostDetails(
              application.application_job_id,
            );
            await this.generateInsightsForApplication(
              application,
              jobDetails,
              formattedMessage,
              resumeUrl,
            );
          } else {
            this.logger.warn(
              `No messages found for application ${application.application_id}`,
              { applicationId: application.application_id },
            );
            await this.jobApplicationInsightsRepository.update(
              { jobApplicationId: application.application_id },
              {
                status: JobApplicationInsightsStatus.PENDING,
              },
            );
          }
        } catch (error) {
          this.logger.error(
            `Error processing application ${application.application_id}:`,
            {
              error: error.message,
              stack: error.stack,
              applicationId: application.application_id,
            },
          );
          await this.jobApplicationInsightsRepository.update(
            { jobApplicationId: application.application_id },
            {
              status: JobApplicationInsightsStatus.FAILED,
            },
          );
        }
      }

      this.logger.log('Insights generation process completed successfully');
      return { message: 'Insights generation process completed' };
    } catch (error) {
      this.logger.error('Error in insights generation job:', {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Generates insights for a job application
   * @param application - The job application
   * @param jobDetails - The job details
   * @param messageContent - The candidate's response to the job application
   * @param resumeUrl - The candidate's resume URL
   */
  private async generateInsightsForApplication(
    application: any,
    jobDetails: any,
    messageContent: any[],
    resumeUrl: string,
  ) {
    let retries = 0;
    while (retries < this.MAX_RETRIES) {
      try {
        this.logger.debug(
          `Generating AI Insights for application ${application.application_id}, attempt ${retries + 1}/${this.MAX_RETRIES}`,
        );

        // Generate prompt for OpenAI
        const prompt = this.generatePrompt(
          jobDetails,
          messageContent,
          resumeUrl,
        );

        // Call OpenAI API
        this.logger.debug(
          `Calling OpenAI API for application ${application.application_id}`,
        );
        const aiResponse =
          await this.openAIService.generateAIInsightsForApplication(prompt);

        // Update insights with the response
        this.logger.debug(
          `Updating insights with AI response for application ${application.application_id}`,
        );
        await this.jobApplicationInsightsRepository.update(
          { jobApplicationId: application.application_id },
          {
            matchPercentage: aiResponse.match_score,
            matchingKeywords: aiResponse.top_matching_keywords,
            summary: aiResponse.summary,
            generatedAt: new Date(),
            status: JobApplicationInsightsStatus.COMPLETED,
          },
        );

        this.logger.debug(
          `Successfully generated insights for application ${application.application_id}`,
        );
        return; // Success, exit the retry loop
      } catch (error) {
        retries++;
        if (error.response?.status === 429) {
          // Rate limit error
          this.logger.warn(
            `Rate limit hit for application ${application.application_id}, retry ${retries}/${this.MAX_RETRIES}`,
            {
              applicationId: application.application_id,
              retryCount: retries,
            },
          );
          if (retries < this.MAX_RETRIES) {
            const delay = this.RETRY_DELAY * retries;
            this.logger.debug(
              `Waiting ${delay}ms before retry ${retries} for application ${application.application_id}`,
            );
            await new Promise((resolve) => setTimeout(resolve, delay));
            continue;
          }
        }

        if (retries === this.MAX_RETRIES) {
          this.logger.error(
            `Error generating insights for application ${application.application_id} after ${this.MAX_RETRIES} retries:`,
            {
              error: error.message,
              stack: error.stack,
              applicationId: application.application_id,
            },
          );
          await this.jobApplicationInsightsRepository.update(
            { jobApplicationId: application.application_id },
            {
              status: JobApplicationInsightsStatus.FAILED,
            },
          );
        }
      }
    }
  }

  /**
   * Generates a prompt for the AI to generate insights for a job application
   * @param jobDetails - The job details
   * @param messageContent - The candidate's response to the job application
   * @param resumeUrl - The candidate's resume URL
   * @returns The prompt for the AI
   */
  private generatePrompt(
    jobDetails: JobApplication,
    messageContent: any[],
    resumeUrl: string,
  ): string {
    return `
You are an expert AI recruitment assistant. Your task is to evaluate a candidate's suitability for a job opening based on:

1. The job description and its detailed qualifications and responsibilities.
2. The candidate's answers to automated screening questions (messageContent).
3. Optionally, a candidate resume URL (if provided).


---

Job Details:
${JSON.stringify(jobDetails, null, 2)}


---

Candidate Response Transcript (as messageContent array):
${JSON.stringify(messageContent, null, 2)}

---

Candidate Resume URL:
${resumeUrl ? resumeUrl : 'No resume URL provided'}

---

Based on the above inputs, perform the following:

1. Give a match rating out of 10.000 (up to 3 decimal places).
2. List the top 4–6 matching keywords relevant to the job.
3. Write a 1–2 line AI-generated summary about the candidate's fit for the role.

Respond only in this JSON format:
{
  "match_score": 8.235,
  "top_matching_keywords": ["Team Leadership", "Inventory Management"],
  "summary": "The candidate demonstrates strong leadership but lacks direct culinary experience."
}

`;
  }

  /**
   * Get the AI insights for a specific job application
   * @param applicationId - The ID of the job application
   * @returns The AI insights for the job application
   */
  async getApplicationInsights(applicationId: string) {
    try {
      this.logger.debug(`Fetching insights for application ${applicationId}`);
      const application = await this.jobApplicationRepository.findOne({
        where: { id: applicationId },
        relations: ['insights'],
      });

      if (!application) {
        this.logger.warn(`Application not found with ID ${applicationId}`);
        throw new NotFoundException(
          `Job application with ID "${applicationId}" not found`,
        );
      }

      this.logger.debug(
        `Successfully fetched insights for application ${applicationId}`,
      );
      return application.insights?.[0] || null;
    } catch (error) {
      this.logger.error(
        `Error fetching insights for application ${applicationId}:`,
        {
          error: error.message,
          stack: error.stack,
          applicationId,
        },
      );
      throw error;
    }
  }

  /**
   * Formats messages for AI processing
   * @param messages - Array of message objects
   * @returns Array of formatted messages
   */
  private formatMessagesForAI(messages: any[]) {
    const formattedMessage = [];
    let resumeUrl = null;
    for (let i = 0; i < messages.length; i++) {
      const current = messages[i];

      // Check if it's a bot message
      if (!current.senderId && current.receiverId) {
        const next = messages[i + 1];
        const isCandidateReply =
          next &&
          next.senderId &&
          !next.receiverId &&
          next.conversationId === current.conversationId;

        if (isCandidateReply) {
          resumeUrl = next.metadata?.url;
        }
        formattedMessage.push({
          bot: current.content,
          candidate: isCandidateReply ? next.content : null,
          metadata: isCandidateReply && next.metadata ? next.metadata : null,
        });

        // Skip next message if it was used as reply
        if (isCandidateReply) i++;
      }
    }
    return { formattedMessage, resumeUrl };
  }

  /**
   * Get the job post details
   * @param jobId - The ID of the job
   * @returns The job post details
   */
  private async getJobPostDetails(jobId: string) {
    try {
      this.logger.debug(`Fetching job post details for job ${jobId}`);
      const job = await this.jobPostsService.getJobPostById(jobId);

      const jobDetails = {
        title: job.title,
        description: job.description,
        location: {
          name: job?.location?.name,
          city: job?.location?.city,
          address: job?.location?.address,
          country: job?.location?.country,
        },
        jobDetails: job?.sections?.map((section) => {
          return {
            title: section?.title,
            content: section?.content,
            type: section?.type,
          };
        }),
      };

      this.logger.debug(
        `Successfully fetched job post details for job ${jobId}`,
      );
      return jobDetails;
    } catch (error) {
      this.logger.error(`Error fetching job post details for job ${jobId}:`, {
        error: error.message,
        stack: error.stack,
        jobId,
      });
      throw error;
    }
  }

  async generateProfessionalProfile(
    jobApplicationId: string,
    resumeUrl: string,
  ) {
    try {
      this.logger.debug(
        `Generating professional profile for job application ${jobApplicationId}`,
      );

      const jobApplication = await this.jobApplicationRepository.findOne({
        where: { id: jobApplicationId },
      });

      if (!jobApplication) {
        this.logger.warn(
          `Job application not found with ID ${jobApplicationId}`,
        );
        return;
      }

      const resumeText =
        await this.textractService.extractTextFromResume(resumeUrl);
      const prompt = this.generateProfessionalProfilePrompt(resumeText);

      const aiResponse =
        await this.openAIService.generateAIProfileFromResume(prompt);

      // Check if the AI detected an invalid resume
      if (aiResponse.error) {
        this.logger.warn(`AI detected invalid resume: ${aiResponse.error}`);
        await this.jobApplicationInsightsRepository.update(
          { jobApplicationId },
          {
            status: JobApplicationInsightsStatus.FAILED,
            summary: aiResponse.error,
          },
        );
        return;
      }

      // Update insights with the parsed profile
      await this.jobApplicationInsightsRepository.update(
        { jobApplicationId },
        {
          keySkills: aiResponse.keySkills,
          yearsOfExperience: aiResponse.yearsOfExperience,
          education: aiResponse.education,
          workExperience: aiResponse.workExperience,
          professionalSummary: aiResponse.professionalSummary,
          profileGeneratedAt: new Date(),
        },
      );

      this.logger.debug(
        `Successfully generated professional profile for job application ${jobApplicationId}`,
      );

      return aiResponse;
    } catch (error) {
      this.logger.error(
        `Error generating professional profile for job application ${jobApplicationId}:`,
        {
          error: error.message,
          stack: error.stack,
          jobApplicationId,
        },
      );

      // Update insights status to failed
      await this.jobApplicationInsightsRepository.update(
        { jobApplicationId },
        {
          status: JobApplicationInsightsStatus.FAILED,
          summary: `Error processing resume: ${error.message}`,
        },
      );

      throw error;
    }
  }

  private generateProfessionalProfilePrompt(resumeText: string) {
    return `
    You are an expert resume parser. Your task is to intelligently analyze the following document and extract structured data only if it is a valid resume.
    If the document does not appear to be a resume (e.g., it's a cover letter, invoice, random text), return an error with a clear message like "error": "Input does not appear to be a valid resume."

    Resume Text: ${resumeText}

    Your output must be valid JSON and strictly follow this format:
    {
  "keySkills": {
    "technical": ["..."],
    "soft": ["..."],
    "languages": ["..."],
    "certifications": ["..."],
    "tools": ["..."]
  },
  "yearsOfExperience": 0,
  "education": [
    {
      "level": "Bachelors / Masters / Diploma / etc.",
      "field": "Computer Science / Business / etc.",
      "institution": "University Name",
      "location": "City, Country",
      "graduationYear": 2021,
      "gpa": 3.8,
      "honors": ["Summa Cum Laude"],
      "major": "Computer Science",
      "minor": "Mathematics",
      "coursework": ["Algorithms", "Data Structures", "AI"]
    }
  ],
  "workExperience": [
    {
      "title": "Software Engineer",
      "company": "TechCorp Inc.",
      "location": "Bangalore, India",
      "industry": "Technology",
      "startDate": "2021-06-01",
      "endDate": "2023-12-01",
      "isCurrent": false,
      "description": "Worked on full-stack applications...",
      "achievements": ["Reduced server costs by 20%", "Led a team of 5 engineers"],
      "skills_used": ["Node.js", "React", "AWS"],
      "projects": [
        {
          "name": "Inventory Management System",
          "description": "Built a tool to manage product inventory.",
          "technologies": ["Node.js", "MongoDB"],
          "url": "https://github.com/user/project"
        }
      ]
    }
  ],
  "professionalSummary": {
    "brief": "Experienced software engineer with a passion for scalable systems.",
    "highlights": [
      "Led cloud migrations",
      "Built high-performance APIs",
      "Worked across cross-functional teams"
    ],
    "expertiseLevel": "Mid-level / Senior / Entry-level",
    "industryFocus": ["Technology", "E-commerce"],
    "careerHighlights": [
      "Awarded Best Developer 2022",
      "Built an app with 1M+ downloads"
    ]
  }
}

Only return this JSON. Do not add any explanation or comments.
    `;
  }
}
