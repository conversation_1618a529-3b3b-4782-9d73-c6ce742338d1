import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobApplication } from '../../../models/jobs/entities/job-application.entity';
import { AiInsightsService } from './ai-insights.service';
import { AiInsightsController } from './ai-insights.controller';
import { ConversationRepository } from '../../../models/conversations/repositories/conversation.repository';
import { MessageRepository } from '../../../models/conversations/repositories/message.repository';
import { ConfigModule } from '@nestjs/config';
import { OpenAIModule } from '../../../common/providers/openai/openai.module';
import { Conversation } from '../../../models/conversations/entities/conversation.entity';
import { Message } from '../../../models/conversations/entities/message.entity';
import { TenantModule } from '../../../modules/tenant/tenant.module';
import { JobApplicationInsights } from '../../../models/jobs/entities/job-application-insights.entity';
import { JobPostsModule } from '../job-posts/job-posts.module';
import { JobPostRepository } from '../../../models/jobs/repositories/job-post.repository';
import { JobPostSectionRepository } from '../../../models/jobs/repositories/job-post-section.repository';
import { JobTemplateRepository } from '../../../models/jobs/repositories/job-template.repository';
import { LocationsModule } from '../../locations/locations.module';
import { SectionDefinitionRepository } from '../../../models/jobs/repositories/section-definition.repository';
import { HirenetixAiEngineModule } from '../../../common/providers/hirenetix-ai-engine/hirenetix-ai-engine.module';
import { JobPost } from '../../../models/jobs/entities/job-post.entity';
import { JobPostSection } from '../../../models/jobs/entities/job-post-section.entity';
import { JobTemplate } from '../../../models/jobs/entities/job-template.entity';
import { SectionDefinition } from '../../../models/jobs/entities/section-definition.entity';
import { TextractService } from '../../../common/providers/aws-textract/textract.service';
import { TextractModule } from '../../../common/providers/aws-textract/textract.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      JobApplication,
      Conversation,
      Message,
      JobApplicationInsights,
      JobPost,
      JobPostSection,
      JobTemplate,
      SectionDefinition,
    ]),
    ConfigModule,
    OpenAIModule,
    TenantModule,
    LocationsModule,
    HirenetixAiEngineModule,
    JobPostsModule,
    TextractModule,
  ],
  providers: [
    AiInsightsService,
    ConversationRepository,
    MessageRepository,
    JobPostRepository,
    JobPostSectionRepository,
    JobTemplateRepository,
    SectionDefinitionRepository,
    TextractService,
  ],
  controllers: [AiInsightsController],
  exports: [AiInsightsService],
})
export class AiInsightsModule {}
