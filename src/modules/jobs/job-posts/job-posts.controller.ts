import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
  Request,
  Delete,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JobPostsService } from './job-posts.service';
import { CreateJobPostDto } from './dto/create-job-post.dto';
import { JwtAuthGuard } from '../../../authentication/jwt-auth.guard';
import { PermissionsGuard } from '../../../common/guards/permissions.guard';
import { RequirePermissions } from '../../../common/decorators/metadata/permissions.decorator';
import {
  PermissionModules,
  PermissionActions,
} from '../../../models/roles/constants/role.constants';
import { JobPostListResponseDto } from './dto/job-post-list.dto';
import { JobPostDetailDto } from './dto/job-post-detail.dto';
import { Transform } from 'class-transformer';
import {
  IsOptional,
  IsInt,
  Min,
  IsDateString,
  IsString,
  IsEnum,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UpdateJobPostDto } from './dto/update-job-post.dto';
import {
  JobPostSortColumn,
  JobPostStatus,
} from '../../../models/jobs/constants/job.constants';
import { SORT_ORDER } from '../../../models/users/constants/user.constants';
import { JobPostSortBy } from '../../../models/jobs/constants/job.constants';

class GetJobPostsQueryDto {
  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ required: false, default: 10 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  limit?: number = 50;

  @ApiProperty({
    description: 'Filter users created after this date (ISO 8601 format)',
    example: '2023-01-01T00:00:00Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for startDate' })
  startDate?: string;

  @ApiProperty({
    description: 'Filter users created before this date (ISO 8601 format)',
    example: '2023-12-31T23:59:59Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for endDate' })
  endDate?: string;

  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Sort order (ascending or descending)',
    enum: SORT_ORDER,
    required: false,
    default: SORT_ORDER.DESC,
  })
  @IsOptional()
  @IsEnum(JobPostSortBy, { message: 'Invalid sort order' })
  sortOrder?: JobPostSortBy = JobPostSortBy.DESC;

  @ApiProperty({
    description: 'Sort column',
    enum: JobPostSortColumn,
    required: false,
    default: JobPostSortColumn.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(JobPostSortColumn, { message: 'Invalid sort column' })
  sortColumn?: JobPostSortColumn = JobPostSortColumn.CREATED_AT;

  @ApiProperty({
    description: 'Status',
    enum: JobPostStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(JobPostStatus, { message: 'Invalid status' })
  status?: JobPostStatus;

  @ApiProperty({
    description: 'active jobs',
    required: false,
  })
  @IsOptional()
  @IsEnum(['true', 'false'], { message: 'Invalid active status' })
  isActive?: any;
}

@ApiTags('Job Posts')
@Controller('job-posts')
@UseGuards(JwtAuthGuard, PermissionsGuard)
@ApiBearerAuth()
export class JobPostsController {
  constructor(private readonly jobPostsService: JobPostsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all job posts' })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated job posts with their details',
    type: JobPostListResponseDto,
  })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.VIEW_JOBS,
  })
  async getAllJobPosts(
    @Query() query: GetJobPostsQueryDto,
  ): Promise<JobPostListResponseDto> {
    return this.jobPostsService.getAllJobPosts(
      query.page,
      query.limit,
      query.startDate,
      query.endDate,
      query.search,
      query.sortOrder,
      query.sortColumn,
      query.status,
      query.isActive,
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get job post by ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the job post details',
    type: JobPostDetailDto,
  })
  @ApiResponse({ status: 404, description: 'Job post not found' })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.VIEW_JOBS,
  })
  async getJobPostById(@Param('id') id: string): Promise<JobPostDetailDto> {
    return this.jobPostsService.getJobPostById(id);
  }

  @Patch('publish/:id')
  @ApiOperation({ summary: 'Publish a job post' })
  @ApiResponse({
    status: 200,
    description: 'The job post has been successfully published',
    type: JobPostDetailDto,
  })
  @ApiResponse({ status: 404, description: 'Job post not found' })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.PUBLISH_JOBS,
  })
  async publishJobPost(@Param('id') id: string): Promise<JobPostDetailDto> {
    return this.jobPostsService.publishJobPost(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new job post' })
  @ApiResponse({
    status: 201,
    description: 'The job post has been successfully created',
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.CREATE_JOBS,
  })
  async createJobPost(
    @Body() createJobPostDto: CreateJobPostDto,
    @Request() req,
  ) {
    return this.jobPostsService.createJobPost(
      createJobPostDto,
      req.user.id,
      req.user.locationId,
    );
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a job post' })
  @ApiResponse({
    status: 200,
    description: 'The job post has been successfully updated',
    type: JobPostDetailDto,
  })
  @ApiResponse({ status: 404, description: 'Job post not found' })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.EDIT_JOBS,
  })
  async updateJobPost(
    @Param('id') id: string,
    @Body() updateJobPostDto: UpdateJobPostDto,
  ): Promise<JobPostDetailDto> {
    return this.jobPostsService.updateJobPost(id, updateJobPostDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a job post' })
  @ApiResponse({
    status: 200,
    description: 'The job post has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Job post not found' })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.DELETE_JOBS,
  })
  async deleteJobPost(@Param('id') id: string): Promise<void> {
    return this.jobPostsService.deleteJobPost(id);
  }

  @Patch('active-status/:id')
  @ApiOperation({ summary: 'Update job post active status' })
  @ApiResponse({
    status: 200,
    description: 'The job post active status has been successfully updated',
    type: JobPostDetailDto,
  })
  @ApiResponse({ status: 404, description: 'Job post not found' })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.PUBLISH_JOBS,
  })
  async updateJobPublishedStatus(
    @Param('id') id: string,
  ): Promise<JobPostDetailDto> {
    return this.jobPostsService.toggleJobPublishedStatus(id);
  }
}
