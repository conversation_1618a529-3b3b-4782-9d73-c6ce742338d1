import { ApiProperty } from '@nestjs/swagger';
import { PublicJobPostSerializer } from '../../../../models/jobs/serializers/public-job-post.serializer';

export class PublicJobPostListResponseDto {
  @ApiProperty({ type: [PublicJobPostSerializer] })
  data: PublicJobPostSerializer[];

  @ApiProperty()
  totalItems: number;

  @ApiProperty()
  currentPage: number;

  @ApiProperty()
  itemsPerPage: number;

  @ApiProperty()
  totalPages: number;
}
