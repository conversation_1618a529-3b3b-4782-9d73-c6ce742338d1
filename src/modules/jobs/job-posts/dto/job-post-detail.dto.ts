import { ApiProperty } from '@nestjs/swagger';
import { JobPostStatus } from '../../../../models/jobs/constants/job.constants';
import { JobPostSectionType } from '../../../../models/jobs/entities/job-post-section.entity';
class UserDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;
}

class LocationDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  city: string;

  @ApiProperty()
  address: string;

  @ApiProperty()
  country: string;
}

class JobPostSectionDto {
  @ApiProperty()
  title: string;

  @ApiProperty()
  content: any;

  @ApiProperty()
  displayOrder: number;

  @ApiProperty()
  sectionDefinitionId: string;

  @ApiProperty()
  type: JobPostSectionType;

  @ApiProperty()
  isVisibleDefault: boolean;

  @ApiProperty()
  isEditable: boolean;

  @ApiProperty()
  isDeletable: boolean;
}

export class JobPostDetailDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty({ nullable: true })
  description: string | null;

  @ApiProperty({ enum: JobPostStatus })
  status: JobPostStatus;

  @ApiProperty({ type: UserDto, nullable: true })
  createdBy: UserDto | null;

  @ApiProperty({ type: LocationDto, nullable: true })
  location: LocationDto | null;

  @ApiProperty({ nullable: true })
  jobTemplateId: string | null;

  @ApiProperty({ nullable: true })
  publishedAt: Date | null;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ type: [JobPostSectionDto] })
  sections: JobPostSectionDto[];
}
