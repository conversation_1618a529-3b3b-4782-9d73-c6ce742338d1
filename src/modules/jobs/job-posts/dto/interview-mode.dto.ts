import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class InterviewModeDto {
  @ApiProperty({
    description: 'Whether phone interviews are available',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === true)
  phone: boolean = false;

  @ApiProperty({
    description: 'Whether video interviews are available',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === true)
  video: boolean = false;

  @ApiProperty({
    description: 'Whether in-person interviews are available',
    example: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === true)
  in_person: boolean = false;
}
