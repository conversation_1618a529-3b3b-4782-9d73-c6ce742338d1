import { ApiProperty } from '@nestjs/swagger';

export class JobPostListItemDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty({ nullable: true })
  description: string | null;
}

export class JobPostListResponseDto {
  @ApiProperty({ type: [JobPostListItemDto] })
  data: JobPostListItemDto[];

  @ApiProperty()
  totalItems: number;

  @ApiProperty()
  currentPage: number;

  @ApiProperty()
  itemsPerPage: number;

  @ApiProperty()
  totalPages: number;
}
