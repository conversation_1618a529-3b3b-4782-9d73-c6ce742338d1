import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsUUID,
  IsArray,
  ValidateNested,
  IsOptional,
  IsBoolean,
  IsInt,
  IsNumber,
  ValidateIf,
} from 'class-validator';

class JobDetailsContentDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  key?: string;

  @ApiProperty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  label?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  value?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  options?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  min?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  max?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  currency?: string;
}

class TextContentDto {
  @ApiProperty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsString()
  value: string;
}

class CreateJobPostSectionDto {
  @ApiProperty()
  @IsUUID()
  @IsOptional()
  jobTemplateSectionId: string;

  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty({ type: [Object] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JobDetailsContentDto, {
    discriminator: {
      property: 'title',
      subTypes: [
        { value: JobDetailsContentDto, name: 'Job Details' },
        { value: TextContentDto, name: 'default' },
      ],
    },
  })
  content: (JobDetailsContentDto | TextContentDto)[];

  @ApiProperty()
  @IsInt()
  displayOrder: number;

  @ApiProperty()
  @IsBoolean()
  isVisibleDefault: boolean;

  @ApiProperty()
  @IsBoolean()
  isEditable: boolean;

  @ApiProperty()
  @IsBoolean()
  isDeletable: boolean;

  @ApiProperty()
  @IsOptional()
  sectionDefinitionId?: string;

  @ApiProperty()
  @IsString()
  type: string;
}

export class CreateJobPostDto {
  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  isDraft?: boolean;

  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @ValidateIf((o) => !o.isDraft)
  locationId?: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  jobTemplateId: string;

  @ApiProperty({ type: [CreateJobPostSectionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateJobPostSectionDto)
  @ValidateIf((o) => !o.isDraft)
  sections: CreateJobPostSectionDto[];
}
