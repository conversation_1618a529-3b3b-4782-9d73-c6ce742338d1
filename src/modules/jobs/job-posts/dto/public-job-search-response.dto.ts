import { ApiProperty } from '@nestjs/swagger';

export class PublicJobLocationDto {
  @ApiProperty({
    description: 'Location ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Location name',
    example: 'San Francisco Office',
  })
  name: string;

  @ApiProperty({
    description: 'City name',
    example: 'San Francisco',
  })
  city: string;

  @ApiProperty({
    description: 'Location address',
    example: '123 Main St, San Francisco, CA 94105',
  })
  address: string;
}

export class PublicJobSectionDto {
  @ApiProperty({
    description: 'Section ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Section title',
    example: 'Job Requirements',
  })
  title: string;

  @ApiProperty({
    description: 'Section content',
    example: 'The ideal candidate will have...',
  })
  content: string;

  @ApiProperty({
    description: 'Display order of the section',
    example: 1,
  })
  displayOrder: number;

  @ApiProperty({
    description: 'Section type',
    example: 'REQUIREMENTS',
  })
  type: string;

  @ApiProperty({
    description: 'Whether the section is visible',
    example: true,
  })
  isVisible: boolean;
}

export class PublicJobSearchResultDto {
  @ApiProperty({
    description: 'Job post ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Job title',
    example: 'Senior Software Engineer',
  })
  title: string;

  @ApiProperty({
    description: 'Job description',
    example: 'We are looking for a senior software engineer...',
  })
  description: string;

  @ApiProperty({
    description: 'Job location details',
    type: PublicJobLocationDto,
  })
  location: PublicJobLocationDto;

  @ApiProperty({
    description: 'Job sections',
    type: [PublicJobSectionDto],
  })
  sections: PublicJobSectionDto[];

  @ApiProperty({
    description: 'Job published date',
    example: '2024-03-20T10:00:00Z',
  })
  publishedAt: Date;
}

export class PublicJobSearchResponseDto {
  @ApiProperty({
    description: 'List of job search results',
    type: [PublicJobSearchResultDto],
  })
  data: PublicJobSearchResultDto[];
}
