import { ApiProperty } from '@nestjs/swagger';

export class JobTitleDto {
  @ApiProperty({
    description: 'Job post ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Job title',
    example: 'Senior Software Engineer',
  })
  title: string;
}

export class JobTitlesResponseDto {
  @ApiProperty({
    description: 'List of job titles',
    type: [JobTitleDto],
  })
  data: JobTitleDto[];
}
