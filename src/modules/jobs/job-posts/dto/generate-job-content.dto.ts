import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsObject, IsNotEmpty, IsOptional } from 'class-validator';

export enum JobContentType {
  JOB_DESCRIPTION = 'Job Description',
  JOB_REQUIREMENTS = 'Job Requirements',
  JOB_RESPONSIBILITIES = 'Job Responsibilities',
  JOB_BENEFITS = 'Job Benefits',
}

export class JobHighlights {
  @ApiProperty({
    description: 'Type of employment',
    example: 'Full-time',
  })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  jobType: string;

  @ApiProperty({
    description: 'Minimum age requirement',
    example: '18',
  })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  ageLimit: string;

  @ApiProperty({
    description: 'Compensation range',
    example: {
      min: 60000,
      max: 90000,
      currency: 'USD',
    },
  })
  @IsObject()
  @IsOptional()
  compensation: {
    min: number;
    max: number;
    currency: string;
  };

  @ApiProperty({
    description: 'Work shift',
    example: 'Day shift',
  })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  shift: string;
}

export class GenerateJobContentDto {
  @ApiProperty({
    description: 'Type of content to generate',
    example: 'Job Description',
  })
  @IsString()
  generateType: string;

  @ApiProperty({
    description: 'Job title',
    example: 'Senior Software Engineer',
  })
  @IsString()
  @IsNotEmpty()
  jobTitle: string;

  @ApiProperty({
    description: 'Job location',
    example: 'San Francisco, CA',
  })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  jobLocation: string;

  @ApiProperty({
    description:
      'Job highlights including type, age limit, compensation, and shift',
    type: JobHighlights,
  })
  @IsObject()
  @IsOptional()
  jobHighlights: JobHighlights;
}

export class GenerateJobContentResponseDto {
  @ApiProperty({
    description: 'Generated content based on the requested type',
    example:
      'We are seeking a talented Senior Software Engineer to join our dynamic team...',
  })
  content: string;
}
