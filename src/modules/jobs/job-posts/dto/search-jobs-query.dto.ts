import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString, IsUUID } from 'class-validator';

export class SearchJobsRequestDto {
  @ApiProperty({ required: true })
  @IsUUID()
  tenantId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    required: false,
    description: 'Search by location name, city, or address',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiProperty({
    required: false,
    description: 'Array of keys to include in the response',
    example: ['id', 'title', 'description', 'location', 'publishedAt'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keys?: string[];

  @ApiProperty({
    required: false,
    description: 'Array of section types to include in the response',
    example: ['REQUIREMENTS', 'BENEFITS', 'RESPONSIBILITIES'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  sectionTypes?: string[];
}
