import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JobContentGenerationService } from './job-content-generation.service';
import {
  GenerateJobContentDto,
  GenerateJobContentResponseDto,
} from './dto/generate-job-content.dto';
import { JwtAuthGuard } from '../../../authentication/jwt-auth.guard';
import { PermissionsGuard } from '../../../common/guards/permissions.guard';
import { RequirePermissions } from '../../../common/decorators/metadata/permissions.decorator';
import {
  PermissionModules,
  PermissionActions,
} from '../../../models/roles/constants/role.constants';

@ApiTags('Job Content Generation')
@Controller('job-content')
@UseGuards(JwtAuthGuard, PermissionsGuard)
@ApiBearerAuth()
export class JobContentGenerationController {
  constructor(
    private readonly jobContentGenerationService: JobContentGenerationService,
  ) {}

  @Post('generate')
  @ApiOperation({ summary: 'Generate job-related content based on type' })
  @ApiResponse({
    status: 201,
    description: 'Returns generated job content based on the requested type',
    type: GenerateJobContentResponseDto,
  })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.CREATE_JOBS,
  })
  async generateJobContent(
    @Body() generateJobContentDto: GenerateJobContentDto,
  ): Promise<GenerateJobContentResponseDto> {
    const content = await this.jobContentGenerationService.generateContent(
      generateJobContentDto,
    );
    return { content };
  }
}
