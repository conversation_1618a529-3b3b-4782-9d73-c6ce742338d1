import {
  Injectable,
  NotFoundException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { JobPostRepository } from '../../../models/jobs/repositories/job-post.repository';
import { JobPostSectionRepository } from '../../../models/jobs/repositories/job-post-section.repository';
import { CreateJobPostDto } from './dto/create-job-post.dto';
import { UpdateJobPostDto } from './dto/update-job-post.dto';
import { JobPost } from '../../../models/jobs/entities/job-post.entity';
import {
  JobPostSortBy,
  JobPostSortColumn,
  JobPostStatus,
} from '../../../models/jobs/constants/job.constants';
import { JobPostListResponseDto } from './dto/job-post-list.dto';
import { JobPostDetailDto } from './dto/job-post-detail.dto';
import { JobTemplateRepository } from '../../../models/jobs/repositories/job-template.repository';
import { LocationsRepository } from '../../../models/locations/repositories/locations.repository';
import { TenantContextStorage } from '../../tenant/tenant-context';
import { JobPostSectionType } from '../../../models/jobs/entities/job-post-section.entity';
import { SectionDefinitionRepository } from '../../../models/jobs/repositories/section-definition.repository';
import { HirenetixAiEngineService } from '../../../common/providers/hirenetix-ai-engine/hirenetix-ai-engine.service';
import { JOB_POST_MESSAGES } from '../../../common/constants/message.constants';
import { JobTitleDto } from './dto/job-titles-response.dto';
import { ILike } from 'typeorm';

@Injectable()
export class JobPostsService {
  private readonly logger = new Logger(JobPostsService.name);

  constructor(
    private readonly jobPostRepository: JobPostRepository,
    private readonly jobPostSectionRepository: JobPostSectionRepository,
    private readonly jobTemplateRepository: JobTemplateRepository,
    private readonly locationRepository: LocationsRepository,
    private readonly sectionDefinitionRepository: SectionDefinitionRepository,
    private readonly hirenetixAiEngineService: HirenetixAiEngineService,
  ) {}

  async getJobPostById(id: string): Promise<JobPostDetailDto> {
    const jobPost = await this.jobPostRepository.findByIdWithSections(id);

    if (!jobPost) {
      throw new NotFoundException(`Job post with ID "${id}" not found`);
    }

    return {
      id: jobPost.id,
      title: jobPost.title,
      description: jobPost.description,
      status: jobPost.status,
      createdBy: jobPost.createdBy
        ? {
            id: jobPost.createdBy.id,
            name: jobPost.createdBy.fullName,
          }
        : null,
      location: jobPost.location
        ? {
            id: jobPost.location.id,
            name: jobPost.location.name,
            city: jobPost.location.city,
            address: jobPost.location.address,
            country: null,
          }
        : null,
      jobTemplateId: jobPost.originalTemplateId,
      publishedAt: jobPost.publishedAt,
      createdAt: jobPost.createdAt,
      updatedAt: jobPost.updatedAt,
      sections:
        jobPost.sections?.map((section) => ({
          jobTemplateSectionId: section.id,
          title: section.title,
          content: section.content,
          displayOrder: section.displayOrder,
          sectionDefinitionId: section.sectionDefinitionId,
          type: section.type,
          isVisibleDefault: section.isVisible,
          isEditable: section.isEditable,
          isDeletable: section.isDeletable,
        })) || [],
    };
  }

  /**
   * Publish a job post and add it to the vector store
   * @param id - The ID of the job post to publish
   * @returns The published job post
   */
  async publishJobPost(id: string): Promise<JobPostDetailDto> {
    const jobPost = await this.jobPostRepository.findByIdWithSections(id);

    if (!jobPost) {
      throw new NotFoundException(`Job post with ID "${id}" not found`);
    }

    try {
      // Add the job to vector store using the AI engine service
      this.hirenetixAiEngineService.addJobToVectorStore(jobPost);
    } catch (error) {
      this.logger.error('Failed to add job to vector store:', error.message);
    }

    //check if the job post is already published
    if (jobPost.status === JobPostStatus.PUBLISHED) {
      throw new BadRequestException(
        `Job post with ID "${id}" is already published`,
      );
    }

    // Update the job post status and published date
    const updatedJobPost = await this.jobPostRepository.update(id, {
      status: JobPostStatus.PUBLISHED,
      publishedAt: new Date(),
    });

    try {
      // Add the job to vector store using the AI engine service
      this.hirenetixAiEngineService.addJobToVectorStore(jobPost);
    } catch (error) {
      this.logger.error('Failed to add job to vector store:', error.message);
    }

    return this.getJobPostById(updatedJobPost.id);
  }

  async getAllJobPosts(
    page: number = 1,
    limit: number = 10,
    startDate?: string,
    endDate?: string,
    search?: string,
    sortOrder?: JobPostSortBy,
    sortColumn?: JobPostSortColumn,
    status?: JobPostStatus,
    isActive?: any,
  ): Promise<JobPostListResponseDto> {
    const [jobPosts, totalItems] =
      await this.jobPostRepository.findAllWithSearchRelations(
        page,
        limit,
        search,
        startDate,
        endDate,
        sortOrder,
        sortColumn,
        status,
        isActive,
      );

    const totalPages = Math.ceil(totalItems / limit);

    const data = jobPosts.map((post) => ({
      id: post.id,
      title: post.title,
      description: post.description,
      status: post.status,
      isActive: post.isActive,
      publishedAt: post.publishedAt,
      createdAt: post.createdAt,
      updatedAt: post.updatedAt,
      createdBy: {
        id: post?.createdBy?.id,
        fullName: post?.createdBy?.fullName,
        email: post?.createdBy?.email,
      },
      location: {
        id: post?.location?.id,
        name: post?.location?.name,
        city: post?.location?.city,
        address: post?.location?.address,
      },
      jobApplications: [],
      scheduledInterviews: 0,
    }));

    return {
      data,
      totalItems,
      currentPage: page,
      itemsPerPage: limit,
      totalPages,
    };
  }

  /**
   * Create a new job post
   * @param createJobPostDto - The DTO containing job post details
   * @param userId - The ID of the user creating the job post
   * @param locationId - The ID of the location for the job post
   * @returns The created job post
   */
  async createJobPost(
    createJobPostDto: CreateJobPostDto,
    userId: string,
    locationId: string,
  ): Promise<JobPost> {
    //Check if the job template exists
    const jobTemplate = await this.jobTemplateRepository.findById(
      createJobPostDto.jobTemplateId,
    );

    const tenantId = TenantContextStorage.getCurrentTenantId();

    if (!jobTemplate) {
      throw new NotFoundException(
        `Job template with ID "${createJobPostDto.jobTemplateId}" not found`,
      );
    }

    //Check if the location exists
    const location = await this.locationRepository.findById(locationId);

    if (!location) {
      throw new NotFoundException(`Location with ID "${locationId}" not found`);
    }

    const jobPost = await this.jobPostRepository.create({
      title: createJobPostDto.title,
      description: createJobPostDto.description,
      originalTemplateId: createJobPostDto.jobTemplateId,
      createdById: userId,
      locationId: locationId || createJobPostDto.locationId,
      status: JobPostStatus.DRAFT,
      tenantId: tenantId,
    });

    const sections = [];
    if (createJobPostDto.sections?.length) {
      for (let i = 0; i < createJobPostDto.sections.length; i++) {
        const section = createJobPostDto.sections[i];

        if (!section.sectionDefinitionId) {
          let sectionDefinition =
            await this.sectionDefinitionRepository.findByDefaultName(
              section.title,
            );

          if (!sectionDefinition) {
            //insert new section definition
            sectionDefinition = await this.sectionDefinitionRepository.create({
              defaultName: section.title,
              isSystemDefined: false,
              tenantId: tenantId,
            });
          }

          createJobPostDto.sections[i].sectionDefinitionId =
            sectionDefinition.id;
        }

        //Check jobTemplateSectionId exist or not
        const jobTemplateSection = await this.jobTemplateRepository.findById(
          section.jobTemplateSectionId,
        );

        if (!jobTemplateSection) {
          section.jobTemplateSectionId = null;
        }

        sections.push({
          jobPostId: jobPost.id,
          title: section.title,
          content: section.content,
          displayOrder: section.displayOrder,
          isVisible: section.isVisibleDefault,
          sectionDefinitionId: section.sectionDefinitionId,
          originalTemplateSectionId: section.jobTemplateSectionId,
          type: section.type as JobPostSectionType,
        });
      }
    }
    await this.jobPostSectionRepository.createMany(sections);

    try {
      // Add the job to vector store using the AI engine service
      this.hirenetixAiEngineService.addJobToVectorStore(jobPost);
    } catch (error) {
      this.logger.error('Failed to add job to vector store:', error.message);
    }
    return jobPost;
  }

  async updateJobPost(
    id: string,
    updateJobPostDto: UpdateJobPostDto,
  ): Promise<JobPostDetailDto> {
    const jobPost = await this.jobPostRepository.findByIdWithSections(id);

    if (!jobPost) {
      throw new NotFoundException(`Job post with ID "${id}" not found`);
    }

    const tenantId = TenantContextStorage.getCurrentTenantId();

    // Check if the job template exists
    if (updateJobPostDto.jobTemplateId) {
      const jobTemplate = await this.jobTemplateRepository.findById(
        updateJobPostDto.jobTemplateId,
      );

      if (!jobTemplate) {
        throw new NotFoundException(
          `Job template with ID "${updateJobPostDto.jobTemplateId}" not found`,
        );
      }
    }

    // Update basic job post information
    await this.jobPostRepository.update(id, {
      title: updateJobPostDto.title,
      description: updateJobPostDto.description,
      locationId: updateJobPostDto.locationId,
      originalTemplateId: updateJobPostDto.jobTemplateId,
    });

    // Create new sections
    if (updateJobPostDto.sections?.length) {
      for (let i = 0; i < updateJobPostDto.sections.length; i++) {
        const section = updateJobPostDto.sections[i];

        if (!section?.sectionDefinitionId) {
          let sectionDefinition =
            await this.sectionDefinitionRepository.findByDefaultName(
              section.title,
            );

          if (!sectionDefinition) {
            sectionDefinition = await this.sectionDefinitionRepository.create({
              defaultName: section.title,
              isSystemDefined: false,
              tenantId: tenantId,
            });
          }

          section.sectionDefinitionId = sectionDefinition.id;
        }

        // Find existing section by jobTemplateSectionId if it exists
        let existingSection = null;
        if (section.jobTemplateSectionId) {
          existingSection = await this.jobPostSectionRepository.findById(
            section.jobTemplateSectionId,
          );
        }

        const sectionData = {
          title: section.title,
          content: section.content,
          displayOrder: section.displayOrder,
          isVisible: section.isVisibleDefault,
          sectionDefinitionId: section.sectionDefinitionId,
          originalTemplateSectionId: null,
          type: section.type as JobPostSectionType,
          isEditable: section.isEditable,
          isDeletable: section.isDeletable,
        };

        if (existingSection) {
          // Update existing section
          await this.jobPostSectionRepository.update(
            existingSection.id,
            sectionData,
          );
        } else {
          // Create new section
          await this.jobPostSectionRepository.create({
            ...sectionData,
            jobPostId: id,
          });
        }
      }
    }

    try {
      // Add the job to vector store using the AI engine service
      this.hirenetixAiEngineService.addJobToVectorStore(jobPost);
    } catch (error) {
      this.logger.error('Failed to add job to vector store:', error.message);
    }

    return this.getJobPostById(id);
  }

  async findPublicJobPosts(
    page: number = 1,
    limit: number = 50,
    organisationId?: string,
  ): Promise<[JobPost[], number]> {
    return this.jobPostRepository.findPublicJobPosts(
      page,
      limit,
      organisationId,
    );
  }

  async findPublicJobPostById(id: string, tenantId: string): Promise<JobPost> {
    const jobPost = await this.jobPostRepository.findPublicJobPostById(
      id,
      tenantId,
    );

    if (!jobPost) {
      throw new NotFoundException(`Job post with ID "${id}" not found`);
    }

    return jobPost;
  }

  async deleteJobPost(id: string): Promise<void> {
    const jobPost = await this.jobPostRepository.findByIdWithSections(id);

    if (!jobPost) {
      throw new NotFoundException(JOB_POST_MESSAGES.JOB_POST_NOT_FOUND(id));
    }

    // Delete the job post
    await this.jobPostRepository.delete(id);

    try {
      // Delete the job from vector store using the AI engine service
      await this.hirenetixAiEngineService.deleteJobFromVectorStore(
        id,
        jobPost?.tenantId,
      );
    } catch (error) {
      this.logger.error(
        'Failed to delete job from vector store:',
        error.message,
      );
    }
  }

  async toggleJobPublishedStatus(id: string): Promise<JobPostDetailDto> {
    const jobPost = await this.jobPostRepository.findByIdWithSections(id);

    if (!jobPost) {
      throw new NotFoundException(JOB_POST_MESSAGES.JOB_POST_NOT_FOUND(id));
    }

    // Toggle the published status
    const updatedJobPost = await this.jobPostRepository.update(id, {
      isActive: !jobPost.isActive,
    });

    return this.getJobPostById(updatedJobPost.id);
  }

  async getJobTitlesByTenantId(
    tenantId: string,
    search: string = '',
  ): Promise<JobTitleDto[]> {
    if (!tenantId) {
      throw new BadRequestException('Tenant ID is required');
    }

    const jobPosts = await this.jobPostRepository.find({
      select: ['id', 'title'],
      where: {
        tenantId,
        status: JobPostStatus.PUBLISHED,
        isActive: true,
        title: ILike(`%${search}%`),
      },
      order: {
        title: 'ASC',
      },
    });

    return jobPosts.map((post) => ({
      id: post.id,
      title: post.title,
    }));
  }

  async searchJobsByTitleAndLocation(
    tenantId: string,
    title?: string,
    location?: string,
  ): Promise<JobPost[]> {
    if (!tenantId) {
      throw new BadRequestException('Tenant ID is required');
    }

    return this.jobPostRepository.searchJobsByTitleAndLocation(
      tenantId,
      title,
      location,
    );
  }
}
