import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JobPostsService } from './job-posts.service';
import { PublicJobPostSerializer } from '../../../models/jobs/serializers/public-job-post.serializer';
import { ThrottlerGuard } from '@nestjs/throttler';
import { Transform } from 'class-transformer';
import { IsOptional, IsInt, Min, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { PublicJobPostListResponseDto } from './dto/public-job-post-list.dto';
import { JobTitlesResponseDto } from './dto/job-titles-response.dto';
import { PublicJobSearchResponseDto } from './dto/public-job-search-response.dto';
import { SearchJobsRequestDto } from './dto/search-jobs-query.dto';
import { GetJobPostQueryDto } from './dto/get-job-post-query.dto';

class GetPublicJobPostsQueryDto {
  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ required: false, default: 10 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  limit?: number = 50;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  tenantId?: string;
}

@ApiTags('Public Job Posts')
@Controller('public/job-posts')
@UseGuards(ThrottlerGuard)
export class PublicJobPostsController {
  constructor(private readonly jobPostsService: JobPostsService) {}

  @Get('titles')
  @ApiOperation({ summary: 'Get job titles by tenant ID' })
  @ApiQuery({
    name: 'tenantId',
    required: true,
    description: 'ID of the organization/tenant',
    type: String,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search for a job title',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns a list of job titles and IDs for a given tenant.',
    type: JobTitlesResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Tenant ID is required',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests',
  })
  async getJobTitlesByTenantId(
    @Query('tenantId') tenantId: string,
    @Query('search') search: string = '',
  ): Promise<JobTitlesResponseDto> {
    console.log('tenantId :', tenantId);
    const jobTitles = await this.jobPostsService.getJobTitlesByTenantId(
      tenantId,
      search,
    );
    return { data: jobTitles };
  }

  @Post('search-jobs')
  @ApiOperation({ summary: 'Search jobs by title and location' })
  @ApiResponse({
    status: 200,
    description: 'Returns a list of jobs matching the search criteria.',
    type: PublicJobSearchResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests',
  })
  async searchJobs(
    @Body() request: SearchJobsRequestDto,
  ): Promise<PublicJobSearchResponseDto> {
    const jobs = await this.jobPostsService.searchJobsByTitleAndLocation(
      request.tenantId,
      request.title,
      request.location,
    );

    const defaultKeys = [
      'id',
      'title',
      'description',
      'location',
      'publishedAt',
    ];
    const selectedKeys = request.keys?.length ? request.keys : defaultKeys;

    return {
      data: jobs.map((job) => {
        const result: any = {};
        selectedKeys.forEach((key) => {
          if (key === 'location' && job.location) {
            result.location = {
              id: job.location.id,
              name: job.location.name,
              city: job.location.city,
              address: job.location.address,
            };
          } else if (key === 'sections' && job.sections) {
            // Filter sections by type if sectionTypes are specified
            const filteredSections = request.sectionTypes?.length
              ? job.sections.filter((section) =>
                  request.sectionTypes.includes(section.type),
                )
              : job.sections;

            result.sections = filteredSections.map((section) => ({
              id: section.id,
              title: section.title,
              content: section.content,
              displayOrder: section.displayOrder,
              type: section.type,
              isVisible: section.isVisible,
            }));
          } else if (key in job) {
            result[key] = job[key];
          }
        });
        return result;
      }),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all published job posts' })
  @ApiResponse({
    status: 200,
    description: 'Returns a list of published job posts.',
    type: PublicJobPostListResponseDto,
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests',
  })
  async getJobPosts(
    @Query() query: GetPublicJobPostsQueryDto,
  ): Promise<PublicJobPostListResponseDto> {
    const [jobPosts, totalItems] =
      await this.jobPostsService.findPublicJobPosts(
        query.page,
        query.limit,
        query.tenantId,
      );

    const totalPages = Math.ceil(totalItems / query.limit);

    return {
      data: PublicJobPostSerializer.serializeMany(jobPosts),
      totalItems,
      currentPage: query.page,
      itemsPerPage: query.limit,
      totalPages,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get job post details by ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the job post details.',
    type: PublicJobPostSerializer,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters or tenant ID is required',
  })
  @ApiResponse({
    status: 404,
    description: 'Job post not found',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests',
  })
  async getJobPost(
    @Param('id') id: string,
    @Query() query: GetJobPostQueryDto,
  ): Promise<PublicJobPostSerializer> {
    if (!query.tenantId) {
      throw new BadRequestException('Tenant ID is required');
    }

    const jobPost = await this.jobPostsService.findPublicJobPostById(
      id,
      query.tenantId,
    );
    return PublicJobPostSerializer.serialize(jobPost);
  }
}
