import { Injectable, Logger } from '@nestjs/common';
import { DeepseekService } from '../../../common/providers/deepseek/deepseek.service';
import { OpenAIService } from '../../../common/providers/openai/openai.service';
import { GenerateJobContentDto } from './dto/generate-job-content.dto';

@Injectable()
export class JobContentGenerationService {
  private readonly logger = new Logger(JobContentGenerationService.name);

  constructor(
    private readonly deepseekService: DeepseekService,
    private readonly openaiService: OpenAIService,
  ) {}

  async generateContent(
    generateJobContentDto: GenerateJobContentDto,
  ): Promise<string> {
    try {
      const prompt = this.buildPrompt(generateJobContentDto);
      let response: string = '';
      if (process.env.AI_PROVIDER === 'openai') {
        response = await this.openaiService.generateJobPostContent(prompt);
      } else {
        response = await this.deepseekService.generateJobPostContent(prompt);
      }
      return response;
    } catch (error) {
      this.logger.error(`Error generating job content: ${error.message}`);
      throw error;
    }
  }

  private buildPrompt(dto: GenerateJobContentDto): string {
    const { generateType, jobTitle, jobLocation = '', jobHighlights } = dto;
    const {
      jobType = '',
      ageLimit = '',
      compensation = { min: 0, max: 0, currency: '' },
      shift = '',
    } = jobHighlights || {};

    return `You are an expert HR content strategist and copywriter.

    Your task is to generate engaging and professional generateType: **${generateType}** content for the following job opening. The output must be in clean, valid HTML format so it can be rendered directly in a web-based textarea or job posting editor.

    Job Details:
    - Title: ${jobTitle}
    - Location: ${jobLocation}
    - Employment Type: ${jobType}
    - Age Requirement: ${ageLimit ? ageLimit + '+' : 'Not specified'}
    - Compensation Range: ${compensation.min}-${compensation.max} ${compensation.currency}
    - Shift: ${shift}

    Instructions:
    - Generate original content each time — avoid repetition or templates.
    - Keep it under ~100 words unless the section type requires bullets (e.g., responsibilities, qualifications, benefits).
    - Tailor the tone and detail based on the section type: generateType:**${generateType}**
    - Use <ul><li> HTML list elements for bullet sections (like qualifications or responsibilities).
    - Use <p> for descriptive paragraphs (like job description or summary).
    - Make the content informative, non-generic, and attractive to skilled applicants.
    - Avoid clichés and buzzwords; focus on clarity and value.

    ### Section Type Validation:
    If the generateType is not one of the following valid job-related sections, return only this JSON error message:

    Invalid section title 

    Provide only the final HTML output below without any additional text or comments, also do not include html tag or classes or heading tags in the response:
  `;
  }
}
