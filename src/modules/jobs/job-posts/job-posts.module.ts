import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobPost } from '../../../models/jobs/entities/job-post.entity';
import { JobPostSection } from '../../../models/jobs/entities/job-post-section.entity';
import { JobPostRepository } from '../../../models/jobs/repositories/job-post.repository';
import { JobPostSectionRepository } from '../../../models/jobs/repositories/job-post-section.repository';
import { JobPostsController } from './job-posts.controller';
import { JobPostsService } from './job-posts.service';
import { RolesModule } from '../../roles-permissions/roles.module';
import { JobTemplateRepository } from '../../../models/jobs/repositories/job-template.repository';
import { JobTemplate } from '../../../models/jobs/entities/job-template.entity';
import { LocationsModule } from '../../locations/locations.module';
import { DeepseekModule } from '../../../common/providers/deepseek/deepseek.module';
import { JobContentGenerationController } from './job-content-generation.controller';
import { JobContentGenerationService } from './job-content-generation.service';
import { SectionDefinitionRepository } from '../../../models/jobs/repositories/section-definition.repository';
import { SectionDefinition } from '../../../models/jobs/entities/section-definition.entity';
import { PublicJobPostsController } from './public-job-posts.controller';
import { ThrottlerModule } from '@nestjs/throttler';
import { HirenetixAiEngineModule } from '../../../common/providers/hirenetix-ai-engine/hirenetix-ai-engine.module';
import { OpenAIModule } from '../../../common/providers/openai/openai.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      JobPost,
      JobPostSection,
      JobTemplate,
      SectionDefinition,
    ]),
    RolesModule,
    LocationsModule,
    DeepseekModule,
    OpenAIModule,
    HirenetixAiEngineModule,
    ThrottlerModule.forRoot({
      throttlers: [
        {
          ttl: 60000,
          limit: 100,
        },
      ],
    }),
  ],
  controllers: [
    JobPostsController,
    JobContentGenerationController,
    PublicJobPostsController,
  ],
  providers: [
    JobPostsService,
    JobPostRepository,
    JobPostSectionRepository,
    JobTemplateRepository,
    JobContentGenerationService,
    SectionDefinitionRepository,
  ],
  exports: [JobPostsService, JobPostRepository],
})
export class JobPostsModule {}
