import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { ConversationalFlow } from '../../../models/jobs/entities/conversational-flow.entity';
import { FlowQuestion } from '../../../models/jobs/entities/flow-question.entity';
import { FlowOutcome } from '../../../models/jobs/entities/flow-outcome.entity';
import { FlowCondition } from '../../../models/jobs/entities/flow-condition.entity';
import { UpdateFlowDto } from './dto/update-flow.dto';
import {
  OutcomeTypeEnum,
  QuestionTypeEnum,
} from '../../../models/jobs/constants/job.constants';
import { DeepseekService } from '../../../common/providers/deepseek/deepseek.service';
import { OpenAIService } from '../../../common/providers/openai/openai.service';
@Injectable()
export class ConversationalJobFlowService {
  constructor(
    @InjectRepository(ConversationalFlow)
    private readonly flowRepository: Repository<ConversationalFlow>,
    @InjectRepository(FlowQuestion)
    private readonly questionRepository: Repository<FlowQuestion>,
    @InjectRepository(FlowOutcome)
    private readonly outcomeRepository: Repository<FlowOutcome>,
    @InjectRepository(FlowCondition)
    private readonly conditionRepository: Repository<FlowCondition>,
    private dataSource: DataSource,
    private deepseekService: DeepseekService,
    private openaiService: OpenAIService,
  ) {}

  async getFlowByJobPostId(jobPostId: string, secretKey?: string) {
    try {
      // Check if secret key is provided and valid when used
      if (secretKey) {
        const validSecretKey = process.env.API_SECRET;
        if (!validSecretKey || secretKey !== validSecretKey) {
          console.log(validSecretKey, secretKey);
          throw new Error('Invalid secret key provided');
        }
      }

      // Get the flow with basic relations
      const flow = await this.flowRepository.findOne({
        where: { job_post_id: jobPostId },
        relations: ['questions', 'outcomes'],
      });

      if (!flow) {
        return {
          success: true,
          data: {},
          message: `Flow with Job Post ID ${jobPostId} not found`,
        };
      }

      // Directly query conditions for the questions in this flow
      const conditions = await this.conditionRepository
        .createQueryBuilder('condition')
        .leftJoinAndSelect('condition.nextQuestion', 'nextQuestion')
        .leftJoinAndSelect('condition.nextOutcome', 'nextOutcome')
        .where('condition.source_question_id IN (:...questionIds)', {
          questionIds: flow.questions.map((q) => q.id),
        })
        .getMany();

      // Log conditions that were found
      console.log(`Total conditions found: ${conditions.length}`);
      if (conditions.length > 0) {
        console.log('Sample condition:', {
          id: conditions[0].id,
          source_question_id: conditions[0].source_question_id,
          trigger_answer_value: conditions[0].trigger_answer_value,
          next_question_id: conditions[0].next_question_id,
          next_outcome_id: conditions[0].next_outcome_id,
        });
      }

      // Create map of question ID to conditions
      const questionIdToConditions = new Map<number, FlowCondition[]>();
      conditions.forEach((condition) => {
        if (!questionIdToConditions.has(condition.source_question_id)) {
          questionIdToConditions.set(condition.source_question_id, []);
        }
        questionIdToConditions
          .get(condition.source_question_id)
          .push(condition);
      });

      // Add conditions to questions
      flow.questions.forEach((question) => {
        question.source_conditions =
          questionIdToConditions.get(question.id) || [];
        console.log(
          `Question ${question.id}: loaded ${question.source_conditions.length} conditions`,
        );
      });

      // Transform the data to include client references
      const questionIdToClientRef = new Map<number, string>();
      const outcomeIdToClientRef = new Map<number, string>();

      // Generate client references for questions and outcomes
      flow.questions.forEach((question, index) => {
        questionIdToClientRef.set(question.id, `q${index + 1}`);
      });

      flow.outcomes.forEach((outcome, index) => {
        outcomeIdToClientRef.set(outcome.id, `o${index + 1}`);
      });

      const rejectionOutcome = flow.outcomes.find((o) => o.is_default_reject);

      // Transform questions with client references
      const transformedQuestions = flow.questions.map((question) => {
        // Transform conditions to include client references
        const conditions = Array.isArray(question.source_conditions)
          ? question.source_conditions.map((condition: FlowCondition) => {
              // Use the question ID from either next_question_id or the nextQuestion property
              const nextQuestionId =
                condition.next_question_id ||
                (condition.nextQuestion ? condition.nextQuestion.id : null);

              // Use the outcome ID from either next_outcome_id or the nextOutcome property
              const nextOutcomeId =
                condition.next_outcome_id ||
                (condition.nextOutcome ? condition.nextOutcome.id : null);

              const nextQuestionClientRef = nextQuestionId
                ? nextQuestionId.toString()
                : null;

              const nextOutcomeClientRef = nextOutcomeId
                ? nextOutcomeId.toString()
                : null;

              return {
                trigger_answer_value: condition.trigger_answer_value,
                next_question_client_ref: nextQuestionClientRef,
                next_outcome_client_ref: nextOutcomeClientRef,
              };
            })
          : [];

        return {
          id: question.id,
          client_ref: question.id.toString(),
          question_type: question.question_type,
          heading: question.heading,
          sub_text: question.sub_text,
          placeholder_text: question.placeholder_text,
          options: question.options,
          is_mandatory: question.is_mandatory,
          display_order: question.display_order,
          allowed_file_types: question.allowed_file_types,
          max_file_size_mb: question.max_file_size_mb,
          message_style: question.message_style,
          conditions,
        };
      });

      // Transform outcomes with client references
      const transformedOutcomes = flow.outcomes.map((outcome) => {
        return {
          id: outcome.id,
          client_ref: outcome.id.toString(),
          outcome_type: outcome.outcome_type,
          message_text: outcome.message_text,
          is_default_reject: outcome.is_default_reject,
          is_default_success: outcome.is_default_success,
        };
      });

      return {
        success: true,
        data: {
          id: flow.id,
          job_post_id: flow.job_post_id,
          start_question_id: flow.start_question_id,
          questions: transformedQuestions,
          outcomes: transformedOutcomes,
          genesis: flow.genesis ? { message_text: flow.genesis } : null,
          rejection_outcome: rejectionOutcome.id,
        },
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Error fetching flow: ${error.message}`);
    }
  }

  async updateFlow(id: string, updateData: UpdateFlowDto) {
    const queryRunner = this.dataSource.createQueryRunner();

    try {
      // Connect and start transaction
      await queryRunner.connect();
      await queryRunner.startTransaction();

      // Get the flow
      let flow = await this.flowRepository.findOne({
        where: { job_post_id: id },
      });

      if (updateData?.generate_with_ai && !flow) {
        // Generate the required payload with AI
        flow = await this.generateAIFlow(id, updateData, queryRunner);

        // When using AI generation, we need to ensure the original flow is properly set up
        console.log(
          `AI flow generated with ID: ${flow.id}, setting up base flow properties`,
        );

        // If the flow was just created, we don't have a start_question_id yet, it will be set later
        // when we create the questions and outcomes
      }

      if (!flow && !updateData?.generate_with_ai) {
        throw new NotFoundException(`Flow with Job Post ID ${id} not found`);
      }

      // Create a new flow object with updated properties
      const updatedFlow = {
        ...flow,
        job_post_id: updateData.job_post_id || flow.job_post_id,
        genesis: updateData.genesis
          ? updateData.genesis.message_text
          : flow.genesis,
      };

      // Debug log for start question client ref
      console.log(
        `Start question client ref: ${updateData.start_question_client_ref}`,
      );

      // Delete all existing conditions first (due to foreign key constraints)
      await queryRunner.manager.delete(FlowCondition, {
        source_question_id: In(
          await queryRunner.manager
            .createQueryBuilder()
            .select('question.id')
            .from(FlowQuestion, 'question')
            .where('question.flow_id = :flowId', { flowId: flow.id })
            .getRawMany()
            .then((results) => results.map((r) => r.id)),
        ),
      });

      // Delete all existing questions
      await queryRunner.manager.delete(FlowQuestion, { flow_id: flow.id });

      // Delete all existing outcomes
      await queryRunner.manager.delete(FlowOutcome, { flow_id: flow.id });

      // Maps to store client references to database IDs
      const clientRefToQuestionId = new Map<string, number>();
      const clientRefToOutcomeId = new Map<string, number>();

      // Create new questions
      for (const questionDto of updateData.questions) {
        const newQuestion = this.questionRepository.create({
          flow_id: flow.id,
          question_type: questionDto.question_type,
          heading: questionDto.heading,
          sub_text: questionDto.sub_text,
          placeholder_text: questionDto.placeholder_text,
          options: questionDto.options,
          is_mandatory: questionDto.is_mandatory,
          display_order: questionDto.display_order || 0,
          allowed_file_types: questionDto.allowed_file_types,
          max_file_size_mb: questionDto.max_file_size_mb,
          message_style: questionDto.message_style,
        });

        const savedQuestion = await queryRunner.manager.save(newQuestion);
        clientRefToQuestionId.set(questionDto.client_ref, savedQuestion.id);

        // If this is the start question, update the flow object
        if (questionDto.client_ref === updateData.start_question_client_ref) {
          updatedFlow.start_question_id = savedQuestion.id;
          console.log(
            `Setting start_question_id to ${savedQuestion.id} from client_ref ${questionDto.client_ref}`,
          );
        }
      }

      // Create new outcomes
      for (const outcomeDto of updateData.outcomes) {
        const newOutcome = this.outcomeRepository.create({
          flow_id: flow.id,
          outcome_type: outcomeDto.outcome_type as OutcomeTypeEnum,
          message_text: outcomeDto.message_text,
          is_default_reject: outcomeDto.is_default_reject || false,
          is_default_success: outcomeDto.is_default_success || false,
        });

        const savedOutcome = await queryRunner.manager.save(newOutcome);
        clientRefToOutcomeId.set(outcomeDto.client_ref, savedOutcome.id);
      }

      // Ensure start_question_id is set before saving
      if (
        updateData.start_question_client_ref &&
        !updatedFlow.start_question_id
      ) {
        const startQuestionId = clientRefToQuestionId.get(
          updateData.start_question_client_ref,
        );
        if (startQuestionId) {
          updatedFlow.start_question_id = startQuestionId;
          console.log(
            `Setting start_question_id to ${startQuestionId} from client_ref ${updateData.start_question_client_ref} (fallback)`,
          );
        } else {
          console.warn(
            `Could not find question with client_ref ${updateData.start_question_client_ref}`,
          );
        }
      }

      // Log the final updatedFlow object before saving
      console.log('Final updatedFlow object:', {
        id: updatedFlow.id,
        job_post_id: updatedFlow.job_post_id,
        start_question_id: updatedFlow.start_question_id,
      });

      // Save the updated flow with the new start question ID
      await queryRunner.manager.save(ConversationalFlow, updatedFlow);

      // Create conditions after all questions and outcomes are created
      for (const questionDto of updateData.questions) {
        const sourceQuestionId = clientRefToQuestionId.get(
          questionDto.client_ref,
        );

        if (!sourceQuestionId) {
          continue;
        }

        for (const conditionDto of questionDto.conditions) {
          let nextQuestionId = null;
          let nextOutcomeId = null;

          if (conditionDto.next_question_client_ref) {
            nextQuestionId = clientRefToQuestionId.get(
              conditionDto.next_question_client_ref,
            );
          }

          if (conditionDto.next_outcome_client_ref) {
            nextOutcomeId = clientRefToOutcomeId.get(
              conditionDto.next_outcome_client_ref,
            );
          }

          const newCondition = this.conditionRepository.create({
            source_question_id: sourceQuestionId,
            trigger_answer_value: conditionDto.trigger_answer_value,
            next_question_id: nextQuestionId,
            next_outcome_id: nextOutcomeId,
            priority: 0,
          });

          await queryRunner.manager.save(newCondition);
        }
      }

      await queryRunner.commitTransaction();

      // Return the completely refreshed flow with all its relations
      const refreshedFlow = await this.flowRepository.findOne({
        where: { id: flow.id },
        relations: ['questions', 'outcomes'],
      });

      // Get conditions for all questions in this flow
      const conditions = await this.conditionRepository
        .createQueryBuilder('condition')
        .leftJoinAndSelect('condition.nextQuestion', 'nextQuestion')
        .leftJoinAndSelect('condition.nextOutcome', 'nextOutcome')
        .where('condition.source_question_id IN (:...questionIds)', {
          questionIds: refreshedFlow.questions.map((q) => q.id),
        })
        .getMany();

      // Map conditions to questions
      const questionIdToConditions = new Map<number, FlowCondition[]>();
      conditions.forEach((condition) => {
        if (!questionIdToConditions.has(condition.source_question_id)) {
          questionIdToConditions.set(condition.source_question_id, []);
        }
        questionIdToConditions
          .get(condition.source_question_id)
          .push(condition);
      });

      // Add conditions to questions
      refreshedFlow.questions.forEach((question) => {
        question.source_conditions =
          questionIdToConditions.get(question.id) || [];
      });

      // Transform the response to match the desired format
      return {
        success: true,
        data: {
          ...refreshedFlow,
          genesis: refreshedFlow.genesis
            ? { message_text: refreshedFlow.genesis }
            : null,
          // Add transformation for questions and conditions
          questions: refreshedFlow.questions.map((question) => {
            // Transform conditions to include client references

            const conditions =
              question.source_conditions?.map((condition) => {
                // Use the question ID from either next_question_id or the nextQuestion property
                const nextQuestionId =
                  condition.next_question_id ||
                  (condition.nextQuestion ? condition.nextQuestion.id : null);

                // Use the outcome ID from either next_outcome_id or the nextOutcome property
                const nextOutcomeId =
                  condition.next_outcome_id ||
                  (condition.nextOutcome ? condition.nextOutcome.id : null);

                // Use database IDs for client references in response
                const nextQuestionClientRef = nextQuestionId
                  ? nextQuestionId.toString()
                  : null;
                const nextOutcomeClientRef = nextOutcomeId
                  ? nextOutcomeId.toString()
                  : null;

                return {
                  trigger_answer_value: condition.trigger_answer_value,
                  next_question_client_ref: nextQuestionClientRef,
                  next_outcome_client_ref: nextOutcomeClientRef,
                };
              }) || [];

            return {
              id: question.id, // Include the actual database ID
              client_ref: question.id.toString(), // Use database ID as client_ref
              question_type: question.question_type,
              heading: question.heading,
              sub_text: question.sub_text,
              placeholder_text: question.placeholder_text,
              options: question.options,
              is_mandatory: question.is_mandatory,
              display_order: question.display_order,
              allowed_file_types: question.allowed_file_types,
              max_file_size_mb: question.max_file_size_mb,
              message_style: question.message_style,
              conditions,
            };
          }),
          outcomes: refreshedFlow.outcomes.map((outcome) => ({
            id: outcome.id, // Include the actual database ID
            client_ref: outcome.id.toString(), // Use database ID as client_ref
            outcome_type: outcome.outcome_type,
            message_text: outcome.message_text,
            is_default_reject: outcome.is_default_reject,
            is_default_success: outcome.is_default_success,
          })),
          start_question_id: refreshedFlow.start_question_id,
        },
      };
    } catch (error) {
      // Only rollback if transaction is active
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new Error(`Error updating flow: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Generates a conversational flow using AI based on job post content
   * @param jobPostId - ID of the job post
   * @param updateData - Update DTO with initial data
   * @param queryRunner - Active query runner for transaction
   * @returns The created flow entity
   */
  private async generateAIFlow(
    jobPostId: string,
    updateData: UpdateFlowDto,
    queryRunner: any,
  ): Promise<ConversationalFlow> {
    // Create a new flow
    const newFlow = this.flowRepository.create({
      job_post_id: jobPostId,
      name: 'AI Generated Flow',
      description: 'Automatically generated conversational flow',
      genesis:
        updateData.genesis?.message_text ||
        'Welcome to our job application process!',
    });

    const savedFlow = await queryRunner.manager.save(newFlow);

    // Fetch all job post sections to use for AI generation
    const jobPostSections = await queryRunner.manager
      .createQueryBuilder()
      .select('jps')
      .from('job_post_sections', 'jps')
      .where('jps.job_post_id = :jobPostId', { jobPostId })
      .getMany();

    // Log the sections we found for debugging
    console.log(
      `Found ${jobPostSections.length} sections for job post ${jobPostId}`,
    );

    // Debug section structure
    if (jobPostSections.length > 0) {
      console.log(
        'First section structure:',
        JSON.stringify(jobPostSections[0], null, 2),
      );
    }

    // Extract content from all sections for AI prompt
    const sectionContent = jobPostSections.reduce((acc, section) => {
      // Safely handle section.content, ensuring it's properly serialized
      if (section && section.title) {
        if (typeof section.content === 'object' && section.content !== null) {
          acc[section.title] = JSON.stringify(section.content);
        } else {
          acc[section.title] = section.content ? String(section.content) : '';
        }
      }
      return acc;
    }, {});

    // Log section titles found
    console.log(
      'Section titles found:',
      Object.keys(sectionContent).join(', '),
    );

    let questions;
    let outcomes;

    console.log('Sending job post content to Deepseek API');

    try {
      let aiResponse;
      if (process.env.AI_PROVIDER == 'deepseek') {
        // Call deepseek API with the section content object

        aiResponse =
          await this.deepseekService.generateConversationalFlow(sectionContent);

        console.log('Received response from Deepseek API');
      } else {
        // Call openai API with the section content object
        aiResponse =
          await this.openaiService.generateConversationalFlow(sectionContent);
        console.log('Received response from OpenAI API');
      }
      console.log('aiResponse :', aiResponse.questions);

      // Use the AI-generated questions and outcomes
      questions = aiResponse.questions;
      outcomes = aiResponse.outcomes;
    } catch (error) {
      console.error(
        'Error generating conversational flow with AI:',
        error.message,
      );

      // Fallback to default template if AI generation fails
      questions = [
        {
          client_ref: 'temp_q_name',
          question_type: QuestionTypeEnum.TEXT_INPUT,
          heading: 'What is your full name?',
          sub_text: 'Please enter your first and last name.',
          placeholder_text: 'e.g., John Doe',
          is_mandatory: true,
          display_order: 1,
          conditions: [
            {
              trigger_answer_value: '*',
              next_question_client_ref: 'temp_q_email',
              next_outcome_client_ref: null,
            },
          ],
        },
        {
          client_ref: 'temp_q_email',
          question_type: QuestionTypeEnum.TEXT_INPUT,
          heading: 'Your Email Address?',
          placeholder_text: 'e.g., <EMAIL>',
          is_mandatory: true,
          display_order: 2,
          conditions: [
            {
              trigger_answer_value: '*',
              next_question_client_ref: 'temp_q_experience',
            },
          ],
        },
        {
          client_ref: 'temp_q_experience',
          question_type: QuestionTypeEnum.YES_NO,
          heading: 'Do you have relevant experience for this position?',
          is_mandatory: true,
          display_order: 3,
          options: [{ value: 'yes' }, { value: 'no' }],
          conditions: [
            {
              trigger_answer_value: 'yes',
              next_question_client_ref: 'temp_q_details',
            },
            {
              trigger_answer_value: 'no',
              next_outcome_client_ref: 'temp_o_reject_exp',
            },
          ],
        },
        {
          client_ref: 'temp_q_details',
          question_type: QuestionTypeEnum.TEXT_INPUT,
          heading: 'Tell us about your relevant experience.',
          sub_text:
            'Please describe your experience that qualifies you for this role.',
          is_mandatory: true,
          display_order: 4,
          conditions: [
            {
              trigger_answer_value: '*',
              next_question_client_ref: 'temp_q_cv_upload',
            },
          ],
        },
        {
          client_ref: 'temp_q_cv_upload',
          question_type: QuestionTypeEnum.FILE_UPLOAD,
          heading: 'Upload your CV/Resume.',
          sub_text: 'PDF, DOCX, JPG formats. Max 10MB.',
          is_mandatory: true,
          display_order: 5,
          allowed_file_types: 'pdf,docx,jpg,jpeg,png',
          max_file_size_mb: 10,
          conditions: [
            {
              trigger_answer_value: '*',
              next_question_client_ref: 'temp_q_static_submitted',
            },
          ],
        },
        {
          client_ref: 'temp_q_static_submitted',
          question_type: QuestionTypeEnum.STATIC_MESSAGE,
          heading: 'Application Submitted Successfully!',
          sub_text: 'Thank you for completing the application.',
          message_style: 'SUCCESS',
          is_mandatory: true,
          display_order: 6,
          conditions: [
            {
              trigger_answer_value: '*',
              next_outcome_client_ref: 'temp_o_thank_you',
            },
          ],
        },
      ];

      outcomes = [
        {
          client_ref: 'temp_o_reject_exp',
          outcome_type: OutcomeTypeEnum.REJECT,
          message_text:
            'Thank you for your interest. We require candidates with relevant experience for this position. We encourage you to apply for other suitable positions.',
          is_default_reject: false,
        },
        {
          client_ref: 'temp_o_thank_you',
          outcome_type: OutcomeTypeEnum.THANK_YOU_COMPLETE,
          message_text:
            "We've received your application. Our team will review it and contact you if your profile matches our requirements. We appreciate your time!",
          is_default_success: false,
        },
      ];
    }

    // Update the DTO with generated data
    updateData.questions = questions;
    updateData.outcomes = outcomes;
    updateData.start_question_client_ref = questions[0].client_ref;

    console.log(
      `AI-generated flow: setting start_question_client_ref to ${updateData.start_question_client_ref}`,
    );

    return savedFlow;
  }
}
