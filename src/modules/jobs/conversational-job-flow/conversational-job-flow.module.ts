import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConversationalJobFlowController } from './conversational-job-flow.controller';
import { ConversationalJobFlowService } from './conversational-job-flow.service';
import { ConversationalFlow } from '../../../models/jobs/entities/conversational-flow.entity';
import { FlowQuestion } from '../../../models/jobs/entities/flow-question.entity';
import { FlowOutcome } from '../../../models/jobs/entities/flow-outcome.entity';
import { FlowCondition } from '../../../models/jobs/entities/flow-condition.entity';
import { RolesModule } from '../../roles-permissions/roles.module';
import { DeepseekModule } from '../../../common/providers/deepseek/deepseek.module';
import { OpenAIModule } from '../../../common/providers/openai/openai.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      ConversationalFlow,
      FlowQuestion,
      FlowOutcome,
      FlowCondition,
    ]),
    RolesModule,
    DeepseekModule,
    OpenAIModule,
  ],
  controllers: [ConversationalJobFlowController],
  providers: [ConversationalJobFlowService],
})
export class ConversationalJobFlowModule {}
