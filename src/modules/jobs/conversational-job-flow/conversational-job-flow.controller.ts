import {
  <PERSON>,
  Get,
  Patch,
  Param,
  Body,
  UseGuards,
  Query,
} from '@nestjs/common';
import { ApiT<PERSON>s, Api<PERSON>earerAuth, ApiQuery } from '@nestjs/swagger';
import { ConversationalJobFlowService } from './conversational-job-flow.service';
import { UpdateFlowDto } from './dto/update-flow.dto';
import { JwtAuthGuard } from '../../../authentication/jwt-auth.guard';
import { PermissionsGuard } from '../../../common/guards/permissions.guard';
import { RequirePermissions } from '../../../common/decorators/metadata/permissions.decorator';
import {
  PermissionModules,
  PermissionActions,
} from '../../../models/roles/constants/role.constants';
import { Public } from '../../../common/decorators/public.decorator';

@ApiTags('Conversational Job Flow')
@Controller('jobs/conversational-flow')
@UseGuards(JwtAuthGuard, PermissionsGuard)
@ApiBearerAuth()
export class ConversationalJobFlowController {
  constructor(
    private readonly conversationalJobFlowService: ConversationalJobFlowService,
  ) {}

  @Get('job/:jobPostId')
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.VIEW_JOBS,
  })
  getFlowByJobPostId(@Param('jobPostId') jobPostId: string) {
    return this.conversationalJobFlowService.getFlowByJobPostId(jobPostId);
  }

  @Public()
  @Get('public/job/:jobPostId')
  @ApiQuery({
    name: 'secretKey',
    required: true,
    description: 'Secret key for API access',
  })
  getPublicFlowByJobPostId(
    @Param('jobPostId') jobPostId: string,
    @Query('secretKey') secretKey: string,
  ) {
    return this.conversationalJobFlowService.getFlowByJobPostId(
      jobPostId,
      secretKey,
    );
  }

  @Patch(':id')
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.EDIT_JOBS,
  })
  updateFlow(@Param('id') id: string, @Body() updateData: UpdateFlowDto) {
    return this.conversationalJobFlowService.updateFlow(id, updateData);
  }
}
