import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { QuestionTypeEnum } from '../../../../models/jobs/constants/job.constants';

export class FlowGenesisDto {
  @ApiProperty({ description: 'Initial message text to display' })
  @IsString()
  message_text: string;
}

export class FlowConditionDto {
  @ApiProperty({ description: 'Answer value that triggers this condition' })
  @IsString()
  trigger_answer_value: string;

  @ApiPropertyOptional({
    description: 'Client reference for the next question',
  })
  @IsOptional()
  @IsString()
  next_question_client_ref?: string;

  @ApiPropertyOptional({ description: 'Client reference for the next outcome' })
  @IsOptional()
  @IsString()
  next_outcome_client_ref?: string;
}

export class FlowQuestionOptionDto {
  @ApiProperty({ description: 'Option value' })
  @IsString()
  value: string;
}

export class FlowQuestionDto {
  @ApiProperty({ description: 'Client reference ID for this question' })
  @IsString()
  client_ref: string;

  @ApiProperty({ description: 'Type of question', enum: QuestionTypeEnum })
  @IsEnum(QuestionTypeEnum)
  question_type: QuestionTypeEnum;

  @ApiProperty({ description: 'Question heading/title' })
  @IsString()
  heading: string;

  @ApiPropertyOptional({ description: 'Additional explanatory text' })
  @IsOptional()
  @IsString()
  sub_text?: string;

  @ApiPropertyOptional({ description: 'Placeholder text for input fields' })
  @IsOptional()
  @IsString()
  placeholder_text?: string;

  @ApiProperty({ description: 'Whether the question is mandatory' })
  @IsBoolean()
  is_mandatory: boolean;

  @ApiPropertyOptional({ description: 'Options for select-type questions' })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FlowQuestionOptionDto)
  options?: FlowQuestionOptionDto[];

  @ApiPropertyOptional({ description: 'Display order of the question' })
  @IsOptional()
  @IsNumber()
  display_order?: number;

  @ApiPropertyOptional({ description: 'Allowed file types for uploads' })
  @IsOptional()
  @IsString()
  allowed_file_types?: string;

  @ApiPropertyOptional({ description: 'Maximum file size in MB' })
  @IsOptional()
  @IsNumber()
  max_file_size_mb?: number;

  @ApiPropertyOptional({ description: 'Message style for static messages' })
  @IsOptional()
  @IsString()
  message_style?: string;

  @ApiProperty({ description: 'Conditions for this question' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FlowConditionDto)
  conditions: FlowConditionDto[];
}

export class FlowOutcomeDto {
  @ApiProperty({ description: 'Client reference ID for this outcome' })
  @IsString()
  client_ref: string;

  @ApiProperty({ description: 'Type of outcome' })
  @IsString()
  outcome_type: string;

  @ApiProperty({ description: 'Message text for this outcome' })
  @IsString()
  message_text: string;

  @ApiPropertyOptional({
    description: 'Whether this is the default reject outcome',
  })
  @IsOptional()
  @IsBoolean()
  is_default_reject?: boolean;

  @ApiPropertyOptional({
    description: 'Whether this is the default success outcome',
  })
  @IsOptional()
  @IsBoolean()
  is_default_success?: boolean;
}

export class UpdateFlowDto {
  @ApiPropertyOptional({ description: 'Whether the flow is a draft' })
  @IsOptional()
  @IsBoolean()
  isDraft?: boolean;

  @ApiPropertyOptional({ description: 'UUID of the job post' })
  @IsOptional()
  @IsUUID()
  job_post_id?: string;

  @ApiPropertyOptional({ description: 'Whether to generate with AI' })
  @IsOptional()
  @IsBoolean()
  generate_with_ai?: boolean;

  @ApiProperty({ description: 'Client reference for the starting question' })
  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) => !o.isDraft)
  start_question_client_ref: string;

  @ApiPropertyOptional({ description: 'Initial message configuration' })
  @IsOptional()
  @ValidateNested()
  @Type(() => FlowGenesisDto)
  genesis?: FlowGenesisDto;

  @ApiPropertyOptional({
    description: 'Array of questions in the flow',
    type: [FlowQuestionDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FlowQuestionDto)
  questions?: FlowQuestionDto[];

  @ApiPropertyOptional({
    description: 'Array of outcomes in the flow',
    type: [FlowOutcomeDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FlowOutcomeDto)
  outcomes?: FlowOutcomeDto[];
}
