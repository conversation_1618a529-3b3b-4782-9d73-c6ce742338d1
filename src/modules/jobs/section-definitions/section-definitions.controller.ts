import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../authentication/jwt-auth.guard';
import { SectionDefinitionsService } from './section-definitions.service';
import { CreateSectionDefinitionDto } from './dto/create-section-definition.dto';
import { UpdateSectionDefinitionDto } from './dto/update-section-definition.dto';
import { SectionDefinition } from '../../../models/jobs/entities/section-definition.entity';
import { API_RESPONSES } from '../../../common/constants/swagger.constants';
import { PermissionsGuard } from '../../../common/guards/permissions.guard';
import { PermissionActions } from '../../../models/roles/constants/role.constants';
import { PermissionModules } from '../../../models/roles/constants/role.constants';
import { RequirePermissions } from '../../../common/decorators/metadata/permissions.decorator';

@ApiTags('job-section-definitions')
@Controller('job-section-definitions')
@UseGuards(JwtAuthGuard, PermissionsGuard)
@ApiBearerAuth()
export class SectionDefinitionsController {
  constructor(
    private readonly sectionDefinitionsService: SectionDefinitionsService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new section definition' })
  @ApiResponse({
    status: 201,
    description: API_RESPONSES.SECTION_DEFINITION_CREATED_SUCCESSFULLY,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @RequirePermissions({
    action: PermissionActions.CREATE_JOBS,
    module: PermissionModules.JOBS,
  })
  async create(
    @Body() createSectionDefinitionDto: CreateSectionDefinitionDto,
  ): Promise<SectionDefinition> {
    return this.sectionDefinitionsService.create(createSectionDefinitionDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all section definitions' })
  @ApiResponse({
    status: 200,
    description: API_RESPONSES.LIST_OF_SECTION_DEFINITIONS,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async findAll(): Promise<SectionDefinition[]> {
    return this.sectionDefinitionsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a section definition by ID' })
  @ApiResponse({
    status: 200,
    description: API_RESPONSES.SECTION_DEFINITION_DETAILS,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Not Found.' })
  async findOne(@Param('id') id: string): Promise<SectionDefinition> {
    return this.sectionDefinitionsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a section definition' })
  @ApiResponse({
    status: 200,
    description: API_RESPONSES.SECTION_DEFINITION_UPDATED_SUCCESSFULLY,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Not Found.' })
  async update(
    @Param('id') id: string,
    @Body() updateSectionDefinitionDto: UpdateSectionDefinitionDto,
  ): Promise<SectionDefinition> {
    return this.sectionDefinitionsService.update(
      id,
      updateSectionDefinitionDto,
    );
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete a section definition if not system-defined',
  })
  @ApiResponse({
    status: 204,
    description: API_RESPONSES.SECTION_DEFINITION_DELETED_SUCCESSFULLY,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Not Found.' })
  async remove(@Param('id') id: string): Promise<void> {
    return this.sectionDefinitionsService.remove(id);
  }
}
