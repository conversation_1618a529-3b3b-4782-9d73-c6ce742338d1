import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SectionDefinitionRepository } from '../../../models/jobs/repositories/section-definition.repository';
import { CreateSectionDefinitionDto } from './dto/create-section-definition.dto';
import { UpdateSectionDefinitionDto } from './dto/update-section-definition.dto';
import { SectionDefinition } from '../../../models/jobs/entities/section-definition.entity';
import { SECTION_DEFINITION_MESSAGES } from '../../../common/constants/message.constants';

@Injectable()
export class SectionDefinitionsService {
  constructor(
    private readonly sectionDefinitionRepository: SectionDefinitionRepository,
  ) {}

  async create(
    createSectionDefinitionDto: CreateSectionDefinitionDto,
  ): Promise<SectionDefinition> {
    // The tenant context is automatically applied by the TenantAwareRepository
    return this.sectionDefinitionRepository.create(createSectionDefinitionDto);
  }

  async findAll(): Promise<SectionDefinition[]> {
    return this.sectionDefinitionRepository.findAll();
  }

  async findOne(id: string): Promise<SectionDefinition> {
    const sectionDefinition =
      await this.sectionDefinitionRepository.findById(id);
    if (!sectionDefinition) {
      throw new NotFoundException(
        SECTION_DEFINITION_MESSAGES.SECTION_DEFINITION_NOT_FOUND,
      );
    }
    return sectionDefinition;
  }

  async update(
    id: string,
    updateSectionDefinitionDto: UpdateSectionDefinitionDto,
  ): Promise<SectionDefinition> {
    const sectionDefinition = await this.findOne(id);

    if (sectionDefinition.isSystemDefined) {
      throw new BadRequestException(
        SECTION_DEFINITION_MESSAGES.SYSTEM_DEFINED_SECTION_MODIFICATION_NOT_ALLOWED,
      );
    }

    return this.sectionDefinitionRepository.update(
      id,
      updateSectionDefinitionDto,
    );
  }

  async remove(id: string): Promise<void> {
    const sectionDefinition = await this.findOne(id);

    if (sectionDefinition.isSystemDefined) {
      throw new BadRequestException(
        SECTION_DEFINITION_MESSAGES.SYSTEM_DEFINED_SECTION_DELETION_NOT_ALLOWED,
      );
    }

    await this.sectionDefinitionRepository.delete(id);
  }
}
