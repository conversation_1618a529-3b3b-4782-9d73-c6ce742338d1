import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SectionDefinitionsController } from './section-definitions.controller';
import { SectionDefinitionsService } from './section-definitions.service';
import { SectionDefinition } from '../../../models/jobs/entities/section-definition.entity';
import { SectionDefinitionRepository } from '../../../models/jobs/repositories/section-definition.repository';
import { RolesModule } from '../../roles-permissions/roles.module';

@Module({
  imports: [TypeOrmModule.forFeature([SectionDefinition]), RolesModule],
  controllers: [SectionDefinitionsController],
  providers: [SectionDefinitionsService, SectionDefinitionRepository],
  exports: [SectionDefinitionsService],
})
export class SectionDefinitionsModule {}
