import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString } from 'class-validator';

export class UpdateSectionDefinitionDto {
  @ApiPropertyOptional({
    description: 'The default name of the section',
    example: 'Job Requirements',
  })
  @IsOptional()
  @IsString()
  defaultName?: string;

  @ApiPropertyOptional({
    description: 'Default values for the section (if applicable)',
    example: ['Option 1', 'Option 2'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  defaultValues?: string[];
}
