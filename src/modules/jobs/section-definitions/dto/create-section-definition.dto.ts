import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateSectionDefinitionDto {
  @ApiProperty({
    description: 'The default name of the section',
    example: 'Job Requirements',
  })
  @IsString()
  @IsNotEmpty()
  defaultName: string;

  @ApiPropertyOptional({
    description: 'Default values for the section (if applicable)',
    example: ['Option 1', 'Option 2'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  defaultValues?: string[];

  @ApiPropertyOptional({
    description: 'Whether this is a system-defined section (default: false)',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isSystemDefined?: boolean = false;
}
