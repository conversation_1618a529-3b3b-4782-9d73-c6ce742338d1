import { ApiProperty } from '@nestjs/swagger';
import { JobTemplateSectionType } from 'src/models/jobs/entities/job-template-section.entity';
export class JobDetailsContentDto {
  @ApiProperty({ example: 'Software Engineer' })
  jobTitle: string;

  @ApiProperty({ example: 'San Francisco, CA' })
  location: string;

  @ApiProperty({ example: ['FULL_TIME', 'PART_TIME'] })
  jobType: string[];

  @ApiProperty({ example: { currency: 'USD', min: 120000, max: 180000 } })
  compensationRange: {
    currency: string;
    min: number;
    max: number;
  };
}

export class JobHighlightsContentDto {
  @ApiProperty({
    example: ['Work on cutting-edge technology', 'Flexible work arrangements'],
  })
  highlights: string[];
}

export class QualificationsContentDto {
  @ApiProperty({
    example: ['3+ years of experience', 'Strong JavaScript skills'],
  })
  qualifications: string[];
}

export class BenefitsContentDto {
  @ApiProperty({ example: ['Health insurance', '401(k) matching'] })
  benefits: string[];
}

export class ResponsibilitiesContentDto {
  @ApiProperty({ example: ['Design and implement solutions', 'Code reviews'] })
  responsibilities: string[];
}

export class JobDescriptionContentDto {
  @ApiProperty({ example: 'Detailed job description text' })
  content: string;
}

export class JobTemplateSectionDto {
  @ApiProperty()
  jobTemplateSectionId: string;

  @ApiProperty()
  title: string;

  @ApiProperty({
    oneOf: [
      { $ref: '#/components/schemas/JobDetailsContentDto' },
      { $ref: '#/components/schemas/JobHighlightsContentDto' },
      { $ref: '#/components/schemas/QualificationsContentDto' },
      { $ref: '#/components/schemas/BenefitsContentDto' },
      { $ref: '#/components/schemas/ResponsibilitiesContentDto' },
      { $ref: '#/components/schemas/JobDescriptionContentDto' },
    ],
  })
  content:
    | JobDetailsContentDto
    | JobHighlightsContentDto
    | QualificationsContentDto
    | BenefitsContentDto
    | ResponsibilitiesContentDto
    | JobDescriptionContentDto;

  @ApiProperty()
  displayOrder: number;

  @ApiProperty()
  isVisibleDefault: boolean;

  @ApiProperty()
  isEditable: boolean;

  @ApiProperty()
  isDeletable: boolean;

  @ApiProperty()
  sectionDefinitionId: string;

  @ApiProperty()
  type: JobTemplateSectionType;
}

export class JobTemplateResponseDto {
  @ApiProperty()
  jobTemplateId: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  location: {
    id: string;
    name: string;
    city: string;
    address: string;
    country: string;
  };

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ type: [JobTemplateSectionDto] })
  sections: JobTemplateSectionDto[];
}
