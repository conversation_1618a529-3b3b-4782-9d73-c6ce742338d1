import { ApiProperty } from '@nestjs/swagger';
import { JobTemplateResponseDto } from './job-template-response.dto';

export class PaginatedJobTemplatesDto {
  @ApiProperty({ type: [JobTemplateResponseDto] })
  data: JobTemplateResponseDto[];

  @ApiProperty({ example: 1 })
  currentPage: number;

  @ApiProperty({ example: 10 })
  itemsPerPage: number;

  @ApiProperty({ example: 100 })
  totalItems: number;

  @ApiProperty({ example: 10 })
  totalPages: number;
}
