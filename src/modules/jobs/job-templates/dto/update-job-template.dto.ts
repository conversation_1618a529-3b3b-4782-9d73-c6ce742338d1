import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateJobTemplateSectionDto {
  @ApiPropertyOptional({
    description: 'The section definition ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  @IsString()
  sectionDefinitionId?: string;

  @ApiPropertyOptional({
    description: 'The title of the section',
    example: 'Job Requirements',
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'The content of the section',
    example: {
      qualifications: ['3+ years of experience', 'Strong JavaScript skills'],
    },
  })
  @IsOptional()
  content?: any;

  @ApiPropertyOptional({
    description: 'The display order of the section',
    example: 1,
  })
  @IsOptional()
  displayOrder?: number;

  @ApiPropertyOptional({
    description: 'Whether the section is visible by default',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isVisibleDefault?: boolean;

  @ApiPropertyOptional({
    description: 'Whether the section is editable',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isEditable?: boolean;

  @ApiPropertyOptional({
    description: 'Whether the section is deletable',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isDeletable?: boolean;

  @ApiPropertyOptional({
    description: 'The parent section ID (if this is a subsection)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  @IsString()
  parentSectionId?: string;
}

export class UpdateJobTemplateDto {
  @ApiPropertyOptional({
    description: 'The name of the job template',
    example: 'Software Engineer Template',
  })
  @IsOptional()
  @IsString()
  templateName?: string;

  @ApiPropertyOptional({
    description: 'The description of the job template',
    example: 'Template for software engineering positions',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'The sections of the job template',
    type: [UpdateJobTemplateSectionDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateJobTemplateSectionDto)
  sections?: UpdateJobTemplateSectionDto[];
}
