import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { JobTemplateSectionType } from 'src/models/jobs/entities/job-template-section.entity';

export class CreateJobTemplateSectionDto {
  @ApiProperty({
    description: 'The section definition ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  sectionDefinitionId: string;

  @ApiProperty({
    description: 'The title of the section',
    example: 'Job Requirements',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiPropertyOptional({
    description: 'The content of the section',
    example: {
      qualifications: ['3+ years of experience', 'Strong JavaScript skills'],
    },
  })
  @IsOptional()
  content?: any;

  @ApiProperty({
    description: 'The display order of the section',
    example: 1,
  })
  displayOrder: number;

  @ApiPropertyOptional({
    description: 'Whether the section is visible by default',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isVisibleDefault?: boolean;

  @ApiPropertyOptional({
    description: 'Whether the section is editable',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isEditable?: boolean;

  @ApiPropertyOptional({
    description: 'Whether the section is deletable',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isDeletable?: boolean;

  @ApiPropertyOptional({
    description: 'The parent section ID (if this is a subsection)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  @IsString()
  parentSectionId?: string;

  @ApiProperty({
    description: 'The type of the section',
    enum: JobTemplateSectionType,
    example: JobTemplateSectionType.JOB_DETAILS,
    default: JobTemplateSectionType.DEFAULT,
  })
  @IsEnum(JobTemplateSectionType)
  @IsOptional()
  type?: JobTemplateSectionType;
}

export class CreateJobTemplateDto {
  @ApiProperty({
    description: 'The name of the job template',
    example: 'Software Engineer Template',
  })
  @IsString()
  @IsNotEmpty()
  templateName: string;

  @ApiPropertyOptional({
    description: 'The description of the job template',
    example: 'Template for software engineering positions',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'The sections of the job template',
    type: [CreateJobTemplateSectionDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateJobTemplateSectionDto)
  sections: CreateJobTemplateSectionDto[];
}
