import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobTemplateResponseDto } from './dto/job-template-response.dto';
import { JobTemplateSerializer } from './serializers/job-template.serializer';
import {
  CreateJobTemplateDto,
  CreateJobTemplateSectionDto,
} from './dto/create-job-template.dto';
import { UpdateJobTemplateDto } from './dto/update-job-template.dto';
import { JobTemplateRepository } from '../../../models/jobs/repositories/job-template.repository';
import { JobTemplateSectionRepository } from '../../../models/jobs/repositories/job-template-section.repository';
import { JobTemplateQueryDto } from './dto/job-template-query.dto';
import { PaginatedJobTemplatesDto } from './dto/paginated-job-templates.dto';
import { TextractService } from '@hirenetix/common/providers/aws-textract/textract.service';
import { DeepseekService } from '@hirenetix/common/providers/deepseek/deepseek.service';
import { unlink, readFile } from 'fs/promises';
import { JobTemplateSectionType } from '../../../models/jobs/entities/job-template-section.entity';
import { SectionDefinition } from '../../../models/jobs/entities/section-definition.entity';
import * as fs from 'fs';
import * as mammoth from 'mammoth';
import { OpenAIService } from '../../../common/providers/openai/openai.service';
@Injectable()
export class JobTemplatesService {
  private readonly logger = new Logger(JobTemplatesService.name);

  constructor(
    private readonly jobTemplateRepository: JobTemplateRepository,
    private readonly jobTemplateSectionRepository: JobTemplateSectionRepository,
    private readonly textractService: TextractService,
    private readonly deepseekService: DeepseekService,
    private readonly openaiService: OpenAIService,
    @InjectRepository(SectionDefinition)
    private readonly sectionDefinitionRepository: Repository<SectionDefinition>,
  ) {}

  async getAllJobTemplates(
    query: JobTemplateQueryDto,
  ): Promise<PaginatedJobTemplatesDto> {
    const {
      page = 1,
      limit = 100,

      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = query;

    const qb = this.jobTemplateRepository.createQueryBuilderWithSections();

    // Apply sorting
    qb.orderBy(`template.${sortBy}`, sortOrder);

    if (sortBy === 'templateName') {
      qb.addOrderBy('template.createdAt', 'DESC'); // Secondary sort
    }

    // Add display order sorting for sections
    qb.addOrderBy('sections.displayOrder', 'ASC');

    // Get total count
    const totalItems = await qb.getCount();

    // Apply pagination
    const skip = (page - 1) * limit;
    qb.skip(skip).take(limit);

    // Get paginated results
    const jobTemplates = await qb.getMany();

    // Transform to response DTOs
    const data = jobTemplates.map((template) =>
      JobTemplateSerializer.serialize(template),
    );

    return {
      data,
      currentPage: page,
      itemsPerPage: limit,
      totalItems,
      totalPages: Math.ceil(totalItems / limit),
    };
  }

  async getJobTemplateDetails(id: string): Promise<JobTemplateResponseDto> {
    const jobTemplate =
      await this.jobTemplateRepository.findByIdWithSections(id);

    if (!jobTemplate) {
      throw new NotFoundException(`Job template with ID "${id}" not found`);
    }

    return JobTemplateSerializer.serialize(jobTemplate);
  }

  async createJobTemplate(
    createJobTemplateDto: CreateJobTemplateDto,
  ): Promise<JobTemplateResponseDto> {
    const { sections, ...templateData } = createJobTemplateDto;

    // Create the job template
    const jobTemplate = await this.jobTemplateRepository.create(templateData);

    // Create sections if provided
    if (sections && sections.length > 0) {
      const templateSections = sections.map((section) => ({
        ...section,
        templateId: jobTemplate.id,
      }));

      const savedSections =
        await this.jobTemplateSectionRepository.createMany(templateSections);
      jobTemplate.sections = savedSections;
    }

    return JobTemplateSerializer.serialize(jobTemplate);
  }

  async updateJobTemplate(
    id: string,
    updateJobTemplateDto: UpdateJobTemplateDto,
  ): Promise<JobTemplateResponseDto> {
    const existingTemplate =
      await this.jobTemplateRepository.findByIdWithSections(id);
    if (!existingTemplate) {
      throw new NotFoundException(`Job template with ID "${id}" not found`);
    }

    const { sections, ...templateData } = updateJobTemplateDto;

    // Update template data
    if (Object.keys(templateData).length > 0) {
      await this.jobTemplateRepository.update(id, templateData);
    }

    // Update sections if provided
    if (sections && sections.length > 0) {
      // Delete existing sections
      await this.jobTemplateSectionRepository.deleteByTemplateId(id);

      // Create new sections
      const templateSections = sections.map((section) => ({
        ...section,
        templateId: id,
      }));

      const savedSections =
        await this.jobTemplateSectionRepository.createMany(templateSections);
      existingTemplate.sections = savedSections;
    }

    return this.getJobTemplateDetails(id);
  }

  async deleteJobTemplate(id: string): Promise<void> {
    const template = await this.jobTemplateRepository.findByIdWithSections(id);
    if (!template) {
      throw new NotFoundException(`Job template with ID "${id}" not found`);
    }

    await this.jobTemplateRepository.delete(id);
  }

  private async generateJobPostContent(extractedText: string): Promise<any> {
    // First, try to extract sections from the document
    const sections = {
      'Job Highlights': '',
      'Job Description': '',
      Qualifications: '',
      Responsibilities: '',
      Benefits: '',
    };

    // Simple pattern matching to find sections in the text
    const sectionMatches = extractedText.match(
      /(?:Job Description|Qualifications|Responsibilities|Benefits)[\s\n]*:?[\s\n]*((?:(?!Job Description|Qualifications|Responsibilities|Benefits)[\s\S])*)/gi,
    );

    if (sectionMatches) {
      sectionMatches.forEach((match) => {
        const [section, content] = match.split(/[\s\n]*:?[\s\n]*/);
        if (content) {
          sections[section] = content.trim();
        }
      });
    }

    // Prepare the prompt for Deepseek
    const prompt = `Given this job posting content:
${extractedText}

Please generate a complete job template with the following sections. If any section is empty or missing in the original text, generate appropriate content based on the context. Format the response exactly as shown in the example below, maintaining the exact structure and field names.

Required format:
{
  "title": "Software Engineer", // Generate an appropriate title based on the content
  "description": "Brief overview of the position",
  "sections": [
    {
      "title": "Job Highlights",
      "type": "job_highlights",
      "content": [
        {
          "key": "job_type",
          "type": "select",
          "label": "Job type",
          "value": "Full time",
          "options": ["Full time", "Part time"]
        },
        {
          "key": "compensation",
          "type": "range",
          "label": "Competitive Pay",
          "min": 0,
          "max": 0,
          "currency": "USD($)"
        },
        {
          "key": "age_limit",
          "type": "singleInput",
          "label": "Age Limit",
          "value": "16"
        },
        {
          "key": "shift",
          "type": "select",
          "label": "Shift",
          "value": "Day",
          "options": ["Day", "Night"]
        }
      ]
    },
    {
      "title": "Qualifications",
      "type": "qualifications",
      "content": [
        {
          "type": "text",
          "value": "Detailed qualifications text here"
        }
      ]
    },
    {
      "title": "Benefits",
      "type": "benefits",
      "content": [
        {
          "type": "text",
          "value": "Detailed benefits text here"
        }
      ]
    },
    {
      "title": "Responsibilities",
      "type": "responsibilities",
      "content": [
        {
          "type": "text",
          "value": "Detailed responsibilities text here"
        }
      ]
    },
    {
      "title": "Job Description",
      "type": "job_description",
      "content": [
        {
          "type": "text",
          "value": "Detailed job description text here"
        }
      ]
    }
  ]
}

IMPORTANT GUIDELINES:
1. Maintain the exact structure and field names as shown
2. Generate appropriate content for any missing sections
3. Ensure all text content is professional and relevant to the position
4. Format the response as valid JSON
5. Include all sections shown in the example
6. Use realistic salary ranges based on the position and requirements
7. Keep the job_highlights section exactly as structured in the example

Response must be valid JSON without any additional text or explanations.`;

    console.log('Sending prompt to Deepseek:', prompt);

    try {
      // Call Deepseek API with the prepared prompt
      const aiResponse = await this.deepseekService.generateConversationalFlow({
        sections: { prompt },
      });

      console.log('Received AI response:', JSON.stringify(aiResponse, null, 2));

      // The response might be in different places depending on the API response structure
      let responseContent;

      if (aiResponse.questions?.[0]?.sub_text) {
        responseContent = aiResponse.questions[0].sub_text;
      } else if (aiResponse.questions?.[0]?.heading) {
        responseContent = aiResponse.questions[0].heading;
      } else if (typeof aiResponse === 'string') {
        responseContent = aiResponse;
      } else {
        console.error('Unexpected AI response structure:', aiResponse);
        throw new Error('Unexpected AI response structure');
      }

      // Try to extract JSON from the response (it might be wrapped in markdown code blocks)
      const jsonMatch = responseContent.match(/```(?:json)?([\s\S]*?)```/) || [
        null,
        responseContent,
      ];
      const jsonContent = jsonMatch[1].trim();

      console.log('Extracted JSON content:', jsonContent);

      try {
        // Parse the JSON response
        const parsedResponse = JSON.parse(jsonContent);

        // Get all section definition IDs first
        const sectionDefinitionIds = await Promise.all(
          parsedResponse.sections.map((section) =>
            this.getSectionDefinitionIdByName(section.title),
          ),
        );

        // Transform the response into the required format
        return {
          title: parsedResponse.title,
          description: parsedResponse.description,
          sections: parsedResponse.sections.map((section, index) => ({
            title: section.title,
            type: section.type,
            content: section.content,
            displayOrder: index + 1,
            isVisibleDefault: true,
            isEditable: true,
            isDeletable: section.title !== 'Job Highlights',
            sectionDefinitionId: sectionDefinitionIds[index],
          })),
        };
      } catch (error) {
        console.error('Error parsing JSON:', error);
        console.error('JSON content that failed to parse:', jsonContent);
        throw new Error(
          `Failed to parse AI response as JSON: ${error.message}`,
        );
      }
    } catch (error) {
      console.error('Error in generateJobPostContent:', error);
      throw new Error(
        `Failed to generate job template content: ${error.message}`,
      );
    }
  }

  private parseAiResponseToTemplate(response: any): CreateJobTemplateDto {
    const template: CreateJobTemplateDto = {
      templateName: response.title || 'Untitled Job',
      description: response.description || '',
      sections: [],
    };

    // Convert the questions array into template sections
    if (Array.isArray(response.questions)) {
      template.sections = this.convertQuestionsToSections(response.questions);
    }

    return template;
  }

  private convertQuestionsToSections(
    questions: any[],
  ): CreateJobTemplateSectionDto[] {
    const sections: CreateJobTemplateSectionDto[] = [];
    let currentSection = null;

    // Add Job Details section first
    sections.push({
      title: 'Job Details',
      sectionDefinitionId: '163cf2dd-e2a9-4feb-b11d-0bf09219a75d',
      type: JobTemplateSectionType.JOB_DETAILS,
      content: [
        {
          key: 'job_type',
          type: 'select',
          label: 'Job type',
          value: 'Full time',
          options: ['Full time', 'Part time'],
        },
        {
          key: 'compensation',
          type: 'range',
          label: 'Competitive Pay',
          min: 10000,
          max: 150000,
          currency: 'USD($)',
        },
        {
          key: 'age_limit',
          type: 'singleInput',
          label: 'Age Limit',
          value: '',
        },
        {
          key: 'shift',
          type: 'select',
          label: 'Shift',
          value: 'Day',
          options: ['Day', 'Night'],
        },
      ],
      displayOrder: 1,
      isVisibleDefault: true,
      isEditable: true,
      isDeletable: false,
    });

    // Group questions by type into sections
    questions.forEach((question) => {
      let sectionTitle = '';
      let sectionDefinitionId = '';
      let sectionType = JobTemplateSectionType.DEFAULT;

      switch (question.question_type) {
        case 'YES_NO':
        case 'MULTI_SELECT':
          sectionTitle = 'Qualifications';
          sectionDefinitionId = 'f4d1b88d-6f4e-4b8c-935a-75f2d969f15c';
          sectionType = JobTemplateSectionType.QUALIFICATIONS;
          break;
        case 'TEXT_INPUT':
          sectionTitle = 'Responsibilities';
          sectionDefinitionId = '4804839d-1f64-4fc6-b9d8-0828f5db4cc6';
          sectionType = JobTemplateSectionType.RESPONSIBILITIES;
          break;
        case 'FILE_UPLOAD':
          return; // Skip file upload questions
        default:
          return; // Skip other question types
      }

      if (!currentSection || currentSection.title !== sectionTitle) {
        currentSection = {
          title: sectionTitle,
          sectionDefinitionId,
          type: sectionType,
          content: [{ type: 'text', value: '' }],
          displayOrder: sections.length + 1,
          isVisibleDefault: true,
          isEditable: true,
          isDeletable: true,
        };
        sections.push(currentSection);
      }

      // Add question content to section
      if (question.heading) {
        currentSection.content[0].value += question.heading + '\n';
      }
    });

    return sections;
  }

  async createFromDocument(file: {
    path: string;
  }): Promise<JobTemplateResponseDto> {
    try {
      let extractedText = '';
      const fileExtension = file.path.toLowerCase().split('.').pop();

      // Handle different file types
      if (fileExtension === 'txt') {
        // For txt files, read directly
        extractedText = await this.readTextFile(file.path);
      } else if (fileExtension === 'doc' || fileExtension === 'docx') {
        // For doc/docx files, use mammoth
        extractedText = await this.readDocxFile(file.path);
      } else {
        // For other files (pdf, images), use AWS Textract
        extractedText = await this.textractService.extractText(file);
      }
      let jobPost: any = {};
      if (process.env.AI_PROVIDER === 'deepseek') {
        jobPost = await this.deepseekService.generateJobPost(extractedText);
      } else {
        jobPost = await this.openaiService.generateJobPost(extractedText);
      }

      return jobPost;
    } catch (err) {
      console.error('Error in createFromDocument:', err);
      throw err;
    } finally {
      // Always clean up the uploaded file, regardless of success or failure
      if (file?.path) {
        try {
          await unlink(file.path);
          console.log('Successfully deleted uploaded file:', file.path);
        } catch (unlinkError) {
          // Only log if error is not ENOENT (file not found)
          if (unlinkError.code !== 'ENOENT') {
            console.error(
              'Failed to delete uploaded file:',
              file.path,
              unlinkError,
            );
          }
        }
      }
    }
  }

  private async readTextFile(filePath: string): Promise<string> {
    try {
      const content = await readFile(filePath, 'utf8');
      return content;
    } catch (error) {
      console.error('Error reading text file:', error);
      throw new Error('Failed to read text file');
    }
  }

  private async readDocxFile(filePath: string): Promise<string> {
    try {
      const buffer = fs.readFileSync(filePath);
      const result = await mammoth.extractRawText({ buffer });
      this.logger.error('result :', result);

      return result.value; // Plain text content
    } catch (error) {
      this.logger.error('Error reading docx file:', error);
      throw new Error('Failed to read docx file');
    }
  }

  private async getSectionDefinitionIdByName(name: string): Promise<string> {
    const sectionDefinition = await this.sectionDefinitionRepository.findOne({
      where: { defaultName: name },
    });

    if (!sectionDefinition) {
      throw new NotFoundException(
        `Section definition with name "${name}" not found`,
      );
    }

    return sectionDefinition.id;
  }
}
