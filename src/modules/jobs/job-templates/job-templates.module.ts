import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobTemplate } from '../../../models/jobs/entities/job-template.entity';
import { JobTemplateSection } from '../../../models/jobs/entities/job-template-section.entity';
import { JobTemplatesController } from './job-templates.controller';
import { JobTemplatesService } from './job-templates.service';
import { JobTemplateRepository } from '../../../models/jobs/repositories/job-template.repository';
import { JobTemplateSectionRepository } from '../../../models/jobs/repositories/job-template-section.repository';
import { TextractModule } from '../../../common/providers/aws-textract/textract.module';
import { DeepseekModule } from '../../../common/providers/deepseek/deepseek.module';
import { RolesModule } from '../../../modules/roles-permissions/roles.module';
import { SectionDefinition } from '../../../models/jobs/entities/section-definition.entity';
import { OpenAIModule } from '../../../common/providers/openai/openai.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      JobTemplate,
      JobTemplateSection,
      SectionDefinition,
    ]),
    TextractModule,
    DeepseekModule,
    OpenAIModule,
    RolesModule,
  ],
  controllers: [JobTemplatesController],
  providers: [
    JobTemplatesService,
    JobTemplateRepository,
    JobTemplateSectionRepository,
  ],
  exports: [
    JobTemplatesService,
    JobTemplateRepository,
    JobTemplateSectionRepository,
  ],
})
export class JobTemplatesModule {}
