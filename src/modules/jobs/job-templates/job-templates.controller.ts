import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiConsumes,
} from '@nestjs/swagger';
import { JobTemplatesService } from './job-templates.service';
import { JobTemplateResponseDto } from './dto/job-template-response.dto';
import { CreateJobTemplateDto } from './dto/create-job-template.dto';
import { UpdateJobTemplateDto } from './dto/update-job-template.dto';
import { JwtAuthGuard } from '../../../authentication/jwt-auth.guard';
import { PermissionsGuard } from '../../../common/guards/permissions.guard';
import { RequirePermissions } from '../../../common/decorators/metadata/permissions.decorator';
import {
  PermissionModules,
  PermissionActions,
} from '../../../models/roles/constants/role.constants';
import { JobTemplateQueryDto } from './dto/job-template-query.dto';
import { PaginatedJobTemplatesDto } from './dto/paginated-job-templates.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { unlink } from 'fs/promises';
import {
  MAX_FILE_SIZE,
  ALLOWED_FILE_TYPES,
  ALLOWED_FILE_EXTENSIONS,
} from '../../../common/constants/general.constants';

@ApiTags('Job Templates')
@Controller('job-templates')
@UseGuards(JwtAuthGuard, PermissionsGuard)
@ApiBearerAuth()
export class JobTemplatesController {
  constructor(private readonly jobTemplatesService: JobTemplatesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all job templates' })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated job templates with their sections',
    type: PaginatedJobTemplatesDto,
  })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.VIEW_JOBS,
  })
  async getAllJobTemplates(
    @Query() query: JobTemplateQueryDto,
  ): Promise<PaginatedJobTemplatesDto> {
    return this.jobTemplatesService.getAllJobTemplates(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get job template details by ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the job template details with sections',
    type: JobTemplateResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Job template not found' })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.VIEW_JOBS,
  })
  async getJobTemplateDetails(
    @Param('id') id: string,
  ): Promise<JobTemplateResponseDto> {
    return this.jobTemplatesService.getJobTemplateDetails(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new job template' })
  @ApiResponse({
    status: 201,
    description: 'The job template has been successfully created',
    type: JobTemplateResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.CREATE_JOBS,
  })
  async createJobTemplate(
    @Body() createJobTemplateDto: CreateJobTemplateDto,
  ): Promise<JobTemplateResponseDto> {
    return this.jobTemplatesService.createJobTemplate(createJobTemplateDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a job template' })
  @ApiResponse({
    status: 200,
    description: 'The job template has been successfully updated',
    type: JobTemplateResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Job template not found' })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.EDIT_JOBS,
  })
  async updateJobTemplate(
    @Param('id') id: string,
    @Body() updateJobTemplateDto: UpdateJobTemplateDto,
  ): Promise<JobTemplateResponseDto> {
    return this.jobTemplatesService.updateJobTemplate(id, updateJobTemplateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a job template' })
  @ApiResponse({
    status: 200,
    description: 'The job template has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Job template not found' })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.DELETE_JOBS,
  })
  async deleteJobTemplate(@Param('id') id: string): Promise<void> {
    return this.jobTemplatesService.deleteJobTemplate(id);
  }

  @Post('upload')
  @ApiOperation({ summary: 'Create a job template from document' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job template generated successfully',
    type: JobTemplateResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description:
      'Invalid file type or size. Supported formats: PDF, JPEG, PNG, TIFF. Maximum size: 20MB.',
  })
  @RequirePermissions({
    module: PermissionModules.JOBS,
    action: PermissionActions.CREATE_JOBS,
  })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          return cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      limits: {
        fileSize: MAX_FILE_SIZE,
      },
      fileFilter: (req, file, cb) => {
        const mimeType = file.mimetype.toLowerCase();
        const fileExtension = extname(file.originalname).toLowerCase();

        if (
          !ALLOWED_FILE_TYPES.includes(mimeType) ||
          !ALLOWED_FILE_EXTENSIONS.includes(fileExtension)
        ) {
          return cb(
            new BadRequestException(
              `Unsupported file format. Supported formats are: ${ALLOWED_FILE_EXTENSIONS.join(', ')}. File must be in a format supported by AWS Textract.`,
            ),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  async createFromDocument(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<JobTemplateResponseDto> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    try {
      console.log(file);
      return await this.jobTemplatesService.createFromDocument(file);
    } catch (error) {
      console.log('error :', error);
      // Clean up the uploaded file in case of error
      if (file.path) {
        try {
          await unlink(file.path);
        } catch (unlinkError) {
          // Only log if error is not ENOENT (file not found)
          if (unlinkError.code !== 'ENOENT') {
            console.error('Failed to delete uploaded file:', unlinkError);
          }
        }
      }

      if (error.name === 'UnsupportedDocumentException') {
        throw new BadRequestException(
          'The uploaded document format is not supported. Please ensure the file is a valid PDF, JPEG, PNG, or TIFF file.',
        );
      }

      throw error;
    }
  }
}
