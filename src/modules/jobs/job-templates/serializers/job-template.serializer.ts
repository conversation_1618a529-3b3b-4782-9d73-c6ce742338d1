import { JobTemplate } from '../../../../models/jobs/entities/job-template.entity';
import { JobTemplateResponseDto } from '../dto/job-template-response.dto';

export class JobTemplateSerializer {
  static serialize(jobTemplate: JobTemplate): JobTemplateResponseDto {
    return {
      jobTemplateId: jobTemplate.id || null,
      title: jobTemplate.templateName,
      description: jobTemplate.description,
      createdAt: jobTemplate.createdAt,
      updatedAt: jobTemplate.updatedAt,
      location: {
        id: null,
        name: null,
        city: null,
        address: null,
        country: null,
      },
      sections:
        jobTemplate.sections?.map((section) => ({
          jobTemplateSectionId: section.id,
          title: section.title,
          content: section.content,
          displayOrder: section.displayOrder,
          isVisibleDefault: section.isVisibleDefault,
          isEditable: section.isEditable,
          isDeletable: section.isDeletable,
          sectionDefinitionId: section.sectionDefinitionId,
          type: section.type,
        })) || [],
    };
  }
}
