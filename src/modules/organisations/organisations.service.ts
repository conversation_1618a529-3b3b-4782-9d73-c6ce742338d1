import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { OrganisationsRepository } from './organisations.repository';
import {
  IOrganisationCreate,
  IOrganisationUpdate,
  IOrganisationSearchQuery,
} from '@hirenetix/models/organisations/interfaces/organisation.interface';
import { OrganisationSerializer } from '@hirenetix/models/organisations/serializers/organisation.serializer';
import { OrganisationStatus } from '@hirenetix/models/organisations/constants';
import { ORGANISATION_MESSAGES } from '../../common/constants';

@Injectable()
export class OrganisationsService {
  constructor(
    private readonly organisationsRepository: OrganisationsRepository,
  ) {}

  async findById(id: string): Promise<OrganisationSerializer> {
    const organisation = await this.organisationsRepository.findById(id);

    if (!organisation) {
      throw new NotFoundException(`Organisation with ID ${id} not found`);
    }

    return OrganisationSerializer.serialize(organisation);
  }

  async find(
    query: IOrganisationSearchQuery,
  ): Promise<{ data: OrganisationSerializer[]; count: number }> {
    const [organisations, count] =
      await this.organisationsRepository.find(query);

    return {
      data: OrganisationSerializer.serializeMany(organisations),
      count,
    };
  }

  async create(data: IOrganisationCreate): Promise<OrganisationSerializer> {
    // Check if organisation with the same name already exists

    const existingOrganisation = await this.organisationsRepository.findByName(
      data.name,
    );
    if (existingOrganisation) {
      throw new ConflictException(
        ORGANISATION_MESSAGES.ORGANISATION_NAME_EXISTS(data.name),
      );
    }

    const organisation = await this.organisationsRepository.create(data);

    return OrganisationSerializer.serialize(organisation);
  }

  async update(
    id: string,
    data: IOrganisationUpdate,
  ): Promise<OrganisationSerializer> {
    // Check if organisation exists
    const existingOrganisation =
      await this.organisationsRepository.findById(id);
    if (!existingOrganisation) {
      throw new NotFoundException(`Organisation with ID ${id} not found`);
    }

    // Check if name is being updated and is not already in use
    if (data.name && data.name !== existingOrganisation.name) {
      const organisationWithSameName =
        await this.organisationsRepository.findByName(data.name);
      if (organisationWithSameName) {
        throw new ConflictException(
          `Organisation with name ${data.name} already exists`,
        );
      }
    }

    // If parent ID is being updated, check if it exists and is not the same as the organisation ID
    if (data.parentId && data.parentId !== existingOrganisation.parentId) {
      // Prevent circular reference: an organisation cannot be its own parent/ancestor
      if (data.parentId === id) {
        throw new BadRequestException(
          'An organisation cannot be its own parent',
        );
      }

      const parentOrganisation = await this.organisationsRepository.findById(
        data.parentId,
      );
      if (!parentOrganisation) {
        throw new BadRequestException(
          `Parent organisation with ID ${data.parentId} not found`,
        );
      }

      // TODO: Add more complex logic to prevent circular parent-child relationships
    }

    const updatedOrganisation = await this.organisationsRepository.update(
      id,
      data,
    );

    return OrganisationSerializer.serialize(updatedOrganisation);
  }

  async delete(id: string): Promise<void> {
    // Check if organisation exists
    const existingOrganisation =
      await this.organisationsRepository.findById(id);
    if (!existingOrganisation) {
      throw new NotFoundException(`Organisation with ID ${id} not found`);
    }

    // Check if organisation has children
    if (
      existingOrganisation.children &&
      existingOrganisation.children.length > 0
    ) {
      throw new BadRequestException(
        'Cannot delete an organisation that has child organisations',
      );
    }

    const deleted = await this.organisationsRepository.delete(id);

    if (!deleted) {
      throw new NotFoundException(`Organisation with ID ${id} not found`);
    }
  }

  async changeStatus(
    id: string,
    status: OrganisationStatus,
  ): Promise<OrganisationSerializer> {
    return this.update(id, { status });
  }
}
