import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';
import { OrganisationsRepository } from './organisations.repository';
import { OrganisationsService } from './organisations.service';
import { OrganisationsController } from './organisations.controller';
import { RolesModule } from '@hirenetix/models/roles/roles.module';
import { CommonModule } from '@hirenetix/common/common.module';
import { S3Module } from '../../providers/storage/s3/s3.module';
import { PublicOrganisationsController } from './public-organisations.controller';
import { ThrottlerModule } from '@nestjs/throttler';

@Module({
  imports: [
    TypeOrmModule.forFeature([Organisation]),
    RolesModule,
    CommonModule,
    S3Module,
    ThrottlerModule.forRoot({
      throttlers: [
        {
          ttl: 60000,
          limit: 100,
        },
      ],
    }),
  ],
  providers: [OrganisationsRepository, OrganisationsService],
  controllers: [OrganisationsController, PublicOrganisationsController],
  exports: [OrganisationsService, OrganisationsRepository],
})
export class OrganisationsModule {}
