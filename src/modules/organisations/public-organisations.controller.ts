import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { OrganisationsService } from './organisations.service';
import { PublicOrganisationSerializer } from '../../models/organisations/serializers/public-organisation.serializer';
import { ORGANISATION_MESSAGES } from '../../common/constants/message.constants';
import { ThrottlerGuard } from '@nestjs/throttler';

@ApiTags('Public Organizations')
@Controller('public/organisations')
@UseGuards(ThrottlerGuard)
export class PublicOrganisationsController {
  constructor(private readonly organisationsService: OrganisationsService) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get organization details by ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the organization details.',
    type: PublicOrganisationSerializer,
  })
  @ApiResponse({
    status: 404,
    description: ORGANISATION_MESSAGES.ORGANISATION_NOT_FOUND,
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests',
  })
  async getOrganisation(
    @Param('id') id: string,
  ): Promise<PublicOrganisationSerializer> {
    const organisation = await this.organisationsService.findById(id);
    return PublicOrganisationSerializer.serialize(organisation);
  }
}
