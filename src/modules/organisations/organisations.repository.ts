import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';
import {
  IOrganisationCreate,
  IOrganisationUpdate,
  IOrganisationSearchQuery,
} from '@hirenetix/models/organisations/interfaces/organisation.interface';
import { OrganisationStatus } from '@hirenetix/models/organisations/constants';

@Injectable()
export class OrganisationsRepository {
  constructor(
    @InjectRepository(Organisation)
    private organisationRepository: Repository<Organisation>,
  ) {}

  async findById(id: string): Promise<Organisation | null> {
    return this.organisationRepository.findOne({
      where: { id },
      relations: ['parent', 'children'],
    });
  }

  async findByName(name: string): Promise<Organisation | null> {
    return this.organisationRepository.findOne({
      where: { name },
    });
  }

  async find(
    query: IOrganisationSearchQuery,
  ): Promise<[Organisation[], number]> {
    const queryBuilder =
      this.organisationRepository.createQueryBuilder('organisation');
    queryBuilder.leftJoinAndSelect('organisation.parent', 'parent');
    queryBuilder.leftJoinAndSelect('organisation.children', 'children');

    if (query.id) {
      queryBuilder.andWhere('organisation.id = :id', { id: query.id });
    }

    if (query.name) {
      queryBuilder.andWhere('organisation.name ILIKE :name', {
        name: `%${query.name}%`,
      });
    }

    if (query.country) {
      queryBuilder.andWhere('organisation.country ILIKE :country', {
        country: `%${query.country}%`,
      });
    }

    if (query.parentId) {
      queryBuilder.andWhere('organisation.parentId = :parentId', {
        parentId: query.parentId,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('organisation.status = :status', {
        status: query.status,
      });
    }

    return queryBuilder.getManyAndCount();
  }

  async create(data: IOrganisationCreate): Promise<Organisation> {
    const organisation = new Organisation();

    organisation.name = data.name;
    organisation.description = data.description;
    organisation.website = data.website;
    organisation.address = data.address;
    organisation.status = OrganisationStatus.ACTIVE;

    return this.organisationRepository.save(organisation);
  }

  async update(
    id: string,
    data: IOrganisationUpdate,
  ): Promise<Organisation | null> {
    const organisation = await this.findById(id);

    if (!organisation) {
      return null;
    }

    if (data.name) {
      organisation.name = data.name;
    }

    if (data.description !== undefined) {
      organisation.description = data.description;
    }

    if (data.website !== undefined) {
      organisation.website = data.website;
    }

    if (data.address !== undefined) {
      organisation.address = data.address;
    }

    if (data.country) {
      organisation.country = data.country;
    }

    if (data.parentId !== undefined) {
      organisation.parentId = data.parentId;
    }

    if (data.status) {
      organisation.status = data.status;
    }

    return this.organisationRepository.save(organisation);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.organisationRepository.delete(id);
    return result.affected > 0;
  }
}
