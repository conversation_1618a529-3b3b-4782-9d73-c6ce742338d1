import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
} from '@nestjs/common';
import { OrganisationSerializer } from '@hirenetix/models/organisations/serializers/organisation.serializer';
import {
  IOrganisationUpdate,
  IOrganisationSearchQuery,
} from '@hirenetix/models/organisations/interfaces/organisation.interface';
import { JwtAuthGuard } from '@hirenetix/authentication/jwt-auth.guard';
import { Roles } from '@hirenetix/common/decorators/metadata/roles.decorator';
import { SystemRoles } from '@hirenetix/models/roles/constants';
import { RolesGuard } from '@hirenetix/common/guards/roles.guard';
import { OrganisationsService } from './organisations.service';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { S3Service } from '../../providers/storage/s3/s3.service';
import {
  PresignedUrlRequestDto,
  PresignedUrlResponseDto,
} from '../../providers/storage/s3/dto/presigned-url.dto';
import {
  API_OPERATIONS,
  API_RESPONSES,
  JWT_AUTH_NAME,
} from '@hirenetix/common/constants';

@Controller('organisations')
export class OrganisationsController {
  constructor(
    private readonly organisationsService: OrganisationsService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Get all organisations - Only super_admin can access this endpoint.
   * This API is protected and only users with the super_admin role can access it
   */
  @Get()
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(SystemRoles.SUPER_ADMIN)
  async findAll(
    @Query() query: IOrganisationSearchQuery,
  ): Promise<{ data: OrganisationSerializer[]; count: number }> {
    return this.organisationsService.find(query);
  }

  /**
   * Update organisation - Only super_admin can access this endpoint.
   * This API is protected and only users with the super_admin role can update organisations
   */
  @Put(':id')
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(SystemRoles.SUPER_ADMIN)
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() data: IOrganisationUpdate,
  ): Promise<OrganisationSerializer> {
    return this.organisationsService.update(id, data);
  }

  /**
   * Delete organisation - Only super_admin can access this endpoint.
   * This API is protected and only users with the super_admin role can delete organisations
   */
  @Delete(':id')
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(SystemRoles.SUPER_ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.organisationsService.delete(id);
  }

  /**
   * Generate a presigned URL for S3 operations
   */
  @Post('presigned-url')
  @ApiOperation({ summary: API_OPERATIONS.GENERATE_PRESIGNED_URL })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: API_RESPONSES.PRESIGNED_URL_SUCCESS,
    type: PresignedUrlResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: API_RESPONSES.UNPROCESSABLE_ENTITY,
  })
  @ApiBearerAuth(JWT_AUTH_NAME)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @HttpCode(HttpStatus.CREATED)
  async getPresignedUrl(
    @Body() presignedUrlDto: PresignedUrlRequestDto,
  ): Promise<PresignedUrlResponseDto> {
    const { contentType, filename } = presignedUrlDto;
    const expiresIn = 3600;
    const key = `organisations/logo/${filename}`;
    const url = await this.s3Service.getPresignedUploadUrl(
      key,
      contentType,
      expiresIn,
    );

    return {
      url,
      key,
      expiresIn,
    };
  }
}
