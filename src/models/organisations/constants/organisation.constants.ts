export const ORGANISATION_TABLE_NAME = 'organisations';
export const ORGANISATION_ENTITY_NAME = 'Organisation';
export const ORGANISATION_REPO_TOKEN = 'ORGANISATION_REPOSITORY';

export enum OrganisationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
}

export const ORGANISATION_VALIDATION = {
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 100,
  DESCRIPTION_MAX_LENGTH: 1000,
  WEBSITE_REGEX:
    /^(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)+([/?].*)?$/,
  ADDRESS_MAX_LENGTH: 255,
};

export const ORGANISATION_RELATIONS = {
  DEPARTMENTS: 'departments',
  USERS: 'users',
  JOBS: 'jobs',
};

export const ORGANISATION_SELECT_DEFAULT = {
  id: true,
  name: true,
  description: true,
  size: true,
  type: true,
  status: true,
  createdAt: true,
  updatedAt: true,
};
