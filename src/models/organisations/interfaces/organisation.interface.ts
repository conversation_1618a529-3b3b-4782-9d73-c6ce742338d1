import { OrganisationStatus } from '@hirenetix/models/organisations/constants';

export interface IOrganisation {
  id: string;
  name: string;
  description?: string;
  website?: string;
  address?: string;
  country: string;
  parentId?: string;
  status: OrganisationStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface IOrganisationCreate {
  name: string;
  description?: string;
  website?: string;
  address?: string;
  logoUrl?: string;
}

export interface IOrganisationUpdate {
  name?: string;
  description?: string;
  website?: string;
  address?: string;
  country?: string;
  parentId?: string;
  status?: OrganisationStatus;
}

export interface IOrganisationSearchQuery {
  id?: string;
  name?: string;
  country?: string;
  parentId?: string;
  status?: OrganisationStatus;
}
