import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';

export class OrganisationSerializer {
  id: string;
  name: string;
  description: string | null;
  logoUrl: string | null;
  website: string | null;
  address: string | null;
  country: string;
  parentId: string | null;
  status: string;
  createdAt: Date;
  updatedAt: Date;

  constructor(organisation: Organisation) {
    this.id = organisation.id;
    this.name = organisation.name;
    this.description = organisation.description || null;
    this.logoUrl = organisation.logoUrl || null;
    this.website = organisation.website || null;
    this.address = organisation.address || null;
    this.country = organisation.country;
    this.parentId = organisation.parentId || null;
    this.status = organisation.status;
    this.createdAt = organisation.createdAt;
    this.updatedAt = organisation.updatedAt;
  }

  static serialize(organisation: Organisation): OrganisationSerializer {
    return new OrganisationSerializer(organisation);
  }

  static serializeMany(
    organisations: Organisation[],
  ): OrganisationSerializer[] {
    return organisations.map((organisation) => this.serialize(organisation));
  }
}
