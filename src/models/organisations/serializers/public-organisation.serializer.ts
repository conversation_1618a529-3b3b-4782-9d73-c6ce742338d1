import { ApiProperty } from '@nestjs/swagger';
import { Organisation } from '../entities/organisation.entity';
import { Exclude, Expose } from 'class-transformer';
import { OrganisationSerializer } from './organisation.serializer';

@Exclude()
export class PublicOrganisationSerializer {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  name: string;

  @Expose()
  @ApiProperty({ nullable: true })
  description: string | null;

  @Expose()
  @ApiProperty({ nullable: true })
  logoUrl: string | null;

  @Expose()
  @ApiProperty({ nullable: true })
  website: string | null;

  @Expose()
  @ApiProperty()
  country: string;

  constructor(partial: Partial<PublicOrganisationSerializer>) {
    Object.assign(this, partial);
  }

  static serialize(
    organisation: Organisation | OrganisationSerializer,
  ): PublicOrganisationSerializer {
    return new PublicOrganisationSerializer({
      id: organisation.id,
      name: organisation.name,
      description: organisation.description,
      logoUrl: organisation.logoUrl,
      website: organisation.website,
      country: organisation.country,
    });
  }

  static serializeMany(
    organisations: (Organisation | OrganisationSerializer)[],
  ): PublicOrganisationSerializer[] {
    return organisations.map((organisation) => this.serialize(organisation));
  }
}
