import {
  <PERSON><PERSON>otE<PERSON>y,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ORGANISATION_VALIDATION } from '../constants/organisation.constants';

export class CreateOrganisationDto {
  @IsNotEmpty()
  @IsString()
  @MinLength(ORGANISATION_VALIDATION.NAME_MIN_LENGTH)
  @MaxLength(ORGANISATION_VALIDATION.NAME_MAX_LENGTH)
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(ORGANISATION_VALIDATION.DESCRIPTION_MAX_LENGTH)
  description?: string;

  @IsString()
  @MaxLength(ORGANISATION_VALIDATION.ADDRESS_MAX_LENGTH)
  address?: string;
}
