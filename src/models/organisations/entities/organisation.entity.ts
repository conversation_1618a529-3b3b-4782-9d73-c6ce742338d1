import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import {
  ORGANISATION_TABLE_NAME,
  ORGANISATION_VALIDATION,
  OrganisationStatus,
} from '../constants/organisation.constants';
import { Location } from '../../locations/entities/location.entity';

@Entity(ORGANISATION_TABLE_NAME)
export class Organisation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    length: ORGANISATION_VALIDATION.NAME_MAX_LENGTH,
    unique: true,
  })
  name: string;

  @Column({
    nullable: true,
    type: 'text',
  })
  description: string;

  @Column({ nullable: true, length: 255 })
  website: string;

  @Column({ name: 'logo_url', nullable: true })
  logoUrl: string;

  @Column({ nullable: true, type: 'text' })
  address: string;

  @Column({ length: 100, nullable: true })
  country: string;

  @Column({ name: 'parent_id', nullable: true })
  parentId: string;

  @ManyToOne(() => Organisation, (organisation) => organisation.children, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'parent_id' })
  parent: Organisation;

  @OneToMany(() => Organisation, (organisation) => organisation.parent)
  children: Organisation[];

  @OneToMany(() => Location, (location) => location.organisation, {
    cascade: true,
  })
  locations: Location[];

  @Column({
    type: 'enum',
    enum: OrganisationStatus,
    default: OrganisationStatus.ACTIVE,
  })
  status: OrganisationStatus;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
