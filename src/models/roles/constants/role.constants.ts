export const ROLE_TABLE_NAME = 'roles';
export const ROLE_ENTITY_NAME = 'Role';
export const ROLE_REPO_TOKEN = 'ROLE_REPOSITORY';

export enum RoleType {
  LOCATION = 'location',
  CORPORATE = 'corporate',
}

export enum SystemRoles {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  MANAGER = 'manager',
  EMPLOYEE = 'employee',
  CANDIDATE = 'candidate',
  GUEST = 'guest',
}

export const ROLE_PERMISSIONS_TABLE_NAME = 'role_permissions';
export const ROLE_PERMISSIONS_ENTITY_NAME = 'RolePermission';
export const ROLE_PERMISSIONS_REPO_TOKEN = 'ROLE_PERMISSIONS_REPOSITORY';

export enum PermissionResources {
  ALL = '*',
  USERS = 'users',
  ROLES = 'roles',
  ORGANISATIONS = 'organisations',
  DEPARTMENTS = 'departments',
  JOBS = 'jobs',
  APPLICATIONS = 'applications',
  PROFILES = 'profiles',
  SETTINGS = 'settings',
  LOCATIONS = 'locations',
}

export enum PermissionModules {
  USERS = 'users',
  ROLES = 'roles',
  ORGANISATIONS = 'organisations',
  DEPARTMENTS = 'departments',
  JOBS = 'jobs',
  LOCATIONS = 'locations',
  CANDIDATES = 'candidates',
}

export enum PermissionActions {
  CREATE_USER = 'create_user',
  VIEW_ALL_USERS = 'view_all_users',
  EDIT_USER = 'edit_user',
  DELETE_USER = 'delete_user',
  MANAGE_USER = 'manage_user',
  CREATE_ROLE = 'create_role',
  VIEW_ALL_ROLES = 'view_all_roles',
  EDIT_ROLE = 'edit_role',
  DELETE_ROLE = 'delete_role',
  MANAGE_ROLE = 'manage_role',
  CREATE_LOCATION = 'create_location',
  VIEW_ALL_LOCATIONS = 'view_all_locations',
  EDIT_LOCATION = 'edit_location',
  DELETE_LOCATION = 'delete_location',
  MANAGE_LOCATION = 'manage_location',
  EDIT_ROLES = 'edit_roles',
  DELETE_ROLES = 'delete_roles',
  ADD_ROLES = 'add_roles',
  VIEW_ROLES = 'view_roles',
  VIEW_JOBS = 'view_jobs',
  EDIT_JOBS = 'edit_jobs',
  DELETE_JOBS = 'delete_jobs',
  CREATE_JOBS = 'create_jobs',
  PUBLISH_JOBS = 'publish_jobs',
  ACTIVE_JOBS = 'active_jobs',
  SET_JOB_VISIBILITY = 'set_job_visibility',
  SET_JOB_STATUS = 'set_job_status',
  CREATE_NEW_JOB_POSITION = 'create_new_job_position',
  EDIT_JOB_POSITION = 'edit_job_position',
  DELETE_JOB_POSITION = 'delete_job_position',
  VIEW_JOB_LISTINGS = 'view_job_listings',
  DUPLICATE_EXISTING_POSITION = 'duplicate_existing_position',
  VIEW_JOB_APPLICATIONS = 'view_job_applications',
  EDIT_JOB_APPLICATIONS = 'edit_job_applications',
  DELETE_JOB_APPLICATIONS = 'delete_job_applications',
  CREATE_JOB_APPLICATIONS = 'create_job_applications',
}

export enum ExportFormat {
  CSV = 'csv',
  XLSX = 'xlsx',
}
