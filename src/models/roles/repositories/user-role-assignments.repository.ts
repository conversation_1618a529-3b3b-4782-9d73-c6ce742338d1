import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UserRoleAssignment } from '@hirenetix/models/roles/entities/user-role-assignment.entity';
import {
  IUserRoleAssignmentCreate,
  IUserRoleAssignmentSearchQuery,
} from '@hirenetix/models/roles/interfaces/user-role-assignment.interface';

@Injectable()
export class UserRoleAssignmentsRepository {
  constructor(
    @InjectRepository(UserRoleAssignment)
    private userRoleAssignmentRepository: Repository<UserRoleAssignment>,
  ) {}

  async findById(
    id: string,
    relations = false,
  ): Promise<UserRoleAssignment | null> {
    return this.userRoleAssignmentRepository.findOne({
      where: { id },
      relations: relations ? ['user', 'role'] : [],
    });
  }

  async findByUserAndRole(
    userId: string,
    roleId: string,
  ): Promise<UserRoleAssignment | null> {
    return this.userRoleAssignmentRepository.findOne({
      where: {
        userId,
        roleId,
      },
    });
  }

  async find(
    query: IUserRoleAssignmentSearchQuery,
    relations = false,
  ): Promise<[UserRoleAssignment[], number]> {
    const queryBuilder =
      this.userRoleAssignmentRepository.createQueryBuilder('userRole');

    if (relations) {
      queryBuilder.leftJoinAndSelect('userRole.user', 'user');
      queryBuilder.leftJoinAndSelect('userRole.role', 'role');
    }

    if (query.id) {
      queryBuilder.andWhere('userRole.id = :id', { id: query.id });
    }

    if (query.userId) {
      queryBuilder.andWhere('userRole.userId = :userId', {
        userId: query.userId,
      });
    }

    if (query.roleId) {
      queryBuilder.andWhere('userRole.roleId = :roleId', {
        roleId: query.roleId,
      });
    }

    return queryBuilder.getManyAndCount();
  }

  async create(data: IUserRoleAssignmentCreate): Promise<UserRoleAssignment> {
    const userRoleAssignment = new UserRoleAssignment();
    userRoleAssignment.userId = data.userId;
    userRoleAssignment.roleId = data.roleId;

    return this.userRoleAssignmentRepository.save(userRoleAssignment);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.userRoleAssignmentRepository.delete(id);
    return result.affected > 0;
  }

  async deleteByUserAndRole(userId: string, roleId: string): Promise<boolean> {
    const result = await this.userRoleAssignmentRepository.delete({
      userId,
      roleId,
    });
    return result.affected > 0;
  }

  async getUserRoles(userId: string): Promise<UserRoleAssignment[]> {
    return this.userRoleAssignmentRepository.find({
      where: { userId },
      relations: [
        'role',
        'role.rolePermissions',
        'role.rolePermissions.permission',
      ],
    });
  }
}
