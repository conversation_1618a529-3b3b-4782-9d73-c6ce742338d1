import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import {
  IRoleCreate,
  IRoleUpdate,
  IRoleSearchQuery,
} from '@hirenetix/models/roles/interfaces/role.interface';
import { SystemRoles } from '../constants';

@Injectable()
export class RolesRepository {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
  ) {}

  async findById(id: string, relations = false): Promise<Role | null> {
    return this.roleRepository.findOne({
      where: { id },
      relations: relations
        ? ['rolePermissions', 'rolePermissions.permission']
        : [],
    });
  }

  async findByName(name: string): Promise<Role | null> {
    return this.roleRepository.findOne({
      where: { name: name as <PERSON>R<PERSON><PERSON> },
    });
  }

  async find(
    query: IRoleSearchQuery,
    relations = false,
  ): Promise<[Role[], number]> {
    const queryBuilder = this.roleRepository.createQueryBuilder('role');

    if (relations) {
      queryBuilder.leftJoinAndSelect('role.rolePermissions', 'rolePermissions');
      queryBuilder.leftJoinAndSelect(
        'rolePermissions.permission',
        'permission',
      );
    }

    if (query.id) {
      queryBuilder.andWhere('role.id = :id', { id: query.id });
    }

    if (query.name) {
      queryBuilder.andWhere('role.name ILIKE :name', {
        name: `%${query.name}%`,
      });
    }

    return queryBuilder.getManyAndCount();
  }

  async create(data: IRoleCreate): Promise<Role> {
    const role = new Role();
    role.name = data.name as SystemRoles;
    role.description = data.description;

    return this.roleRepository.save(role);
  }

  async update(id: string, data: IRoleUpdate): Promise<Role | null> {
    const role = await this.findById(id);

    if (!role) {
      return null;
    }

    if (data.name) {
      role.name = data.name as SystemRoles;
    }

    if (data.description !== undefined) {
      role.description = data.description;
    }

    return this.roleRepository.save(role);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.roleRepository.delete(id);
    return result.affected > 0;
  }
}
