import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RolePermission } from '@hirenetix/models/roles/entities/role-permission.entity';
import {
  IRolePermissionCreate,
  IRolePermissionSearchQuery,
} from '@hirenetix/models/roles/interfaces/role-permission.interface';

@Injectable()
export class RolePermissionsRepository {
  constructor(
    @InjectRepository(RolePermission)
    private rolePermissionRepository: Repository<RolePermission>,
  ) {}

  async findById(
    id: string,
    relations = false,
  ): Promise<RolePermission | null> {
    return this.rolePermissionRepository.findOne({
      where: { id },
      relations: relations ? ['role', 'permission'] : [],
    });
  }

  async findByRoleAndPermission(
    roleId: string,
    permissionId: string,
  ): Promise<RolePermission | null> {
    return this.rolePermissionRepository.findOne({
      where: {
        roleId,
        permissionId,
      },
    });
  }

  async find(
    query: IRolePermissionSearchQuery,
    relations = false,
  ): Promise<[RolePermission[], number]> {
    const queryBuilder =
      this.rolePermissionRepository.createQueryBuilder('rolePermission');

    if (relations) {
      queryBuilder.leftJoinAndSelect('rolePermission.role', 'role');
      queryBuilder.leftJoinAndSelect('rolePermission.permission', 'permission');
    }

    if (query.id) {
      queryBuilder.andWhere('rolePermission.id = :id', { id: query.id });
    }

    if (query.roleId) {
      queryBuilder.andWhere('rolePermission.roleId = :roleId', {
        roleId: query.roleId,
      });
    }

    if (query.permissionId) {
      queryBuilder.andWhere('rolePermission.permissionId = :permissionId', {
        permissionId: query.permissionId,
      });
    }

    return queryBuilder.getManyAndCount();
  }

  async create(data: IRolePermissionCreate): Promise<RolePermission> {
    const rolePermission = new RolePermission();
    rolePermission.roleId = data.roleId;
    rolePermission.permissionId = data.permissionId;

    return this.rolePermissionRepository.save(rolePermission);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.rolePermissionRepository.delete(id);
    return result.affected > 0;
  }

  async deleteByRoleAndPermission(
    roleId: string,
    permissionId: string,
  ): Promise<boolean> {
    const result = await this.rolePermissionRepository.delete({
      roleId,
      permissionId,
    });
    return result.affected > 0;
  }

  async getRolePermissions(roleId: string): Promise<RolePermission[]> {
    return this.rolePermissionRepository.find({
      where: { roleId },
      relations: ['permission'],
    });
  }
}
