import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Permission } from '@hirenetix/models/roles/entities/permission.entity';
import {
  IPermissionCreate,
  IPermissionUpdate,
  IPermissionSearchQuery,
} from '@hirenetix/models/roles/interfaces/permission.interface';

@Injectable()
export class PermissionsRepository {
  constructor(
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
  ) {}

  async findAll(): Promise<Permission[]> {
    return await this.permissionRepository.find({
      order: {
        module: 'ASC',
        name: 'ASC',
      },
    });
  }

  async findById(id: string): Promise<Permission | null> {
    return this.permissionRepository.findOne({
      where: { id },
    });
  }

  async findByName(name: string): Promise<Permission | null> {
    return this.permissionRepository.findOne({
      where: { name },
    });
  }

  async find(query: IPermissionSearchQuery): Promise<[Permission[], number]> {
    const queryBuilder =
      this.permissionRepository.createQueryBuilder('permission');

    if (query.id) {
      queryBuilder.andWhere('permission.id = :id', { id: query.id });
    }

    if (query.name) {
      queryBuilder.andWhere('permission.name ILIKE :name', {
        name: `%${query.name}%`,
      });
    }

    return queryBuilder.getManyAndCount();
  }

  async create(data: IPermissionCreate): Promise<Permission> {
    const permission = new Permission();
    permission.name = data.name;
    permission.description = data.description;

    return this.permissionRepository.save(permission);
  }

  async update(
    id: string,
    data: IPermissionUpdate,
  ): Promise<Permission | null> {
    const permission = await this.findById(id);

    if (!permission) {
      return null;
    }

    if (data.name) {
      permission.name = data.name;
    }

    if (data.description !== undefined) {
      permission.description = data.description;
    }

    return this.permissionRepository.save(permission);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.permissionRepository.delete(id);
    return result.affected > 0;
  }
}
