import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsInt,
  Min,
  <PERSON>,
  Is<PERSON><PERSON>y,
  IsBoolean,
  IsDefined,
  IsEnum,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ExportFormat } from '../constants/role.constants';
import { ParseOptionalBoolean } from '../../../common/decorators/validations/boolean.mapper';

export class RoleSearchQueryDto {
  @ApiProperty({
    description: 'Page number (starting from 1)',
    example: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    required: false,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit cannot exceed 100' })
  limit?: number = 10;

  @ApiProperty({
    description:
      'Search text for role name, description, or permission description',
    example: 'admin',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Search must be a string' })
  search?: string;

  @ApiProperty({
    description: 'Filter roles by specific module(s)',
    example: ['Dashboard', 'Users'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (Array.isArray(value)) return value;
    if (typeof value === 'string') return [value];
    return undefined;
  })
  @IsArray({ message: 'Modules must be an array' })
  @IsString({ each: true, message: 'Each module must be a string' })
  modules?: string[];

  @ApiProperty({
    description: 'Filter roles by type',
    example: 'system',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Type must be a string' })
  type?: string;

  @ApiProperty({
    description: 'Return minimal data',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsDefined()
  @Transform(({ value }) =>
    value === 'true' ? true : value === 'false' ? false : value,
  )
  @IsBoolean({ message: 'Minimal must be a boolean' })
  minimal?: boolean = false;

  @ApiProperty({
    description:
      'Flag to export the data instead of returning paginated results',
    example: false,
    required: false,
    default: false,
  })
  @IsOptional()
  @ParseOptionalBoolean()
  @IsBoolean({ message: 'Export flag must be a boolean' })
  export?: boolean = false;

  @ApiProperty({
    description: 'Format for exporting data (csv or xlsx)',
    enum: ExportFormat,
    required: false,
    default: ExportFormat.CSV,
  })
  @IsOptional()
  @IsEnum(ExportFormat, { message: 'Invalid export format' })
  exportFormat?: ExportFormat = ExportFormat.CSV;
}
