import { ApiProperty } from '@nestjs/swagger';

export class UserPermissionsResponseDto {
  @ApiProperty({
    description: 'Permissions grouped by module',
    example: {
      users: ['create_user', 'view_all_users', 'edit_user'],
      locations: ['create_location', 'view_all_locations'],
      roles: ['view_all_roles'],
    },
    type: 'object',
    additionalProperties: {
      type: 'array',
      items: {
        type: 'string',
      },
    },
  })
  permissions: Record<string, string[]>;
}
