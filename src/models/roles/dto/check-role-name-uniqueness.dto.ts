import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength } from 'class-validator';

export class CheckRoleNameUniquenessDto {
  @ApiProperty({
    description: 'Role name to check for uniqueness',
    example: 'HR Manager',
  })
  @IsNotEmpty({ message: 'Role name is required' })
  @IsString({ message: 'Role name must be a string' })
  @MaxLength(255, {
    message: 'Role name cannot be longer than 255 characters',
  })
  name: string;
}
