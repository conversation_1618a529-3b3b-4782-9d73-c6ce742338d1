import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>A<PERSON>y,
  IsOptional,
  IsString,
  <PERSON><PERSON>ength,
  IsEnum,
} from 'class-validator';
import { RoleType } from '../constants/role.constants';

export class UpdateRoleDto {
  @ApiProperty({
    description: 'Description of the role',
    example: 'Manages HR functions and personnel',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  @MaxLength(500, {
    message: 'Description cannot be longer than 500 characters',
  })
  description?: string;

  @ApiProperty({
    description: 'Type of the role',
    enum: RoleType,
    example: RoleType.CORPORATE,
    required: false,
  })
  @IsOptional()
  @IsEnum(RoleType, { message: 'Invalid role type' })
  roleType?: RoleType;

  @ApiProperty({
    description: 'Array of permission names to assign to the role',
    example: ['users:read', 'users:create', 'departments:read'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Permissions must be an array' })
  @IsString({ each: true, message: 'Each permission must be a string' })
  permissions?: string[];
}
