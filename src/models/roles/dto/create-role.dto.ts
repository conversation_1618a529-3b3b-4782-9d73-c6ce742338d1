import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ot<PERSON>mpty,
  <PERSON><PERSON><PERSON>al,
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RoleType } from '../constants/role.constants';

export class CreateRoleDto {
  @ApiProperty({
    description: 'A name for the role',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Type of the role',
    enum: RoleType,
    example: RoleType.CORPORATE,
  })
  @IsNotEmpty()
  @IsEnum(RoleType)
  roleType: RoleType;

  @ApiPropertyOptional({
    description: 'A description of the role',
    example: 'Role for managing team members and resources',
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({
    description: 'Array of permission names to assign to the role',
    type: [String],
    example: ['view_dashboard_overview', 'manage_users'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  permissions?: string[];
}
