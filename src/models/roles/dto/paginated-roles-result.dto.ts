import { ApiProperty } from '@nestjs/swagger';
import { RoleSerializer } from '../serializers/role.serializer';

export class PaginatedRolesResultDto {
  @ApiProperty({
    description: 'Array of roles',
    type: [RoleSerializer],
  })
  data: RoleSerializer[];

  @ApiProperty({
    description: 'Total number of roles matching the query',
    example: 42,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 5,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Whether there is a next page',
    example: true,
  })
  hasNextPage: boolean;

  @ApiProperty({
    description: 'Whether there is a previous page',
    example: false,
  })
  hasPreviousPage: boolean;
}
