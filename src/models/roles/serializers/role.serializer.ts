import { Role } from '@hirenetix/models/roles/entities/role.entity';
import { RoleType } from '../constants/role.constants';
export class RoleSerializer {
  id: string;
  name: string;
  description: string | null;
  roleType: RoleType;
  createdAt: Date;
  updatedAt: Date;
  permissions?: string[];

  constructor(role: Role, includePermissions = false) {
    this.id = role.id;
    this.name = role.name;
    this.description = role.description || null;
    this.roleType = role.roleType;
    this.createdAt = role.createdAt;
    this.updatedAt = role.updatedAt;

    // Include permissions if requested and available
    if (
      includePermissions &&
      role.rolePermissions &&
      role.rolePermissions.length > 0
    ) {
      this.permissions = role.rolePermissions
        .map((rp) => rp.permission?.name)
        .filter((name) => !!name);
    }
  }

  static serialize(role: Role, includePermissions = false): RoleSerializer {
    return new RoleSerializer(role, includePermissions);
  }

  static serializeMany(
    roles: Role[],
    includePermissions = false,
  ): RoleSerializer[] {
    return roles.map((role) => this.serialize(role, includePermissions));
  }
}
