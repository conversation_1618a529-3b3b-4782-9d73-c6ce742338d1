import { ApiProperty } from '@nestjs/swagger';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import { RoleType } from '../constants/role.constants';
export class RoleWithPermissionsSerializer {
  @ApiProperty({
    description: 'Unique identifier of the role',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the role',
    example: 'HR Manager',
  })
  name: string;

  @ApiProperty({
    description: 'Description of the role',
    example: 'Manages HR functions and personnel',
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    description: 'Whether this role is a custom user-created role',
    example: true,
  })
  custom: boolean;

  @ApiProperty({
    description: 'Type of the role',
    example: 'location',
  })
  roleType: RoleType;

  @ApiProperty({
    description: 'Date when the role was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the role was last updated',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'List of permission names associated with this role',
    example: ['view_dashboard_overview', 'duplicate_existing_position'],
    type: [String],
  })
  permissions: string[];

  constructor(role: Role) {
    this.id = role.id;
    this.name = role.name;
    this.description = role.description || null;
    this.custom = role.custom;
    this.roleType = role.roleType;
    this.createdAt = role.createdAt;
    this.updatedAt = role.updatedAt;

    // Transform role permissions into an array of permission names
    this.permissions = [];
    if (role.rolePermissions && role.rolePermissions.length > 0) {
      this.permissions = role.rolePermissions
        .filter((rp) => !!rp.permission)
        .map((rp) => rp.permission.name);
    }
  }

  static serialize(role: Role): RoleWithPermissionsSerializer {
    return new RoleWithPermissionsSerializer(role);
  }
}
