import { Permission } from '../entities/permission.entity';

export class PermissionSerializer {
  id: string;
  name: string;
  description: string | null;
  module: string | null;

  constructor(permission: Permission) {
    this.id = permission.id;
    this.name = permission.name;
    this.description = permission.description;
    this.module = permission.module;
  }

  static serialize(permission: Permission): PermissionSerializer {
    return new PermissionSerializer(permission);
  }

  static serializeMany(permissions: Permission[]): PermissionSerializer[] {
    return permissions.map((permission) => this.serialize(permission));
  }
}
