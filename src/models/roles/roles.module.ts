import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { Role } from './entities/role.entity';
import { Permission } from './entities/permission.entity';
import { UserRoleAssignment } from './entities/user-role-assignment.entity';
import { RolePermission } from './entities/role-permission.entity';

// Repositories
import { RolesRepository } from './repositories/roles.repository';
import { PermissionsRepository } from './repositories/permissions.repository';
import { UserRoleAssignmentsRepository } from './repositories/user-role-assignments.repository';
import { RolePermissionsRepository } from './repositories/role-permissions.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Role,
      Permission,
      UserRoleAssignment,
      RolePermission,
    ]),
  ],
  providers: [
    RolesRepository,
    PermissionsRepository,
    UserRoleAssignmentsRepository,
    RolePermissionsRepository,
  ],
  exports: [
    RolesRepository,
    PermissionsRepository,
    UserRoleAssignmentsRepository,
    RolePermissionsRepository,
  ],
})
export class RolesModule {}
