import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { UserRoleAssignment } from './user-role-assignment.entity';
import { RolePermission } from './role-permission.entity';
import { ROLE_TABLE_NAME, RoleType } from '../constants/role.constants';
import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';

@Entity(ROLE_TABLE_NAME)
export class Role {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    unique: true,
    type: 'varchar',
  })
  name: string;

  @Column({ nullable: true, type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: RoleType,
    name: 'role_type',
    nullable: false,
  })
  roleType: RoleType;

  @Column({ name: 'tenant_id', type: 'uuid', nullable: true })
  tenantId: string;

  @ManyToOne(() => Organisation, { onDelete: 'CASCADE', nullable: true })
  @JoinColumn({ name: 'tenant_id' })
  tenant: Organisation;

  @Column({ default: true })
  custom: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @OneToMany(() => UserRoleAssignment, (userRole) => userRole.role)
  userRoleAssignments: UserRoleAssignment[];

  @OneToMany(() => RolePermission, (rolePermission) => rolePermission.role)
  rolePermissions: RolePermission[];
}
