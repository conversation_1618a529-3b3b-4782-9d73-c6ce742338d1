import { ApiProperty } from '@nestjs/swagger';
import { Candidate } from '../entities/candidate.entity';
import { Exclude, Expose } from 'class-transformer';

@Exclude()
export class CandidateSerializer {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  candidateId: number;

  @Expose()
  @ApiProperty()
  email: string;

  @Expose()
  @ApiProperty()
  phone: string;

  @Expose()
  @ApiProperty()
  name: string;

  @Expose()
  @ApiProperty()
  address: string;

  @Expose()
  @ApiProperty()
  resumeUrl: string;

  @Expose()
  @ApiProperty()
  city: string;

  @Expose()
  @ApiProperty()
  country: string;

  @Expose()
  @ApiProperty()
  status: string;

  @Expose()
  @ApiProperty()
  createdAt: Date;

  @Expose()
  @ApiProperty()
  updatedAt: Date;

  constructor(partial: Partial<CandidateSerializer>) {
    Object.assign(this, partial);
  }

  static serialize(candidate: Candidate): CandidateSerializer {
    return new CandidateSerializer({
      id: candidate.id,
      candidateId: candidate.candidateId,
      email: candidate.email,
      phone: candidate.phone,
      name: candidate.name,
      address: candidate.address,
      resumeUrl: candidate.resumeUrl,
      city: candidate.city,
      country: candidate.country,
      status: candidate.status,
      createdAt: candidate.createdAt,
      updatedAt: candidate.updatedAt,
    });
  }

  static serializeMany(candidates: Candidate[]): CandidateSerializer[] {
    return candidates.map((candidate) => this.serialize(candidate));
  }
}
