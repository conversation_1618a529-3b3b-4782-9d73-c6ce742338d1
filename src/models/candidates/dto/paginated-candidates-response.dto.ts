import { ApiProperty } from '@nestjs/swagger';
import { CandidateSerializer } from '../serializers/candidate.serializer';

export class PaginatedCandidatesResponseDto {
  @ApiProperty({ type: [CandidateSerializer] })
  data: CandidateSerializer[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  totalPages: number;

  @ApiProperty()
  hasNextPage: boolean;

  @ApiProperty()
  hasPreviousPage: boolean;
}
