import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsPhoneNumber, IsString, Length } from 'class-validator';

export class UpdateCandidateDto {
  @ApiPropertyOptional({ description: 'Candidate phone number' })
  @IsPhoneNumber()
  @IsOptional()
  phone?: string;

  @ApiPropertyOptional({ description: 'Candidate name' })
  @IsString()
  @Length(1, 300)
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ description: 'Candidate address' })
  @IsString()
  @Length(1, 300)
  @IsOptional()
  address?: string;

  @ApiPropertyOptional({ description: 'Resume file URL' })
  @IsOptional()
  @IsString()
  resumeUrl?: string;

  @ApiPropertyOptional({ description: 'City' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  city?: string;

  @ApiPropertyOptional({ description: 'Country' })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  country?: string;
}
