import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsOptional,
  IsPhoneNumber,
  IsString,
  Length,
} from 'class-validator';

export class CreateCandidateDto {
  @ApiProperty({ description: 'Candidate email address' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'Candidate phone number' })
  @IsPhoneNumber()
  @IsOptional()
  phone?: string;

  @ApiProperty({ description: 'Candidate name' })
  @IsString()
  @Length(1, 300)
  name: string;

  @ApiPropertyOptional({ description: 'Candidate address' })
  @IsString()
  @Length(1, 300)
  @IsOptional()
  address?: string;

  @ApiPropertyOptional({ description: 'Resume file URL' })
  @IsOptional()
  @IsString()
  resumeUrl?: string;

  @ApiPropertyOptional({ description: 'City' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  city?: string;

  @ApiPropertyOptional({ description: 'Country' })
  @IsString()
  @Length(1, 100)
  @IsOptional()
  country?: string;
}
