import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Candidate } from '../entities/candidate.entity';
import { TenantAwareRepository } from '../../../modules/tenant/tenant-aware.repository';

@Injectable()
export class CandidateRepository extends TenantAwareRepository<Candidate> {
  constructor(
    @InjectRepository(Candidate)
    protected readonly repository: Repository<Candidate>,
  ) {
    super();
  }

  async create(candidate: Partial<Candidate>): Promise<Candidate> {
    const newCandidate = this.repository.create(candidate);
    return this.repository.save(newCandidate);
  }

  async findById(id: string): Promise<Candidate> {
    return this.repository.findOne({
      where: { id },
      relations: ['user', 'organisation'],
    });
  }

  async findByEmail(email: string): Promise<Candidate> {
    return this.repository.findOne({
      where: { email },
      relations: ['user', 'organisation'],
    });
  }

  async findByPhone(phone: string): Promise<Candidate> {
    return this.repository.findOne({
      where: { phone },
      relations: ['user', 'organisation'],
    });
  }

  async update(id: string, candidate: Partial<Candidate>): Promise<Candidate> {
    await this.repository.update(id, candidate);
    return this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
  ): Promise<[Candidate[], number]> {
    const [candidates, total] = await this.repository.findAndCount({
      relations: ['user', 'organisation'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return [candidates, total];
  }
}
