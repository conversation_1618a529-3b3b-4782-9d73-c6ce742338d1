import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Organisation } from '../../organisations/entities/organisation.entity';
import { LOCATION_TABLE_NAME } from '../constants/location.constants';
import { TenantEntityBase } from '@hirenetix/models/shared/entities/tenant-entity.base';

@Entity(LOCATION_TABLE_NAME)
export class Location extends TenantEntityBase {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 255 })
  city: string;

  @Column({ type: 'varchar', length: '100', nullable: true })
  state: string;

  @Column({ type: 'varchar', length: '100', nullable: true })
  country: string;

  @Column({ type: 'varchar', length: 50, name: 'location_code', unique: true })
  locationCode: string;

  @Column({ type: 'text', name: 'address' })
  address: string;

  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  latitude: number;

  @Column({ type: 'decimal', precision: 11, scale: 8, nullable: true })
  longitude: number;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'place_id' })
  placeId: string;

  @Column({ name: 'tenant_id' })
  tenantId: string;

  @Column({ type: 'enum', enum: ['active', 'inactive'], default: 'active' })
  status: string;

  @ManyToOne(() => Organisation, (organisation) => organisation.locations, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'tenant_id' })
  organisation: Organisation;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
