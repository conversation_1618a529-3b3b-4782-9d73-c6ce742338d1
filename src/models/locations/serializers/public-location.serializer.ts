import { ApiProperty } from '@nestjs/swagger';
import { Location } from '../entities/location.entity';

export class PublicLocationSerializer {
  @ApiProperty({
    description: 'Location ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Location name',
    example: 'Downtown Office',
  })
  name: string;

  @ApiProperty({
    description: 'City where the location is situated',
    example: 'New York',
  })
  city: string;

  @ApiProperty({
    description: 'State/Province/Region of the location',
    example: 'New York',
    required: false,
  })
  state: string | null;

  @ApiProperty({
    description: 'Country of the location',
    example: 'United States',
    required: false,
  })
  country: string | null;

  @ApiProperty({
    description: 'Full address of the location',
    example: '123 Main St, New York, NY 10001',
  })
  address: string;

  @ApiProperty({
    description: 'Latitude of the location',
    example: 40.7128,
    required: false,
  })
  latitude: number | null;

  @ApiProperty({
    description: 'Longitude of the location',
    example: -74.006,
    required: false,
  })
  longitude: number | null;

  constructor(location: Location) {
    this.id = location.id;
    this.name = location.name;
    this.city = location.city;
    this.state = location.state || null;
    this.country = location.country || null;
    this.address = location.address;
    this.latitude = location.latitude || null;
    this.longitude = location.longitude || null;
  }

  static serialize(location: Location): PublicLocationSerializer {
    return new PublicLocationSerializer(location);
  }

  static serializeMany(locations: Location[]): PublicLocationSerializer[] {
    return locations.map((location) => this.serialize(location));
  }
}
