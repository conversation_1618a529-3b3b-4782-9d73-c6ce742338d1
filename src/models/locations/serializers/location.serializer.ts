import { Location } from '../entities/location.entity';

export class LocationSerializer {
  id: string;
  name: string;
  city: string;
  state: string | null;
  country: string | null;
  locationCode: string;
  address: string;
  latitude: number | null;
  longitude: number | null;
  placeId: string | null;
  tenantId: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;

  constructor(location: Location) {
    this.id = location.id;
    this.name = location.name;
    this.city = location.city;
    this.state = location.state || null;
    this.country = location.country || null;
    this.locationCode = location.locationCode;
    this.address = location.address;
    this.latitude = location.latitude || null;
    this.longitude = location.longitude || null;
    this.placeId = location.placeId || null;
    this.tenantId = location.tenantId;
    this.status = location.status;
    this.createdAt = location.createdAt;
    this.updatedAt = location.updatedAt;
  }

  static serialize(location: Location): LocationSerializer {
    return new LocationSerializer(location);
  }

  static serializeMany(locations: Location[]): LocationSerializer[] {
    return locations.map((location) => this.serialize(location));
  }
}
