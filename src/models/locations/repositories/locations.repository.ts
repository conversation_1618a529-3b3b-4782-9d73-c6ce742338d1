import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Location } from '../entities/location.entity';
import { LOCATION_SELECT_DEFAULT } from '../constants/location.constants';
import { TenantAwareRepository } from '../../../modules/tenant/tenant-aware.repository';
import { LocationSearchDto } from '../../../modules/locations/dto/location-search.dto';

@Injectable()
export class LocationsRepository extends TenantAwareRepository<Location> {
  constructor(
    @InjectRepository(Location)
    protected readonly repository: Repository<Location>,
  ) {
    super();
  }

  async findById(id: string): Promise<Location | null> {
    return await this.findOne({
      where: { id },
      select: LOCATION_SELECT_DEFAULT,
    });
  }

  async findByCode(locationCode: string): Promise<Location | null> {
    return await this.findOne({
      where: { locationCode },
      select: LOCATION_SELECT_DEFAULT,
    });
  }

  async findByTenantId(tenantId: string): Promise<Location[]> {
    return this.repository.find({
      where: {
        tenantId,
        status: 'active',
      },
      select: LOCATION_SELECT_DEFAULT,
    });
  }

  async create(data: Partial<Location>): Promise<Location> {
    const location = this.repository.create(data);
    return await this.repository.save(location);
  }

  async update(id: string, data: Partial<Location>): Promise<Location> {
    await this.repository.update(id, data);
    return await this.findById(id);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected > 0;
  }

  async findByTenantIdWithFilters(
    tenantId: string,
    filters: LocationSearchDto,
  ): Promise<Location[]> {
    const { search, status } = filters;
    const queryBuilder = this.createQueryBuilder('location').where(
      'location.tenantId = :tenantId',
      { tenantId },
    );

    if (search) {
      queryBuilder.andWhere(
        '(LOWER(location.name) LIKE LOWER(:search) OR LOWER(location.city) LIKE LOWER(:search))',
        { search: `%${search}%` },
      );
    }

    if (status) {
      queryBuilder.andWhere('location.status = :status', { status });
    }

    return queryBuilder.getMany();
  }

  async findWithPagination(
    tenantId: string,
    filters: LocationSearchDto,
  ): Promise<[Location[], number]> {
    const { search, status, page = 1, limit = 10 } = filters;
    const skip = (page - 1) * limit;

    const queryBuilder = this.createQueryBuilder('location').where(
      'location.tenantId = :tenantId',
      { tenantId },
    );

    if (search) {
      queryBuilder.andWhere(
        '(LOWER(location.name) LIKE LOWER(:search) OR LOWER(location.city) LIKE LOWER(:search))',
        { search: `%${search}%` },
      );
    }

    if (status) {
      queryBuilder.andWhere('location.status = :status', { status });
    }

    return queryBuilder
      .orderBy('location.createdAt', 'DESC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();
  }
}
