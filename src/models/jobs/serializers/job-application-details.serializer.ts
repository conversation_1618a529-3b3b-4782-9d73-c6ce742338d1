import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';
import { JobApplication } from '../entities/job-application.entity';
import { JobApplicationStatus } from '../entities/job-application.entity';

@Exclude()
export class QuestionAnswerSerializer {
  @Expose()
  @ApiProperty()
  id: number;

  @Expose()
  @ApiProperty()
  question: string;

  @Expose()
  @ApiProperty()
  answer: string;

  constructor(partial: Partial<QuestionAnswerSerializer>) {
    Object.assign(this, partial);
  }
}

@Exclude()
export class AIInsightsSerializer {
  @Expose()
  @ApiProperty()
  matchPercentage: number;

  @Expose()
  @ApiProperty()
  matchingKeywords: string[];

  @Expose()
  @ApiProperty()
  summary: string;

  constructor(partial: Partial<AIInsightsSerializer>) {
    Object.assign(this, partial);
  }
}

@Exclude()
export class CandidateDetailsSerializer {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  candidateId: number;

  @Expose()
  @ApiProperty()
  name: string;

  @Expose()
  @ApiProperty()
  email: string;

  @Expose()
  @ApiProperty()
  phone: string;

  @Expose()
  @ApiProperty()
  address: string;

  @Expose()
  @ApiProperty()
  city: string;

  @Expose()
  @ApiProperty()
  country: string;

  @Expose()
  @ApiProperty()
  resumeUrl: string;

  constructor(partial: Partial<CandidateDetailsSerializer>) {
    Object.assign(this, partial);
  }
}

@Exclude()
export class JobDetailsSerializer {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  title: string;

  @Expose()
  @ApiProperty()
  description: string;

  @Expose()
  @ApiProperty()
  city: string;

  @Expose()
  @ApiProperty()
  country: string;

  @Expose()
  @ApiProperty()
  status: string;

  constructor(partial: Partial<JobDetailsSerializer>) {
    Object.assign(this, partial);
  }
}

@Exclude()
export class WorkExperienceSerializer {
  @Expose()
  @ApiProperty()
  role: string;

  @Expose()
  @ApiProperty()
  company: string;

  @Expose()
  @ApiProperty()
  duration: string;

  constructor(partial: Partial<WorkExperienceSerializer>) {
    Object.assign(this, partial);
  }
}

@Exclude()
export class ProfessionProfileSerializer {
  @Expose()
  @ApiProperty()
  keySkills: string[];

  @Expose()
  @ApiProperty()
  yearsOfExperience: string;

  @Expose()
  @ApiProperty()
  education: string;

  @Expose()
  @ApiProperty({ type: [WorkExperienceSerializer] })
  @Type(() => WorkExperienceSerializer)
  workExperience: WorkExperienceSerializer[];

  constructor(partial: Partial<ProfessionProfileSerializer>) {
    Object.assign(this, partial);
  }
}

@Exclude()
export class JobApplicationDetailsSerializer {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  appliedDate: Date;

  @Expose()
  @ApiProperty({ enum: JobApplicationStatus })
  status: JobApplicationStatus;

  @Expose()
  @ApiProperty()
  conversationId: string;

  @Expose()
  @ApiProperty({ type: CandidateDetailsSerializer })
  @Type(() => CandidateDetailsSerializer)
  candidate: CandidateDetailsSerializer;

  @Expose()
  @ApiProperty({ type: JobDetailsSerializer })
  @Type(() => JobDetailsSerializer)
  jobDetails: JobDetailsSerializer;

  @Expose()
  @ApiProperty({ type: [QuestionAnswerSerializer] })
  @Type(() => QuestionAnswerSerializer)
  highlightedQuestions: QuestionAnswerSerializer[];

  @Expose()
  @ApiProperty({ type: AIInsightsSerializer })
  @Type(() => AIInsightsSerializer)
  aiGeneratedInsights: AIInsightsSerializer;

  @Expose()
  @ApiProperty({ type: ProfessionProfileSerializer })
  @Type(() => ProfessionProfileSerializer)
  professionProfile: ProfessionProfileSerializer;

  constructor(partial: Partial<JobApplicationDetailsSerializer>) {
    Object.assign(this, partial);
  }

  static serialize(
    application: JobApplication,
  ): JobApplicationDetailsSerializer {
    const highlightedQuestions = [
      {
        id: 1,
        question: 'Are you above 18 years?',
        answer: 'Yes', // Since we don't store age, default to Yes as it's a job requirement
      },
      {
        id: 2,
        question: 'Tell me about your preferred job location?',
        answer: `${application.candidate?.city || ''}, ${application.candidate?.country || 'Not specified'}`,
      },
      {
        id: 3,
        question:
          'Can you provide an example of when you used leadership in a previous job?',
        answer: 'Not provided', // We don't store this information currently
      },
      {
        id: 4,
        question: 'Please share a brief description about yourself?',
        answer: application.candidate?.address || 'Not provided',
      },
    ].map((q) => new QuestionAnswerSerializer(q));

    const aiGeneratedInsights = new AIInsightsSerializer({
      matchPercentage: application.insights?.[0]?.matchPercentage || 0,
      matchingKeywords: application.insights?.[0]?.matchingKeywords || [],
      summary: application.insights?.[0]?.summary || '',
    });

    const candidate = new CandidateDetailsSerializer({
      id: application.candidate?.id || '',
      candidateId: application.candidate.candidateId || 0,
      name: application.candidate?.name || '',
      email: application.candidate?.email || '',
      phone: application.candidate?.phone || '',
      address: application.candidate?.address || '',
      city: application.candidate?.city || '',
      country: application.candidate?.country || '',
      resumeUrl: application.resumeUrl || '',
    });

    const jobDetails = new JobDetailsSerializer({
      id: application.job?.id || '',
      title: application.job?.title || '',
      description: application.job?.description || '',
      city: application.job?.location?.city || '',
      country: application.job?.location?.country || '',
      status: application.job?.status || '',
    });

    const professionProfile = new ProfessionProfileSerializer({
      keySkills:
        Object.values(application.insights?.[0]?.keySkills || {}).flat() || [],
      yearsOfExperience: String(
        application.insights?.[0]?.yearsOfExperience || '',
      ),
      education: application.insights?.[0]?.education?.[0]?.field || '',
      workExperience: (application.insights?.[0]?.workExperience || []).map(
        (exp) => ({
          role: exp.title || '',
          company: exp.company || '',
          duration: `${exp.startDate} - ${exp.endDate}`,
        }),
      ),
    });

    return new JobApplicationDetailsSerializer({
      id: application.id,
      appliedDate: application.createdAt,
      status: application.status,
      conversationId: application.conversationId,
      candidate,
      jobDetails,
      highlightedQuestions,
      aiGeneratedInsights,
      professionProfile,
    });
  }
}
