import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { JobPost } from '../entities/job-post.entity';
import { JobPostStatus } from '../constants/job.constants';
import { JobPostSection } from '../entities/job-post-section.entity';

@Exclude()
export class PublicJobPostSectionSerializer {
  @Expose()
  @ApiProperty()
  title: string;

  @Expose()
  @ApiProperty()
  content: any;

  @Expose()
  @ApiProperty()
  type: string;

  @Expose()
  @ApiProperty()
  displayOrder: number;

  constructor(partial: Partial<PublicJobPostSectionSerializer>) {
    Object.assign(this, partial);
  }
}

@Exclude()
export class PublicJobPostSerializer {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  title: string;

  @Expose()
  @ApiProperty({ nullable: true })
  description: string | null;

  @Expose()
  @ApiProperty()
  status: JobPostStatus;

  @Expose()
  @ApiProperty()
  publishedAt: Date;

  @Expose()
  @ApiProperty()
  tenantId: string;

  @Expose()
  @ApiProperty({ nullable: true })
  location: {
    city: string;
    name: string;
    address: string;
  } | null;

  @Expose()
  @ApiProperty({ type: [PublicJobPostSectionSerializer] })
  sections: PublicJobPostSectionSerializer[];

  constructor(partial: Partial<PublicJobPostSerializer>) {
    Object.assign(this, partial);
  }

  static serialize(jobPost: JobPost): PublicJobPostSerializer {
    return new PublicJobPostSerializer({
      id: jobPost.id,
      title: jobPost.title,
      description: jobPost.description,
      status: jobPost.status,
      publishedAt: jobPost.publishedAt,
      tenantId: jobPost.tenantId,
      location: jobPost.location
        ? {
            city: jobPost.location.city,
            name: jobPost.location.name,
            address: jobPost.location.address,
          }
        : null,
      sections:
        jobPost.sections?.map((section) => this.serializeSection(section)) ||
        [],
    });
  }

  static serializeSection(
    section: JobPostSection,
  ): PublicJobPostSectionSerializer {
    return new PublicJobPostSectionSerializer({
      title: section.title,
      content: section.content,
      type: section.type,
      displayOrder: section.displayOrder,
    });
  }

  static serializeMany(jobPosts: JobPost[]): PublicJobPostSerializer[] {
    return jobPosts.map((jobPost) => this.serialize(jobPost));
  }
}
