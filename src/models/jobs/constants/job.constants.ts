export const JOB_TEMPLATE_TABLE_NAME = 'job_templates';
export const JOB_TEMPLATE_SECTION_TABLE_NAME = 'job_template_sections';
export const SECTION_DEFINITION_TABLE_NAME = 'section_definitions';
export const JOB_POST_TABLE_NAME = 'job_posts';
export const JOB_POST_SECTION_TABLE_NAME = 'job_post_sections';
export const JOB_POST_CONVERSATION_TABLE_NAME = 'job_post_conversations';
export const JOB_POST_MESSAGES_TABLE_NAME = 'job_post_messages';
export const JOB_APPLICATION_TABLE_NAME = 'job_applications';

export const JOB_TEMPLATE_VALIDATION = {
  NAME_MIN_LENGTH: 3,
  NAME_MAX_LENGTH: 255,
};

export enum JobPostStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

export enum QuestionTypeEnum {
  TEXT_INPUT = 'TEXT_INPUT',
  YES_NO = 'YES_NO',
  MULTI_SELECT = 'MULTI_SELECT',
  SINGLE_SELECT = 'SINGLE_SELECT',
  FILE_UPLOAD = 'FILE_UPLOAD',
  SCHEDULER = 'SCHEDULER',
  VIDEO_INTERVIEW = 'VIDEO_INTERVIEW',
  STATIC_MESSAGE = 'STATIC_MESSAGE',
}

export enum OutcomeTypeEnum {
  REJECT = 'REJECT',
  PROCEED_TO_REVIEW = 'PROCEED_TO_REVIEW',
  THANK_YOU_COMPLETE = 'THANK_YOU_COMPLETE',
  SCHEDULE_INTERVIEW_REQUESTED = 'SCHEDULE_INTERVIEW_REQUESTED',
}

export enum JobPostSortColumn {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  TITLE = 'title',
}

export enum JobPostSortBy {
  ASC = 'ASC',
  DESC = 'DESC',
}
