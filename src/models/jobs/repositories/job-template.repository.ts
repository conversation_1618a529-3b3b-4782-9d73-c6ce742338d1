import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobTemplate } from '../entities/job-template.entity';
import { TenantAwareRepository } from '../../../modules/tenant/tenant-aware.repository';

@Injectable()
export class JobTemplateRepository extends TenantAwareRepository<JobTemplate> {
  constructor(
    @InjectRepository(JobTemplate)
    protected readonly repository: Repository<JobTemplate>,
  ) {
    super();
  }

  async findAllWithSections(): Promise<JobTemplate[]> {
    return this.repository
      .createQueryBuilder('job_template')
      .leftJoinAndSelect('job_template.sections', 'sections')
      .leftJoinAndSelect('sections.sectionDefinition', 'sectionDefinition')
      .orderBy('job_template.templateName', 'ASC')
      .addOrderBy('sections.displayOrder', 'ASC')
      .getMany();
  }

  async findByIdWithSections(id: string): Promise<JobTemplate> {
    return this.repository
      .createQueryBuilder('job_template')
      .leftJoinAndSelect('job_template.sections', 'sections')
      .leftJoinAndSelect('sections.sectionDefinition', 'sectionDefinition')
      .where('job_template.id = :id', { id })
      .orderBy('sections.displayOrder', 'ASC')
      .getOne();
  }

  async create(jobTemplate: Partial<JobTemplate>): Promise<JobTemplate> {
    const newJobTemplate = this.repository.create(jobTemplate);
    return this.repository.save(newJobTemplate);
  }

  async update(
    id: string,
    jobTemplate: Partial<JobTemplate>,
  ): Promise<JobTemplate> {
    await this.repository.update(id, jobTemplate);
    return this.findByIdWithSections(id);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  createQueryBuilderWithSections() {
    return this.repository
      .createQueryBuilder('template')
      .leftJoinAndSelect('template.sections', 'sections')
      .leftJoinAndSelect('sections.sectionDefinition', 'sectionDefinition');
  }

  async findById(id: string): Promise<JobTemplate> {
    return this.repository.findOne({ where: { id } });
  }
}
