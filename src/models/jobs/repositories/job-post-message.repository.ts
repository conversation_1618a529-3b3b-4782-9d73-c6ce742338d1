import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Not, Repository } from 'typeorm';
import { JobPostMessage } from '../entities/job-post-message.entity';

@Injectable()
export class JobPostMessageRepository {
  constructor(
    @InjectRepository(JobPostMessage)
    private readonly repository: Repository<JobPostMessage>,
  ) {}

  async createJobPostMessage(
    data: Partial<JobPostMessage>,
  ): Promise<JobPostMessage> {
    const jobPostMessage = this.repository.create({
      ...data,
    });
    return await this.repository.save(jobPostMessage);
  }

  async findAllBySessionId(sessionId: string): Promise<JobPostMessage[]> {
    return await this.repository.find({ where: { sessionId } });
  }

  async findOutcomesBySessionId(sessionId: string): Promise<JobPostMessage[]> {
    return await this.repository.find({
      where: { sessionId, outcome: Not(IsNull()) },
    });
  }
}
