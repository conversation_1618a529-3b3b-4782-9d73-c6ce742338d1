import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { JobApplication } from '../entities/job-application.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { JobApplicationQueryDto } from '../../../modules/jobs/job-applications/dto/get-job-applications.dto';
import { JobApplicationStatus } from '../entities/job-application.entity';
import { JobApplicationStatsDto } from '../../../modules/jobs/job-applications/dto/job-application-stats.dto';
import { SETTINGS } from '../../../common/constants/settings';

@Injectable()
export class JobApplicationRepository {
  constructor(
    @InjectRepository(JobApplication)
    private readonly repository: Repository<JobApplication>,
  ) {}

  /**
   * Create a new job application
   * @param data - The data to create the job application with
   * @returns The created job application
   */
  async createJobApplication(
    data: Partial<JobApplication>,
  ): Promise<JobApplication> {
    const jobApplication = this.repository.create({
      ...data,
    });
    return await this.repository.save(jobApplication);
  }

  /**
   * Find a job application by job ID
   * @param jobId - The ID of the job
   * @returns The job application
   */
  async findByJobId(jobId: string): Promise<JobApplication[]> {
    return this.repository.find({
      where: { jobId },
      relations: ['candidate', 'job'],
    });
  }

  /**
   * Find a job application by organisation ID
   * @param organisationId - The ID of the organisation
   * @returns The job application
   */
  async findByOrganisationId(
    organisationId: string,
  ): Promise<JobApplication[]> {
    return this.repository.find({
      where: { organisationId },
      relations: ['candidate', 'job'],
    });
  }

  /**
   * Find a job application by candidate ID
   * @param candidateId - The ID of the candidate
   * @returns The job application
   */
  async findByCandidateId(candidateId: string): Promise<JobApplication[]> {
    return this.repository.find({
      where: { candidateId },
      relations: ['job', 'organisation'],
    });
  }

  /**
   * Update a job application by its ID
   * @param id - The ID of the job application to update
   * @param data - The data to update the job application with
   * @returns The updated job application
   */
  async updateById(
    id: string,
    data: Partial<JobApplication>,
  ): Promise<JobApplication> {
    await this.repository.update(id, data);
    return this.repository.findOne({ where: { id } });
  }

  async findJobApplications(
    query: JobApplicationQueryDto,
  ): Promise<[JobApplication[], number]> {
    const queryBuilder = this.repository
      .createQueryBuilder('jobApplication')
      .select([
        'jobApplication.id',
        'jobApplication.candidateId',
        'jobApplication.jobId',
        'jobApplication.status',
        'jobApplication.createdAt',
        'candidate.candidateId',
        'candidate.name',
        'candidate.email',
        'job.id',
        'job.title',
        'location.city',
      ])
      .leftJoin('jobApplication.candidate', 'candidate')
      .leftJoin('jobApplication.job', 'job')
      .leftJoin('job.location', 'location');

    // Apply search filter
    if (query.search) {
      queryBuilder.andWhere(
        '(LOWER(candidate.name) LIKE LOWER(:search) OR ' +
          'LOWER(job.title) LIKE LOWER(:search) OR ' +
          'LOWER(location.city) LIKE LOWER(:search) OR ' +
          'LOWER(candidate.email) LIKE LOWER(:search))',
        { search: `%${query.search}%` },
      );
    }

    // Apply date range filter
    if (query.startDate && query.endDate) {
      queryBuilder.andWhere(
        'jobApplication.createdAt BETWEEN :startDate AND :endDate',
        {
          startDate: query.startDate,
          endDate: query.endDate,
        },
      );
    }

    // Apply status filter
    if (query.status) {
      queryBuilder.andWhere('jobApplication.status = :status', {
        status: query.status,
      });
    }

    // Apply sorting based on the field
    switch (query.sortBy) {
      case 'appliedDate':
        queryBuilder.orderBy('jobApplication.createdAt', query.sortOrder);
        break;
      case 'status':
        queryBuilder.orderBy('jobApplication.status', query.sortOrder);
        break;
      default:
        queryBuilder.orderBy('jobApplication.createdAt', 'DESC');
    }

    // Apply pagination
    const skip = (query.page - 1) * query.limit;
    queryBuilder.skip(skip).take(query.limit);

    return await queryBuilder.getManyAndCount();
  }

  async findById(id: string): Promise<JobApplication | null> {
    return this.repository.findOne({ where: { id } });
  }

  async findByIdWithRelations(id: string): Promise<JobApplication | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['candidate', 'job', 'job.location', 'insights'],
    });
  }

  async getApplicationStatsByOrganisation(
    organisationId: string,
  ): Promise<JobApplicationStatsDto[]> {
    // Get counts for existing statuses
    const stats = await this.repository
      .createQueryBuilder('jobApplication')
      .select('jobApplication.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('jobApplication.organisationId = :organisationId', {
        organisationId,
      })
      .groupBy('jobApplication.status')
      .getRawMany();

    // Create a map of existing status counts
    const statusCountMap = new Map(
      stats.map((stat) => [stat.status, parseInt(stat.count)]),
    );

    // Create result array with all statuses, defaulting to 0 for missing ones
    const result = Object.values(JobApplicationStatus).map((status) => ({
      status,
      name: SETTINGS.JOB_APPLICATION_STATUS_NAMES[status],
      count: statusCountMap.get(status) || 0,
    }));

    return result;
  }
}
