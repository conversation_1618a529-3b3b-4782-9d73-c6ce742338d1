import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SectionDefinition } from '../entities/section-definition.entity';
import { TenantAwareRepository } from '../../../modules/tenant/tenant-aware.repository';

@Injectable()
export class SectionDefinitionRepository extends TenantAwareRepository<SectionDefinition> {
  constructor(
    @InjectRepository(SectionDefinition)
    protected readonly repository: Repository<SectionDefinition>,
  ) {
    super();
  }

  async findAll(): Promise<SectionDefinition[]> {
    // This will automatically filter by current tenant and include system definitions (null tenant)
    return this.createQueryBuilder('section_definition')
      .where('section_definition.tenant_id IS NULL')
      .orWhere('section_definition.tenant_id = :tenantId')
      .orderBy('section_definition.default_name', 'ASC')
      .getMany();
  }

  async findById(id: string): Promise<SectionDefinition> {
    // For a specific ID lookup, we can use findOne with automatic tenant filtering
    return this.findOne({ where: { id } });
  }

  async create(
    sectionDefinition: Partial<SectionDefinition>,
  ): Promise<SectionDefinition> {
    const newSectionDefinition = this.repository.create(sectionDefinition);
    return this.repository.save(newSectionDefinition);
  }

  async update(
    id: string,
    sectionDefinition: Partial<SectionDefinition>,
  ): Promise<SectionDefinition> {
    await this.repository.update(id, sectionDefinition);
    return this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  async findByDefaultName(name: string): Promise<SectionDefinition> {
    return this.repository.findOne({
      where: { defaultName: name },
    });
  }
}
