import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { JobApplicationInsights } from '../entities/job-application-insights.entity';
import { IJobApplicationInsights } from '../interfaces/job-application-insights.interface';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class JobApplicationInsightsRepository extends Repository<JobApplicationInsights> {
  constructor(
    @InjectRepository(JobApplicationInsights)
    private readonly repository: Repository<JobApplicationInsights>,
  ) {
    super(JobApplicationInsights, repository.manager);
  }

  async createInsights(
    data: Partial<IJobApplicationInsights>,
  ): Promise<JobApplicationInsights> {
    const insights = this.create(data);
    return this.save(insights);
  }

  async updateInsights(
    id: string,
    data: Partial<IJobApplicationInsights>,
  ): Promise<JobApplicationInsights> {
    await this.update(id, data);
    return this.findOne({ where: { id } });
  }

  async findByJobApplicationId(
    jobApplicationId: string,
  ): Promise<JobApplicationInsights> {
    return this.findOne({ where: { jobApplicationId } });
  }

  async updateInsightsByJobApplicationId(
    jobApplicationId: string,
    data: Partial<IJobApplicationInsights>,
  ): Promise<JobApplicationInsights> {
    await this.update({ jobApplicationId }, data);
    return this.findOne({ where: { jobApplicationId } });
  }
}
