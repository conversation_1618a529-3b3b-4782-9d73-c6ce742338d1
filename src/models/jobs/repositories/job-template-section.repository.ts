import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobTemplateSection } from '../entities/job-template-section.entity';

@Injectable()
export class JobTemplateSectionRepository {
  constructor(
    @InjectRepository(JobTemplateSection)
    private readonly repository: Repository<JobTemplateSection>,
  ) {}

  async findByTemplateId(templateId: string): Promise<JobTemplateSection[]> {
    return this.repository.find({
      where: { templateId },
      order: { displayOrder: 'ASC' },
      relations: ['sectionDefinition'],
    });
  }

  async findById(id: string): Promise<JobTemplateSection> {
    return this.repository.findOne({
      where: { id },
      relations: ['sectionDefinition', 'parentSection'],
    });
  }

  async create(
    section: Partial<JobTemplateSection>,
  ): Promise<JobTemplateSection> {
    const newSection = this.repository.create(section);
    return this.repository.save(newSection);
  }

  async createMany(
    sections: Partial<JobTemplateSection>[],
  ): Promise<JobTemplateSection[]> {
    const newSections = this.repository.create(sections);
    return this.repository.save(newSections);
  }

  async update(
    id: string,
    section: Partial<JobTemplateSection>,
  ): Promise<JobTemplateSection> {
    await this.repository.update(id, section);
    return this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  async deleteByTemplateId(templateId: string): Promise<void> {
    await this.repository.delete({ templateId });
  }
}
