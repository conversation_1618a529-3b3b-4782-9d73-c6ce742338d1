import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobPostSection } from '../entities/job-post-section.entity';

@Injectable()
export class JobPostSectionRepository {
  constructor(
    @InjectRepository(JobPostSection)
    private readonly repository: Repository<JobPostSection>,
  ) {}

  async createMany(
    sections: Partial<JobPostSection>[],
  ): Promise<JobPostSection[]> {
    const newSections = this.repository.create(sections);
    return this.repository.save(newSections);
  }

  async deleteByJobPostId(jobPostId: string): Promise<void> {
    await this.repository.delete({ jobPostId });
  }

  async findByJobPostId(jobPostId: string): Promise<JobPostSection[]> {
    return this.repository.find({
      where: { jobPostId },
      order: { displayOrder: 'ASC' },
      relations: ['sectionDefinition'],
    });
  }

  async update(
    id: string,
    section: Partial<JobPostSection>,
  ): Promise<JobPostSection> {
    await this.repository.update(id, section);
    return this.repository.findOne({ where: { id } });
  }

  async create(section: Partial<JobPostSection>): Promise<JobPostSection> {
    const newSection = this.repository.create(section);
    return this.repository.save(newSection);
  }

  async findById(id: string): Promise<JobPostSection> {
    return this.repository.findOne({ where: { id } });
  }
}
