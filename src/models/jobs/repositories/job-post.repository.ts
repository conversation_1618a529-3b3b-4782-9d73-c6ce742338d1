import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobPost } from '../entities/job-post.entity';
import { TenantAwareRepository } from '../../../modules/tenant/tenant-aware.repository';
import { JobPostStatus } from '../constants/job.constants';
import { JobPostSortColumn } from '../constants/job.constants';
import { JobPostSortBy } from '../constants/job.constants';

@Injectable()
export class JobPostRepository extends TenantAwareRepository<JobPost> {
  constructor(
    @InjectRepository(JobPost)
    protected readonly repository: Repository<JobPost>,
  ) {
    super();
  }

  async findAllWithSections(): Promise<JobPost[]> {
    return this.repository
      .createQueryBuilder('job_post')
      .leftJoinAndSelect('job_post.sections', 'sections')
      .leftJoinAndSelect('sections.sectionDefinition', 'sectionDefinition')
      .orderBy('job_post.createdAt', 'DESC')
      .addOrderBy('sections.displayOrder', 'ASC')
      .getMany();
  }

  async findAllWithRelations(
    page: number = 1,
    limit: number = 10,
  ): Promise<[JobPost[], number]> {
    const query = this.repository
      .createQueryBuilder('job_post')
      .leftJoinAndSelect('job_post.createdBy', 'createdBy')
      .leftJoinAndSelect('job_post.location', 'location')
      .orderBy('job_post.createdAt', 'DESC');

    const [items, total] = await query
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return [items, total];
  }

  async findAllWithSearchRelations(
    page = 1,
    limit = 10,
    search?: string,
    startDate?: string,
    endDate?: string,
    sortOrder?: JobPostSortBy,
    sortColumn?: JobPostSortColumn,
    status?: JobPostStatus,
    isActive?: boolean,
  ): Promise<[JobPost[], number]> {
    const query = this.repository
      .createQueryBuilder('job_post')
      .leftJoinAndSelect('job_post.createdBy', 'createdBy')
      .leftJoinAndSelect('job_post.location', 'location');

    if (search) {
      query.andWhere(
        '(job_post.title ILIKE :search OR createdBy.fullName ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.andWhere('job_post.createdAt >= :start', { start });
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.andWhere('job_post.createdAt <= :end', { end });
    }

    if (status) {
      query.andWhere('job_post.status = :status', { status });
    }

    if (isActive !== undefined) {
      query.andWhere('job_post.isActive = :isActive', { isActive });
    }

    if (sortColumn && sortOrder) {
      query.orderBy(`job_post.${sortColumn}`, sortOrder);
    } else {
      query.orderBy('job_post.createdAt', 'DESC');
    }

    const [items, total] = await query
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return [items, total];
  }

  async findByIdWithSections(id: string): Promise<JobPost> {
    return this.repository
      .createQueryBuilder('job_post')
      .leftJoinAndSelect('job_post.sections', 'sections')
      .leftJoinAndSelect('sections.sectionDefinition', 'sectionDefinition')
      .leftJoinAndSelect('job_post.createdBy', 'createdBy')
      .leftJoinAndSelect('job_post.location', 'location')
      .where('job_post.id = :id', { id })
      .orderBy('sections.displayOrder', 'ASC')
      .getOne();
  }

  async findByLocationId(locationId: string): Promise<JobPost | null> {
    return this.repository
      .createQueryBuilder('job_post')
      .where('job_post.location_id = :locationId', { locationId })
      .getOne();
  }

  async create(jobPost: Partial<JobPost>): Promise<JobPost> {
    const newJobPost = this.repository.create(jobPost);
    return this.repository.save(newJobPost);
  }

  async update(id: string, jobPost: Partial<JobPost>): Promise<JobPost> {
    await this.repository.update(id, jobPost);
    return this.findByIdWithSections(id);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  createQueryBuilderWithSections() {
    return this.repository
      .createQueryBuilder('post')
      .leftJoinAndSelect('post.sections', 'sections')
      .leftJoinAndSelect('sections.sectionDefinition', 'sectionDefinition');
  }

  async findPublicJobPosts(
    page: number = 1,
    limit: number = 50,
    organisationId?: string,
  ): Promise<[JobPost[], number]> {
    const query = this.repository
      .createQueryBuilder('job_post')
      .leftJoinAndSelect('job_post.sections', 'sections')
      .leftJoinAndSelect('job_post.location', 'location')
      .where('job_post.status = :status', { status: JobPostStatus.PUBLISHED })
      .orderBy('job_post.publishedAt', 'DESC')
      .addOrderBy('sections.displayOrder', 'ASC')
      .skip((page - 1) * limit)
      .take(limit);

    if (organisationId) {
      query.andWhere('job_post.tenantId = :organisationId', { organisationId });
    }

    return query.getManyAndCount();
  }

  async findPublicJobPostById(id: string, tenantId: string): Promise<JobPost> {
    return this.repository
      .createQueryBuilder('job_post')
      .leftJoinAndSelect('job_post.sections', 'sections')
      .leftJoinAndSelect('job_post.location', 'location')
      .where('job_post.id = :id', { id })
      .andWhere('job_post.tenantId = :tenantId', { tenantId })
      .andWhere('job_post.status = :status', {
        status: JobPostStatus.PUBLISHED,
      })
      .orderBy('sections.displayOrder', 'ASC')
      .getOne();
  }

  async searchJobsByTitleAndLocation(
    tenantId: string,
    title?: string,
    location?: string,
  ): Promise<JobPost[]> {
    const queryBuilder = this.createQueryBuilder('jobPost')
      .leftJoinAndSelect('jobPost.location', 'location')
      .leftJoinAndSelect('jobPost.sections', 'sections')
      .leftJoinAndSelect('sections.sectionDefinition', 'sectionDefinition')
      .where('jobPost.tenantId = :tenantId', { tenantId })
      .andWhere('jobPost.status = :status', { status: JobPostStatus.PUBLISHED })
      .andWhere('jobPost.isActive = :isActive', { isActive: true });

    if (title) {
      queryBuilder.andWhere('LOWER(jobPost.title) LIKE LOWER(:title)', {
        title: `%${title}%`,
      });
    }

    if (location) {
      queryBuilder.andWhere(
        '(LOWER(location.name) LIKE LOWER(:location) OR LOWER(location.city) LIKE LOWER(:location) OR LOWER(location.address) LIKE LOWER(:location))',
        { location: `%${location}%` },
      );
    }

    return queryBuilder
      .orderBy('jobPost.publishedAt', 'DESC')
      .addOrderBy('sections.displayOrder', 'ASC')
      .getMany();
  }
}
