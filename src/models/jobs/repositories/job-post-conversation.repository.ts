import { Injectable } from '@nestjs/common';
import { DataSource, Repository, UpdateResult } from 'typeorm';
import { JobPostConversation } from '../entities/job-post-conversation.entity';

@Injectable()
export class JobPostConversationRepository extends Repository<JobPostConversation> {
  constructor(private dataSource: DataSource) {
    super(JobPostConversation, dataSource.createEntityManager());
  }

  async findBySessionId(
    sessionId: string,
  ): Promise<JobPostConversation | null> {
    console.log('sessionId :', sessionId);
    return this.findOne({
      where: { id: sessionId },
    });
  }

  async createConversation(
    data: Partial<JobPostConversation>,
  ): Promise<JobPostConversation> {
    const conversation = this.create(data);
    return await this.save(conversation);
  }

  async updateConversation(
    id: string,
    data: Partial<JobPostConversation>,
  ): Promise<UpdateResult> {
    return await this.createQueryBuilder()
      .update(JobPostConversation)
      .set(data)
      .where('id = :id', { id })
      .execute();
  }
}
