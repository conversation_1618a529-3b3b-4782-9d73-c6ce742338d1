import { ApiProperty } from '@nestjs/swagger';

export class JobTemplateResponseDto {
  @ApiProperty({
    description: 'The unique identifier of the job template',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the job template',
    example: 'Crew Member',
  })
  templateName: string;

  @ApiProperty({
    description: 'The description of the job template',
    example: 'Template for hiring crew members in the restaurant',
    required: false,
  })
  description: string;
}
