import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { JobPost } from './job-post.entity';
import { FlowQuestion } from './flow-question.entity';
import { FlowOutcome } from './flow-outcome.entity';

@Entity({ name: 'conversational_flows' })
export class ConversationalFlow {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('text', { nullable: true })
  genesis: string;

  @Column({ type: 'uuid', nullable: true })
  job_post_id: string;

  @ManyToOne(() => JobPost, (jobPost) => jobPost.conversationalFlows, {
    onDelete: 'SET NULL',
    nullable: true,
  })
  @JoinColumn({ name: 'job_post_id' })
  jobPost: JobPost;

  @Column({ type: 'int', nullable: true })
  start_question_id: number;

  @OneToMany(() => FlowQuestion, (question) => question.flow)
  questions: FlowQuestion[];

  @OneToMany(() => FlowOutcome, (outcome) => outcome.flow)
  outcomes: FlowOutcome[];

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updated_at: Date;
}
