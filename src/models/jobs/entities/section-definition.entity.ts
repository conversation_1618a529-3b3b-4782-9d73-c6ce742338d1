import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  Entity,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SECTION_DEFINITION_TABLE_NAME } from '../constants/job.constants';
import { Organisation } from '../../organisations/entities/organisation.entity';
import { JobTemplateSection } from './job-template-section.entity';

@Entity(SECTION_DEFINITION_TABLE_NAME)
export class SectionDefinition {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'default_name', type: 'varchar', length: 255 })
  defaultName: string;

  @Column({ name: 'is_system_defined', type: 'boolean', default: true })
  isSystemDefined: boolean;

  @Column({ name: 'tenant_id', type: 'uuid', nullable: true })
  tenantId: string;

  @Column({ name: 'default_values', type: 'jsonb', nullable: true })
  defaultValues: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => Organisation, { nullable: true })
  @JoinColumn({ name: 'tenant_id' })
  tenant: Organisation;

  @OneToMany(() => JobTemplateSection, (section) => section.sectionDefinition)
  jobTemplateSections: JobTemplateSection[];
}
