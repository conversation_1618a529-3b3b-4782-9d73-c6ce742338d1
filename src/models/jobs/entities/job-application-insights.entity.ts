import {
  <PERSON>umn,
  CreateDateC<PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { JobApplication } from './job-application.entity';

export enum JobApplicationInsightsStatus {
  PENDING = 0,
  PROCESSING = 1,
  COMPLETED = 2,
  FAILED = 3,
}

@Entity('job_application_insights')
export class JobApplicationInsights {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'job_application_id', type: 'uuid', unique: true })
  jobApplicationId: string;

  @Column({ name: 'match_percentage', type: 'integer', nullable: true })
  matchPercentage: number;

  @Column({ name: 'matching_keywords', type: 'jsonb', nullable: true })
  matchingKeywords: string[];

  @Column({ name: 'summary', type: 'text', nullable: true })
  summary: string;

  @Column({
    name: 'status',
    type: 'smallint',
    default: JobApplicationInsightsStatus.PENDING,
    comment: '0: pending, 1: processing, 2: completed, 3: failed',
  })
  status: JobApplicationInsightsStatus;

  @Column({ name: 'generated_at', type: 'timestamp', nullable: true })
  generatedAt: Date;

  @Column({ name: 'key_skills', type: 'jsonb', nullable: true })
  keySkills: {
    technical: string[];
    soft: string[];
    languages: string[];
    certifications: string[];
    tools: string[];
  };

  @Column({ name: 'years_of_experience', type: 'integer', nullable: true })
  yearsOfExperience: number;

  @Column({ name: 'education', type: 'jsonb', nullable: true })
  education: {
    level: string;
    field: string;
    institution: string;
    location?: string;
    graduationYear?: number;
    gpa?: number;
    honors?: string[];
    major?: string;
    minor?: string;
    coursework?: string[];
  }[];

  @Column({ name: 'work_experience', type: 'jsonb', nullable: true })
  workExperience: {
    title: string;
    company: string;
    location?: string;
    industry?: string;
    startDate: string;
    endDate: string;
    isCurrent: boolean;
    description?: string;
    achievements?: string[];
    skills_used?: string[];
    projects?: {
      name: string;
      description: string;
      technologies?: string[];
      url?: string;
    }[];
  }[];

  @Column({ name: 'professional_summary', type: 'jsonb', nullable: true })
  professionalSummary: {
    brief: string;
    highlights: string[];
    expertiseLevel?: string;
    industryFocus?: string[];
    careerHighlights?: string[];
  };

  @Column({ name: 'profile_generated_at', type: 'timestamp', nullable: true })
  profileGeneratedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => JobApplication, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'job_application_id' })
  jobApplication: JobApplication;
}
