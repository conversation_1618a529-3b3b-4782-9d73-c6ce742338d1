import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, Join<PERSON><PERSON>umn } from 'typeorm';
import { FlowQuestion } from './flow-question.entity';
import { FlowOutcome } from './flow-outcome.entity';

@Entity({ name: 'flow_conditions' })
export class FlowCondition {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  source_question_id: number;

  @ManyToOne(() => FlowQuestion, (question) => question.source_conditions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'source_question_id' })
  sourceQuestion: FlowQuestion;

  @Column({ length: 255 })
  trigger_answer_value: string;

  @Column({ type: 'int', nullable: true })
  next_question_id: number;

  @ManyToOne(() => FlowQuestion, (question) => question.next_in_conditions, { onDelete: 'SET NULL', nullable: true, eager: true })
  @JoinColumn({ name: 'next_question_id' })
  nextQuestion: FlowQuestion;

  @Column({ type: 'int', nullable: true })
  next_outcome_id: number;

  @ManyToOne(() => FlowOutcome, (outcome) => outcome.conditions_leading_here, { onDelete: 'SET NULL', nullable: true, eager: true })
  @JoinColumn({ name: 'next_outcome_id' })
  nextOutcome: FlowOutcome;

  @Column({ type: 'int', default: 0 })
  priority: number;
} 