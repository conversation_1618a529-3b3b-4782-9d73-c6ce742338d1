import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { JobPostMessage } from './job-post-message.entity';

export type JobPostConversationStatus =
  | 'in_progress'
  | 'completed'
  | 'abandoned';

@Entity('job_post_conversations')
export class JobPostConversation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Column({
    type: 'enum',
    enum: ['in_progress', 'completed', 'abandoned'],
    default: 'in_progress',
  })
  status: JobPostConversationStatus;

  @Column({ name: 'current_step', type: 'varchar', nullable: true })
  currentStep?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @OneToMany(() => JobPostMessage, (message) => message.conversation)
  messages: JobPostMessage[];
}
