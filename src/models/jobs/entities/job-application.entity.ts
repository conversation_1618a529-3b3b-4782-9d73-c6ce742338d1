import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  OneToMany,
} from 'typeorm';
import { JobPost } from './job-post.entity';
import { Organisation } from '../../organisations/entities/organisation.entity';
import { Candidate } from '../../candidates/entities/candidate.entity';
import { JOB_APPLICATION_TABLE_NAME } from '../constants/job.constants';
import { JobApplicationInsights } from './job-application-insights.entity';

export enum JobApplicationStatus {
  APPLIED = 'applied',
  INCOMPLETE = 'incomplete',
  INTERVIEW_SCHEDULED = 'interview_scheduled',
  SHORTLISTED = 'shortlisted',
  REJECTED = 'rejected',
  HIRED = 'hired',
}

@Entity(JOB_APPLICATION_TABLE_NAME)
export class JobApplication {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', name: 'candidate_id' })
  candidateId: string;

  @Column({ type: 'uuid', name: 'job_id' })
  jobId: string;

  @Column({ type: 'text', nullable: true, name: 'resume_url' })
  resumeUrl: string;

  @Column({
    type: 'enum',
    enum: JobApplicationStatus,
    default: JobApplicationStatus.INCOMPLETE,
  })
  status: JobApplicationStatus;

  @Column({ type: 'uuid', nullable: true, name: 'organisation_id' })
  organisationId: string;

  @Column({ type: 'uuid', nullable: true, name: 'conversation_id' })
  conversationId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => JobPost, (jobPost) => jobPost.applications)
  @JoinColumn({ name: 'job_id' })
  job: JobPost;

  @ManyToOne(() => Organisation)
  @JoinColumn({ name: 'organisation_id' })
  organisation: Organisation;

  @ManyToOne(() => Candidate)
  @JoinColumn({ name: 'candidate_id' })
  candidate: Candidate;

  @OneToMany(
    () => JobApplicationInsights,
    (insights) => insights.jobApplication,
  )
  insights: JobApplicationInsights[];
}
