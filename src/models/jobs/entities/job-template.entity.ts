import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Organisation } from '../../organisations/entities/organisation.entity';
import { JobTemplateSection } from './job-template-section.entity';
import { JOB_TEMPLATE_TABLE_NAME } from '../constants/job.constants';

@Entity(JOB_TEMPLATE_TABLE_NAME)
export class JobTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'template_name', type: 'varchar', length: 255 })
  templateName: string;

  @Column({ name: 'tenant_id', type: 'uuid', nullable: true })
  tenantId: string;

  @Column({ name: 'is_system_defined', type: 'boolean', default: false })
  isSystemDefined: boolean;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @ManyToOne(() => Organisation, { nullable: true })
  @JoinColumn({ name: 'tenant_id' })
  tenant: Organisation;

  @OneToMany(() => JobTemplateSection, (section) => section.template, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  sections: JobTemplateSection[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
