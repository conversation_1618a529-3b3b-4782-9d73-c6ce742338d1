import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SectionDefinition } from './section-definition.entity';
import { JobTemplateSection } from './job-template-section.entity';
import { JobPost } from './job-post.entity';
import { JOB_POST_SECTION_TABLE_NAME } from '../constants/job.constants';

export enum JobPostSectionType {
  DEFAULT = 'default',
  JOB_DETAILS = 'job_details',
  QUALIFICATIONS = 'qualifications',
  RESPONSIBILITIES = 'responsibilities',
  BENEFITS = 'benefits',
  JOB_DESCRIPTION = 'job_description',
  HIGHLIGHTS = 'job_highlights',
}

@Entity(JOB_POST_SECTION_TABLE_NAME)
export class JobPostSection {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'job_post_id', type: 'uuid' })
  jobPostId: string;

  @Column({ name: 'section_definition_id', type: 'uuid' })
  sectionDefinitionId: string;

  @Column({
    name: 'original_template_section_id',
    type: 'uuid',
    nullable: true,
  })
  originalTemplateSectionId: string;

  @Column({ name: 'title', type: 'varchar', length: 255 })
  title: string;

  @Column({ name: 'content', type: 'jsonb', nullable: true })
  content: any;

  @Column({ name: 'display_order', type: 'int' })
  displayOrder: number;

  @Column({ name: 'is_visible', type: 'boolean', default: true })
  isVisible: boolean;

  @Column({ name: 'is_editable', type: 'boolean', default: true })
  isEditable: boolean;

  @Column({ name: 'is_deletable', type: 'boolean', default: true })
  isDeletable: boolean;

  @Column({
    name: 'type',
    type: 'varchar',
    length: 50,
    default: JobPostSectionType.DEFAULT,
  })
  type: JobPostSectionType;

  @ManyToOne(() => JobPost)
  @JoinColumn({ name: 'job_post_id' })
  jobPost: JobPost;

  @ManyToOne(() => SectionDefinition)
  @JoinColumn({ name: 'section_definition_id' })
  sectionDefinition: SectionDefinition;

  @ManyToOne(() => JobTemplateSection, { nullable: true })
  @JoinColumn({ name: 'original_template_section_id' })
  originalTemplateSection: JobTemplateSection;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
