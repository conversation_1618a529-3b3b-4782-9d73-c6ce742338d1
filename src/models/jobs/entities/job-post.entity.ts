import {
  <PERSON>umn,
  <PERSON>reateDateColumn,
  Entity,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Organisation } from '../../organisations/entities/organisation.entity';
import { JobTemplate } from './job-template.entity';
import { JOB_POST_TABLE_NAME, JobPostStatus } from '../constants/job.constants';
import { User } from '../../users/entities/user.entity';
import { Location } from '../../locations/entities/location.entity';
import { JobPostSection } from './job-post-section.entity';
import { TenantEntityBase } from '@hirenetix/models/shared/entities/tenant-entity.base';
import { ConversationalFlow } from './conversational-flow.entity';
import { JobApplication } from './job-application.entity';

@Entity(JOB_POST_TABLE_NAME)
export class JobPost extends TenantEntityBase {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'original_template_id', type: 'uuid', nullable: true })
  originalTemplateId: string;

  @Column({ name: 'title', type: 'varchar', length: 255 })
  title: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: JobPostStatus,
    default: JobPostStatus.DRAFT,
  })
  status: JobPostStatus;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ name: 'published_at', type: 'timestamptz', nullable: true })
  publishedAt: Date;

  @Column({ name: 'tenant_id', type: 'uuid', nullable: true })
  tenantId: string;

  @Column({ name: 'created_by_id', type: 'uuid' })
  createdById: string;

  @Column({ name: 'location_id', type: 'uuid' })
  locationId: string;

  @ManyToOne(() => Organisation, { nullable: true })
  @JoinColumn({ name: 'tenant_id' })
  tenant: Organisation;

  @ManyToOne(() => JobTemplate, { nullable: true })
  @JoinColumn({ name: 'original_template_id' })
  originalTemplate: JobTemplate;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_id' })
  createdBy: User;

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'location_id' })
  location: Location;

  @OneToMany(() => JobPostSection, (section) => section.jobPost, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  sections: JobPostSection[];

  @OneToMany(() => JobApplication, (application) => application.job)
  applications: JobApplication[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @OneToMany(() => ConversationalFlow, (flow) => flow.jobPost)
  conversationalFlows: ConversationalFlow[];
}
