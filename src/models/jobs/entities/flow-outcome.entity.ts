import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { ConversationalFlow } from './conversational-flow.entity';
import { FlowCondition } from './flow-condition.entity';
import { OutcomeTypeEnum } from '../constants/job.constants';

@Entity({ name: 'flow_outcomes' })
export class FlowOutcome {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  flow_id: number;

  @ManyToOne(() => ConversationalFlow, (flow) => flow.outcomes, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'flow_id' })
  flow: ConversationalFlow;

  @Column({
    type: 'enum',
    enum: OutcomeTypeEnum,
  })
  outcome_type: OutcomeTypeEnum;

  @Column('text', { nullable: true })
  message_text: string;

  @Column({ type: 'boolean', default: false })
  is_default_reject: boolean;

  @Column({ type: 'boolean', default: false })
  is_default_success: boolean;

  @OneToMany(() => FlowCondition, (condition) => condition.nextOutcome)
  conditions_leading_here: FlowCondition[];
} 