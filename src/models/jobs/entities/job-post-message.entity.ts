import {
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { JobPostConversation } from './job-post-conversation.entity';
import { JOB_POST_MESSAGES_TABLE_NAME } from '../constants/job.constants';

@Entity(JOB_POST_MESSAGES_TABLE_NAME)
export class JobPostMessage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'session_id' })
  sessionId: string;

  @Column({ name: 'step_id' })
  stepId: string;

  @Column({ name: 'user_response', nullable: true })
  userResponse: string;

  @Column({ name: 'job_agent_response', nullable: true })
  jobAgentResponse: string;

  @Column({ nullable: true })
  outcome: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => JobPostConversation, (conversation) => conversation.messages)
  @JoinColumn({ name: 'session_id' })
  conversation: JobPostConversation;
}
