import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, OneToMany, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ConversationalFlow } from './conversational-flow.entity';
import { FlowCondition } from './flow-condition.entity';
import { QuestionTypeEnum } from '../constants/job.constants';

@Entity({ name: 'flow_questions' })
export class FlowQuestion {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  flow_id: number;

  @ManyToOne(() => ConversationalFlow, (flow) => flow.questions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'flow_id' })
  flow: ConversationalFlow;

  @Column({
    type: 'enum',
    enum: QuestionTypeEnum,
  })
  question_type: QuestionTypeEnum;

  @Column('text')
  heading: string;

  @Column('text', { nullable: true })
  sub_text: string;

  @Column({ length: 255, nullable: true })
  placeholder_text: string;

  @Column('jsonb', { nullable: true })
  options: any; 

  @Column({ type: 'boolean', default: true })
  is_mandatory: boolean;

  @Column({ type: 'int', nullable: true })
  display_order: number;

  @Column({ length: 255, nullable: true })
  allowed_file_types: string;

  @Column({ type: 'int', nullable: true })
  max_file_size_mb: number;

  @Column({ length: 50, nullable: true })
  message_style: string;

  @OneToMany(() => FlowCondition, (condition) => condition.sourceQuestion, { eager: true })
  source_conditions: FlowCondition[];

  @OneToMany(() => FlowCondition, (condition) => condition.nextQuestion)
  next_in_conditions: FlowCondition[];
} 