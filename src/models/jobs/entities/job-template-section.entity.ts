import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SectionDefinition } from './section-definition.entity';
import { JobTemplate } from './job-template.entity';
import { JOB_TEMPLATE_SECTION_TABLE_NAME } from '../constants/job.constants';

export enum JobTemplateSectionType {
  DEFAULT = 'default',
  JOB_DETAILS = 'job_details',
  QUALIFICATIONS = 'qualifications',
  RESPONSIBILITIES = 'responsibilities',
  BENEFITS = 'benefits',
  JOB_DESCRIPTION = 'job_description',
  HIGHLIGHTS = 'job_highlights',
}

@Entity(JOB_TEMPLATE_SECTION_TABLE_NAME)
export class JobTemplateSection {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'template_id', type: 'uuid' })
  templateId: string;

  @Column({ name: 'section_definition_id', type: 'uuid' })
  sectionDefinitionId: string;

  @Column({ name: 'parent_section_id', type: 'uuid', nullable: true })
  parentSectionId: string;

  @Column({ name: 'title', type: 'varchar', length: 255 })
  title: string;

  @Column({ name: 'content', type: 'jsonb', nullable: true })
  content: any;

  @Column({ name: 'display_order', type: 'int' })
  displayOrder: number;

  @Column({ name: 'is_visible_default', type: 'boolean', default: true })
  isVisibleDefault: boolean;

  @Column({ name: 'is_editable', type: 'boolean', default: true })
  isEditable: boolean;

  @Column({ name: 'is_deletable', type: 'boolean', default: true })
  isDeletable: boolean;

  @Column({
    name: 'type',
    type: 'varchar',
    length: 50,
    default: JobTemplateSectionType.DEFAULT,
  })
  type: JobTemplateSectionType;

  @ManyToOne(() => JobTemplate)
  @JoinColumn({ name: 'template_id' })
  template: JobTemplate;

  @ManyToOne(() => SectionDefinition)
  @JoinColumn({ name: 'section_definition_id' })
  sectionDefinition: SectionDefinition;

  @ManyToOne(() => JobTemplateSection)
  @JoinColumn({ name: 'parent_section_id' })
  parentSection: JobTemplateSection;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
