import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Conversation } from '../entities/conversation.entity';
import { TenantAwareRepository } from '../../../modules/tenant/tenant-aware.repository';
import { ConversationStatus } from '../entities/conversation.entity';

@Injectable()
export class ConversationRepository extends TenantAwareRepository<Conversation> {
  constructor(
    @InjectRepository(Conversation)
    protected readonly repository: Repository<Conversation>,
  ) {
    super();
  }

  async findByIdWithMessages(id: string): Promise<Conversation> {
    return this.repository
      .createQueryBuilder('conversation')
      .leftJoinAndSelect('conversation.messages', 'messages')
      .leftJoinAndSelect('conversation.candidate', 'candidate')
      .leftJoinAndSelect('messages.sender', 'sender')
      .leftJoinAndSelect('messages.receiver', 'receiver')
      .where('conversation.id = :id', { id })
      .orderBy('messages.sentAt', 'ASC')
      .getOne();
  }

  async findLastActiveConversation(
    candidateId: string,
  ): Promise<Conversation | null> {
    return this.repository
      .createQueryBuilder('conversation')
      .leftJoinAndSelect('conversation.messages', 'messages')
      .leftJoinAndSelect('conversation.candidate', 'candidate')
      .leftJoinAndSelect('messages.sender', 'sender')
      .leftJoinAndSelect('messages.receiver', 'receiver')
      .where('conversation.candidateId = :candidateId', { candidateId })
      .andWhere('conversation.status = :status', {
        status: ConversationStatus.ACTIVE,
      })
      .orderBy('conversation.createdAt', 'DESC')
      .orderBy('messages.createdAt', 'ASC')
      .getOne();
  }

  async create(conversation: Partial<Conversation>): Promise<Conversation> {
    const newConversation = this.repository.create(conversation);
    return this.repository.save(newConversation);
  }

  async update(id: string, conversation: Partial<Conversation>): Promise<void> {
    await this.repository.update(id, conversation);
  }

  async findByConversationId(
    conversationId: string,
  ): Promise<Conversation | null> {
    return this.repository.findOne({
      where: {
        conversationId,
      },
    });
  }
}
