import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Message } from '../entities/message.entity';
import { TenantAwareRepository } from '../../../modules/tenant/tenant-aware.repository';

@Injectable()
export class MessageRepository extends TenantAwareRepository<Message> {
  constructor(
    @InjectRepository(Message)
    protected readonly repository: Repository<Message>,
  ) {
    super();
  }

  async findByConversationId(conversationId: string): Promise<Message[]> {
    return this.repository
      .createQueryBuilder('message')
      .leftJoinAndSelect('message.sender', 'sender')
      .leftJoinAndSelect('message.receiver', 'receiver')
      .where('message.conversationId = :conversationId', { conversationId })
      .orderBy('message.sentAt', 'ASC')
      .getMany();
  }

  async findMessagesByConversationId(
    conversationId: string,
  ): Promise<Message[]> {
    return this.repository
      .createQueryBuilder('message')
      .where('message.conversationId = :conversationId', { conversationId })
      .orderBy('message.sentAt', 'ASC')
      .getMany();
  }

  async create(message: Partial<Message>): Promise<Message> {
    const newMessage = this.repository.create(message);
    return this.repository.save(newMessage);
  }

  async createMany(messages: Partial<Message>[]): Promise<Message[]> {
    const newMessages = this.repository.create(messages);
    return this.repository.save(newMessages);
  }

  async update(id: string, message: Partial<Message>): Promise<void> {
    await this.repository.update(id, message);
  }

  async findLastMessageByConversationId(
    conversationId: string,
  ): Promise<Message> {
    return this.repository
      .createQueryBuilder('message')
      .where('message.conversationId = :conversationId', { conversationId })
      .orderBy('message.sentAt', 'DESC')
      .limit(1)
      .getOne();
  }
}
