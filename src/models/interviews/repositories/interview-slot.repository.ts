import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { InterviewSlot } from '../entities/interview-slot.entity';

@Injectable()
export class InterviewSlotRepository extends Repository<InterviewSlot> {
  constructor(private dataSource: DataSource) {
    super(InterviewSlot, dataSource.createEntityManager());
  }

  async findAvailableSlots(date?: Date): Promise<InterviewSlot[]> {
    const queryBuilder = this.createQueryBuilder('slot')
      .leftJoinAndSelect('slot.user', 'user')
      .where('slot.is_booked = :isBooked', { isBooked: false });

    if (date) {
      queryBuilder.andWhere('slot.date = :date', { date });
    }

    return queryBuilder.getMany();
  }
}
