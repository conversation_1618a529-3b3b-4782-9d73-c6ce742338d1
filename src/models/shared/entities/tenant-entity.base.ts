import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON> } from 'typeorm';
import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';

/**
 * Base class for all tenant-aware entities
 *
 * Entities that should be scoped to a tenant should extend this class
 * This will automatically add a tenantId column and relationship to the Organisation entity
 */
export abstract class TenantEntityBase {
  @Column({ name: 'tenant_id' })
  tenantId: string;

  @ManyToOne(() => Organisation, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'tenant_id' })
  tenant: Organisation;
}
