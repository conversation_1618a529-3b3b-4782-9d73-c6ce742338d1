export interface IDepartment {
  id: string;
  organisationId: string;
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IDepartmentCreate {
  organisationId: string;
  name: string;
  description?: string;
}

export interface IDepartmentUpdate {
  name?: string;
  description?: string;
}

export interface IDepartmentSearchQuery {
  id?: string;
  organisationId?: string;
  name?: string;
}
