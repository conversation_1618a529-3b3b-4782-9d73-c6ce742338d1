import { Department } from '@hirenetix/models/departments/entities/department.entity';
import { OrganisationSerializer } from '@hirenetix/models/organisations/serializers/organisation.serializer';

export class DepartmentSerializer {
  id: string;
  organisationId: string;
  organisation?: OrganisationSerializer;
  name: string;
  description: string | null;
  createdAt: Date;
  updatedAt: Date;

  constructor(department: Department, includeOrganisation = false) {
    this.id = department.id;
    this.organisationId = department.organisationId;
    this.name = department.name;
    this.description = department.description || null;
    this.createdAt = department.createdAt;
    this.updatedAt = department.updatedAt;

    // Include organisation details if requested and available
    if (includeOrganisation && department.organisation) {
      this.organisation = OrganisationSerializer.serialize(
        department.organisation,
      );
    }
  }

  static serialize(
    department: Department,
    includeOrganisation = false,
  ): DepartmentSerializer {
    return new DepartmentSerializer(department, includeOrganisation);
  }

  static serializeMany(
    departments: Department[],
    includeOrganisation = false,
  ): DepartmentSerializer[] {
    return departments.map((department) =>
      this.serialize(department, includeOrganisation),
    );
  }
}
