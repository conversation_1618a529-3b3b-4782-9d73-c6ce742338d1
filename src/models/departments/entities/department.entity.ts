import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';
import { User } from '@hirenetix/models/users/entities/user.entity';
import {
  DEPARTMENT_TABLE_NAME,
  DEPARTMENT_VALIDATION,
  DepartmentStatus,
  DepartmentType,
} from '../constants/department.constants';

@Entity(DEPARTMENT_TABLE_NAME)
export class Department {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'organisation_id' })
  organisationId: string;

  @ManyToOne(() => Organisation, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'organisation_id' })
  organisation: Organisation;

  @OneToMany(() => User, (user) => user.department)
  users: User[];

  @Column({ length: DEPARTMENT_VALIDATION.NAME_MAX_LENGTH })
  name: string;

  @Column({
    nullable: true,
    type: 'text',
  })
  description: string;

  @Column({
    type: 'enum',
    enum: DepartmentStatus,
    default: DepartmentStatus.ACTIVE,
  })
  status: DepartmentStatus;

  @Column({
    type: 'enum',
    enum: DepartmentType,
    default: DepartmentType.CORE,
    nullable: true,
  })
  type: DepartmentType;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
