export const DEPARTMENT_TABLE_NAME = 'departments';
export const DEPARTMENT_ENTITY_NAME = 'Department';
export const DEPARTMENT_REPO_TOKEN = 'DEPARTMENT_REPOSITORY';

export enum DepartmentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
}

export enum DepartmentType {
  CORE = 'core',
  SUPPORT = 'support',
  PROJECT = 'project',
  TEMPORARY = 'temporary',
}

export const DEPARTMENT_VALIDATION = {
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 100,
  DESCRIPTION_MAX_LENGTH: 500,
  CODE_REGEX: /^[A-Z0-9_]{2,10}$/,
};

export const DEPARTMENT_RELATIONS = {
  ORGANISATION: 'organisation',
  MANAGER: 'manager',
  EMPLOYEES: 'employees',
  PARENT_DEPARTMENT: 'parentDepartment',
  CHILD_DEPARTMENTS: 'childDepartments',
  JOBS: 'jobs',
};

export const DEPARTMENT_SELECT_DEFAULT = {
  id: true,
  name: true,
  code: true,
  description: true,
  status: true,
  type: true,
  createdAt: true,
  updatedAt: true,
};
