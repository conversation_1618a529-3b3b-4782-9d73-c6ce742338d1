import { UserStatus } from '../constants/user.constants';

export interface IUser {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  passwordHash: string;
  tenantId?: string;
  departmentId?: string;
  status: UserStatus;
  city?: string;
  country?: string;
  jobTitle?: string;
  timeZone?: string;
  otpCode?: string;
  otpExpiresAt?: Date;
  isEmailVerified?: boolean;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  createdAt: Date;
  updatedAt: Date;
  locationId?: string;
}

export interface IUserCreate {
  fullName: string;
  email: string;
  phone?: string;
  password?: string;
  tenantId?: string;
  departmentId?: string;
  city?: string;
  country?: string;
  jobTitle?: string;
  timeZone?: string;
  roleId?: string;
  otpCode?: string;
  otpExpiresAt?: Date;
  locationId?: string;
}

export interface IUserUpdate {
  fullName?: string;
  email?: string;
  phone?: string;
  tenantId?: string;
  departmentId?: string;
  status?: UserStatus;
  city?: string;
  country?: string;
  jobTitle?: string;
  timeZone?: string;
  otpCode?: string;
  otpExpiresAt?: Date;
  isEmailVerified?: boolean;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  isPasswordResetRequired?: boolean;
  locationId?: string;
}

export interface IUserSearchQuery {
  id?: string;
  fullName?: string;
  email?: string;
  phone?: string;
  tenantId?: string;
  departmentId?: string;
  status?: UserStatus;
  city?: string;
  country?: string;
  jobTitle?: string;
}
