import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  ManyToOne,
  JoinC<PERSON>umn,
  OneToMany,
} from 'typeorm';
import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';
import { Department } from '@hirenetix/models/departments/entities/department.entity';
import { UserRoleAssignment } from '@hirenetix/models/roles/entities/user-role-assignment.entity';
import {
  USER_TABLE_NAME,
  USER_VALIDATION,
  UserStatus,
} from '../constants/user.constants';
import { TenantEntityBase } from '@hirenetix/models/shared/entities/tenant-entity.base';
import { Location } from '@hirenetix/models/locations/entities/location.entity';

@Entity(USER_TABLE_NAME)
export class User extends TenantEntityBase {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    name: 'full_name',
    length: USER_VALIDATION.NAME_MAX_LENGTH,
  })
  fullName: string;

  @Column({
    unique: true,
    length: USER_VALIDATION.EMAIL_MAX_LENGTH,
  })
  email: string;

  @Column({
    unique: true,
    length: 20,
    nullable: true,
  })
  phone: string;

  @Column({ name: 'password_hash', nullable: true })
  passwordHash: string;

  @Column({ name: 'tenant_id', nullable: true })
  tenantId: string;

  @ManyToOne(() => Organisation, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'tenant_id' })
  organisation: Organisation;

  @Column({ name: 'department_id', nullable: true })
  departmentId: string;

  @ManyToOne(() => Department, (department) => department.users, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'department_id' })
  department: Department;

  @OneToMany(() => UserRoleAssignment, (userRole) => userRole.user)
  userRoleAssignments: UserRoleAssignment[];

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
  })
  status: UserStatus;

  @Column({ nullable: true, length: 255 })
  city: string;

  @Column({ nullable: true, length: 100 })
  country: string;

  @Column({ name: 'job_title', nullable: true, length: 255 })
  jobTitle: string;

  @Column({ name: 'time_zone', nullable: true, length: 100 })
  timeZone: string;

  @Column({ name: 'is_email_verified', default: false })
  isEmailVerified: boolean;

  @Column({ name: 'is_password_reset_required', default: true })
  isPasswordResetRequired: boolean;

  @Column({ name: 'otp_code', nullable: true, length: 10 })
  otpCode: string;

  @Column({ name: 'otp_expires_at', nullable: true })
  otpExpiresAt: Date;

  @Column({ name: 'password_reset_token', nullable: true, length: 100 })
  passwordResetToken: string;

  @Column({ name: 'password_reset_expires', nullable: true })
  passwordResetExpires: Date;

  @Column({ name: 'location_id', nullable: true })
  locationId: string;

  @ManyToOne(() => Location, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'location_id' })
  location: Location;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
