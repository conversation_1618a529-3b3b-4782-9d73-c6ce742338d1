import { ApiProperty } from '@nestjs/swagger';

export class ContactResponseSerializer {
  constructor(responseObj: object) {
    Object.assign(this, responseObj);
  }

  @ApiProperty({
    description: 'Full name of the user',
    example: '<PERSON>',
  })
  fullName: string;

  @ApiProperty({
    description: 'Business email address of the user',
    example: '<EMAIL>',
  })
  businessEmail: string;

  @ApiProperty({
    description: 'Company name of the user',
    example: 'ABC Inc.',
    nullable: true,
  })
  companyName: string;

  @ApiProperty({
    description: 'Contact form response description',
    example: 'I have a company that needs to hire people ...',
    nullable: true,
  })
  description: string;

  @ApiProperty({
    description: 'Whether the user has requested a demo',
    example: true,
  })
  hasRequestedDemo: boolean;

  static serialize(responseObj: object): ContactResponseSerializer {
    return new ContactResponseSerializer(responseObj);
  }
}
