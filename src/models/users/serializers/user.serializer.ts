import { User } from '@hirenetix/models/users/entities/user.entity';
import { ApiProperty } from '@nestjs/swagger';

export class RoleInfo {
  @ApiProperty({
    description: 'ID of the role',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the role',
    example: 'Admin',
  })
  name: string;

  @ApiProperty({
    description: 'Type of the role',
    example: 'location',
  })
  roleType: string;
}

export class LocationInfo {
  @ApiProperty({
    description: 'ID of the location',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the location',
    example: 'New York',
  })
  name: string;

  @ApiProperty({
    description: 'City of the location',
    example: 'New York',
  })
  city: string;

  @ApiProperty({
    description: 'State/Province/Region of the location',
    example: 'New York',
    nullable: true,
  })
  state: string | null;

  @ApiProperty({
    description: 'Country of the location',
    example: 'United States',
    nullable: true,
  })
  country: string | null;

  @ApiProperty({
    description: 'Full address of the location',
    example: '123 Main St, New York, NY 10001',
  })
  address: string;

  @ApiProperty({
    description: 'Unique code for the location',
    example: 'NYC',
  })
  locationCode: string;
}

export class UserSerializer {
  @ApiProperty({
    description: 'Unique identifier of the user',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  })
  id: string;

  @ApiProperty({
    description: 'Full name of the user',
    example: 'John Doe',
  })
  fullName: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Phone number of the user',
    example: '+1234567890',
    nullable: true,
  })
  phone: string;

  @ApiProperty({
    description: 'Tenant/organization ID the user belongs to',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
    nullable: true,
  })
  tenantId: string | null;

  @ApiProperty({
    description: 'Department ID the user belongs to',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
    nullable: true,
  })
  departmentId: string | null;

  @ApiProperty({
    description: 'Current status of the user',
    example: 'active',
  })
  status: string;

  @ApiProperty({
    description: 'City of the user',
    example: 'New York',
    nullable: true,
  })
  city: string | null;

  @ApiProperty({
    description: 'Country of the user',
    example: 'United States',
    nullable: true,
  })
  country: string | null;

  @ApiProperty({
    description: 'Job title of the user',
    example: 'Software Engineer',
    nullable: true,
  })
  jobTitle: string | null;

  @ApiProperty({
    description: 'Time zone of the user',
    example: 'America/New_York',
    nullable: true,
  })
  timeZone: string | null;

  @ApiProperty({
    description: 'Whether the user has verified their email',
    example: true,
  })
  isEmailVerified: boolean;

  @ApiProperty({
    description: 'Whether the user is required to reset their password',
    example: false,
  })
  isPasswordResetRequired: boolean;

  @ApiProperty({
    description: 'Date when the user was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the user was last updated',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description:
      'Roles assigned to the user (legacy field, use roleDetails instead)',
    example: ['Admin', 'Manager'],
    type: [String],
    nullable: true,
    deprecated: true,
  })
  roles?: string[];

  @ApiProperty({
    description: 'Location of the user',
    type: LocationInfo,
    nullable: true,
  })
  location: LocationInfo | null;

  @ApiProperty({
    description: 'Detailed information about roles assigned to the user',
    type: [RoleInfo],
    nullable: true,
    example: [
      {
        id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
        name: 'Admin',
        roleType: 'location',
      },
    ],
  })
  roleDetails?: RoleInfo[];

  constructor(user: User) {
    this.id = user.id;
    this.fullName = user.fullName;
    this.email = user.email;
    this.phone = user.phone;
    this.tenantId = user.tenantId || null;
    this.departmentId = user.departmentId || null;
    this.status = user.status;
    this.city = user.city || null;
    this.country = user.country || null;
    this.jobTitle = user.jobTitle || null;
    this.timeZone = user.timeZone || null;
    this.isEmailVerified = user.isEmailVerified || false;
    this.isPasswordResetRequired = user.isPasswordResetRequired || false;
    this.timeZone = user.timeZone || null;
    this.createdAt = user.createdAt;
    this.updatedAt = user.updatedAt;

    // Set location info if available
    if (user.location) {
      this.location = {
        id: user.location.id,
        name: user.location.name,
        city: user.location.city,
        state: user.location.state,
        country: user.location.country,
        address: user.location.address,
        locationCode: user.location.locationCode,
      };
    } else {
      this.location = null;
    }

    // If user role assignments are loaded, extract role details
    if (user.userRoleAssignments && user.userRoleAssignments.length > 0) {
      // Maintain backward compatibility with roles array
      this.roles = user.userRoleAssignments
        .filter((ura) => ura.role)
        .map((ura) => ura.role.name);

      // Add the new roleDetails array with both ID and name
      this.roleDetails = user.userRoleAssignments
        .filter((ura) => ura.role)
        .map((ura) => ({
          id: ura.role.id,
          name: ura.role.name,
          roleType: ura.role.roleType,
        }));
    }
  }

  static serialize(user: User): UserSerializer {
    return new UserSerializer(user);
  }

  static serializeMany(users: User[]): UserSerializer[] {
    return users.map((user) => this.serialize(user));
  }
}
