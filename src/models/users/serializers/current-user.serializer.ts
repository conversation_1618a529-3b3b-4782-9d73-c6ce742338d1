import { ApiProperty } from '@nestjs/swagger';
import { User } from '@hirenetix/models/users/entities/user.entity';
import { OrganisationSerializer } from '@hirenetix/models/organisations/serializers/organisation.serializer';
import { DepartmentSerializer } from '@hirenetix/models/departments/serializers/department.serializer';
import { LocationSerializer } from '@hirenetix/models/locations/serializers/location.serializer';

export class RolePermissionInfo {
  @ApiProperty({
    description: 'ID of the permission',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the permission',
    example: 'create_user',
  })
  name: string;

  @ApiProperty({
    description: 'Module this permission belongs to',
    example: 'users',
  })
  module: string;

  @ApiProperty({
    description: 'Description of the permission',
    example: 'Allows creating new users',
  })
  description: string;
}

export class CurrentUserRoleInfo {
  @ApiProperty({
    description: 'ID of the role',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the role',
    example: 'Admin',
  })
  name: string;

  @ApiProperty({
    description: 'Description of the role',
    example: 'Administrator role with full access',
  })
  description: string;

  @ApiProperty({
    description: 'Type of the role',
    example: 'organization',
  })
  roleType: string;
}

export class CurrentUserSerializer {
  @ApiProperty({
    description: 'Unique identifier of the user',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  })
  id: string;

  @ApiProperty({
    description: 'Full name of the user',
    example: 'John Doe',
  })
  fullName: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Phone number of the user',
    example: '+1234567890',
    nullable: true,
  })
  phone: string;

  @ApiProperty({
    description: 'Tenant/organization ID the user belongs to',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
    nullable: true,
  })
  tenantId: string | null;

  @ApiProperty({
    description: 'Organization details',
    type: () => OrganisationSerializer,
    nullable: true,
  })
  organisation?: OrganisationSerializer;

  @ApiProperty({
    description: 'Department ID the user belongs to',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
    nullable: true,
  })
  departmentId: string | null;

  @ApiProperty({
    description: 'Department details',
    type: () => DepartmentSerializer,
    nullable: true,
  })
  department?: DepartmentSerializer;

  @ApiProperty({
    description: 'Location ID the user belongs to',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
    nullable: true,
  })
  locationId: string | null;

  @ApiProperty({
    description: 'Location details',
    type: () => LocationSerializer,
    nullable: true,
  })
  location?: LocationSerializer;

  @ApiProperty({
    description: 'Current status of the user',
    example: 'active',
  })
  status: string;

  @ApiProperty({
    description: 'City of the user',
    example: 'New York',
    nullable: true,
  })
  city: string | null;

  @ApiProperty({
    description: 'Country of the user',
    example: 'United States',
    nullable: true,
  })
  country: string | null;

  @ApiProperty({
    description: 'Job title of the user',
    example: 'Software Engineer',
    nullable: true,
  })
  jobTitle: string | null;

  @ApiProperty({
    description: 'Time zone of the user',
    example: 'America/New_York',
    nullable: true,
  })
  timeZone: string | null;

  @ApiProperty({
    description: 'Whether the user has verified their email',
    example: true,
  })
  isEmailVerified: boolean;

  @ApiProperty({
    description: 'Whether the user is required to reset their password',
    example: false,
  })
  isPasswordResetRequired: boolean;

  @ApiProperty({
    description: 'Date when the user was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the user was last updated',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description:
      'Detailed information about roles and permissions assigned to the user',
    type: CurrentUserRoleInfo,
  })
  role: CurrentUserRoleInfo;

  @ApiProperty({
    description: 'Permissions associated with the user',
    example: {
      users: ['create_user', 'edit_user'],
      organizations: ['manage_organization'],
    },
    type: 'object',
    additionalProperties: {
      type: 'array',
      items: {
        type: 'string',
      },
    },
  })
  permissions: Record<string, string[]>;

  constructor(user: User) {
    this.id = user.id;
    this.fullName = user.fullName;
    this.email = user.email;
    this.phone = user.phone;
    this.tenantId = user.tenantId || null;
    this.departmentId = user.departmentId || null;
    this.locationId = user.locationId || null;
    this.status = user.status;
    this.city = user.city || null;
    this.country = user.country || null;
    this.jobTitle = user.jobTitle || null;
    this.timeZone = user.timeZone || null;
    this.isEmailVerified = user.isEmailVerified || false;
    this.isPasswordResetRequired = user.isPasswordResetRequired || false;
    this.createdAt = user.createdAt;
    this.updatedAt = user.updatedAt;

    // Add organization details if available
    if (user.organisation) {
      this.organisation = OrganisationSerializer.serialize(user.organisation);
    }

    // Add department details if available
    if (user.department) {
      this.department = DepartmentSerializer.serialize(user.department);
    }

    // Add location details if available
    if (user.location) {
      this.location = LocationSerializer.serialize(user.location);
    }

    // Process roles and their permissions
    this.role = user.userRoleAssignments
      ?.filter((ura) => ura.role)
      .map((ura) => ({
        id: ura.role.id,
        name: ura.role.name,
        description: ura.role.description,
        roleType: ura.role.roleType,
      }))?.[0] || {
      id: '',
      name: '',
      description: '',
      roleType: '',
    };

    // Group permissions by module
    this.permissions =
      user.userRoleAssignments?.reduce(
        (acc, ura) => {
          if (ura.role?.rolePermissions) {
            ura.role.rolePermissions.forEach((rp) => {
              if (rp.permission) {
                const module = rp.permission.module;
                if (!acc[module]) {
                  acc[module] = [];
                }
                if (!acc[module].includes(rp.permission.name)) {
                  acc[module].push(rp.permission.name);
                }
              }
            });
          }
          return acc;
        },
        {} as Record<string, string[]>,
      ) || {};
  }

  static serialize(user: User): CurrentUserSerializer {
    return new CurrentUserSerializer(user);
  }
}
