export const USER_TABLE_NAME = 'users';
export const USER_ENTITY_NAME = 'User';
export const USER_REPO_TOKEN = 'USER_REPOSITORY';

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export const USER_VALIDATION = {
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 255,
  EMAIL_MAX_LENGTH: 255,
  DESCRIPTION_MIN_LENGTH: 10,
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_MAX_LENGTH: 100,
  PHONE_REGEX: /^\+?[0-9]{10,15}$/,
};

export const USER_RELATIONS = {
  ROLE: 'role',
  ORGANISATION: 'organisation',
  DEPARTMENT: 'department',
  MANAGED_DEPARTMENTS: 'managedDepartments',
};

export const USER_SELECT_DEFAULT = {
  id: true,
  fullName: true,
  email: true,
  status: true,
  createdAt: true,
  updatedAt: true,
};

export enum SORT_FIELD {
  FULL_NAME = 'fullName',
  STATUS = 'status',
  CREATED_AT = 'createdAt',
}

export enum SORT_ORDER {
  ASC = 'ASC',
  DESC = 'DESC',
}

export const USER_SORT_ALLOWED_COLUMNS: SORT_FIELD[] = [
  SORT_FIELD.FULL_NAME,
  SORT_FIELD.STATUS,
  SORT_FIELD.CREATED_AT,
];

export enum ExportFormat {
  CSV = 'csv',
  XLSX = 'xlsx',
}
