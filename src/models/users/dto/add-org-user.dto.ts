import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsString,
  IsUUID,
  <PERSON><PERSON>ength,
  IsPhoneN<PERSON>ber,
  IsOptional,
} from 'class-validator';
import { USER_VALIDATION } from '../constants/user.constants';

export class AddOrgUserDto {
  @ApiProperty({
    description: 'Full name of the user',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(USER_VALIDATION.NAME_MAX_LENGTH)
  fullName: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  @MaxLength(USER_VALIDATION.EMAIL_MAX_LENGTH)
  email: string;

  @ApiProperty({
    description: 'Mobile number of the user',
    example: '+************',
  })
  @IsPhoneNumber(null, {
    message: 'Invalid phone number',
  })
  @IsNotEmpty()
  phone: string;

  @ApiProperty({
    description: 'Role ID for the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  roleId: string;

  @ApiProperty({
    description: 'Timezone of the user',
    example: 'Asia/Kolkata',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  timeZone: string;

  @ApiPropertyOptional({
    description: 'Location ID (required when role type is location)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  locationId?: string;
}
