import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsInt,
  Min,
  Max,
  IsEnum,
  IsDateString,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { UserStatus } from '../constants/user.constants';
import {
  SORT_FIELD,
  SORT_ORDER,
  ExportFormat,
} from '../constants/user.constants';
import { ParseOptionalBoolean } from '@hirenetix/common/decorators/validations/boolean.mapper';

export class UserSearchQueryDto {
  @ApiProperty({
    description: 'Page number (starting from 1)',
    example: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    required: false,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit cannot exceed 100' })
  limit?: number = 10;

  @ApiProperty({
    description: 'Search text for full name, email, or phone',
    example: 'john',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Search must be a string' })
  search?: string;

  @ApiProperty({
    description: 'Filter users by specific role name(s)',
    example: 'Admin,Manager',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Roles must be a string' })
  roles?: string;

  @ApiProperty({
    description: 'Filter users by status',
    enum: UserStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserStatus, { message: 'Invalid status' })
  status?: UserStatus;

  @ApiProperty({
    description: 'Filter users created after this date (ISO 8601 format)',
    example: '2023-01-01T00:00:00Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for startDate' })
  startDate?: string;

  @ApiProperty({
    description: 'Filter users created before this date (ISO 8601 format)',
    example: '2023-12-31T23:59:59Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for endDate' })
  endDate?: string;

  @ApiProperty({
    description: 'Field to sort by',
    enum: SORT_FIELD,
    required: false,
    default: SORT_FIELD.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(SORT_FIELD, { message: 'Invalid sort field' })
  sortBy?: SORT_FIELD = SORT_FIELD.CREATED_AT;

  @ApiProperty({
    description: 'Sort order (ascending or descending)',
    enum: SORT_ORDER,
    required: false,
    default: SORT_ORDER.DESC,
  })
  @IsOptional()
  @IsEnum(SORT_ORDER, { message: 'Invalid sort order' })
  sortOrder?: SORT_ORDER = SORT_ORDER.DESC;

  @ApiProperty({
    description:
      'Flag to export the data instead of returning paginated results',
    example: false,
    required: false,
    default: false,
  })
  @IsOptional()
  @ParseOptionalBoolean()
  @IsBoolean({ message: 'Export flag must be a boolean' })
  export?: boolean = false;

  @ApiProperty({
    description: 'Format for exporting data (csv or xlsx)',
    enum: ExportFormat,
    required: false,
    default: ExportFormat.CSV,
  })
  @IsOptional()
  @IsEnum(ExportFormat, { message: 'Invalid export format' })
  exportFormat?: ExportFormat = ExportFormat.CSV;
}
