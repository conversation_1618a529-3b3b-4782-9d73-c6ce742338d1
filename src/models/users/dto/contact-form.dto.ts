import {
  IsBoolean,
  IsEmail,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { USER_VALIDATION } from '../constants/user.constants';

export class SubmitContactFormDto {
  @IsString()
  @IsNotEmpty()
  @MinLength(USER_VALIDATION.NAME_MIN_LENGTH)
  @MaxLength(USER_VALIDATION.NAME_MAX_LENGTH)
  fullName: string;

  @IsEmail()
  @IsNotEmpty()
  @MaxLength(USER_VALIDATION.EMAIL_MAX_LENGTH)
  businessEmail: string;

  @IsString()
  @IsOptional()
  @MaxLength(USER_VALIDATION.NAME_MAX_LENGTH)
  companyName?: string;

  @IsString()
  @IsOptional()
  @MinLength(USER_VALIDATION.NAME_MIN_LENGTH)
  description?: string;

  @IsBoolean()
  @IsOptional()
  hasRequestedDemo: boolean;

  @IsString()
  @IsNotEmpty()
  captcha: string;

  @IsString()
  @IsNotEmpty()
  captchaImageKey: string;
}
