import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Empty,
  <PERSON><PERSON>ptional,
  IsString,
  IsUUID,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';
import { USER_VALIDATION } from '../constants/user.constants';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  @MinLength(USER_VALIDATION.NAME_MIN_LENGTH)
  @MaxLength(USER_VALIDATION.NAME_MAX_LENGTH)
  fullName: string;

  @IsEmail()
  @IsNotEmpty()
  @IsEmail()
  @IsNotEmpty()
  @MaxLength(USER_VALIDATION.EMAIL_MAX_LENGTH)
  email: string;

  @IsString()
  @IsNotEmpty()
  @Matches(USER_VALIDATION.PHONE_REGEX, {
    message: 'Phone number format is invalid',
  })
  phone: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(USER_VALIDATION.PASSWORD_MIN_LENGTH)
  @MaxLength(USER_VALIDATION.PASSWORD_MAX_LENGTH)
  password: string;

  @IsUUID()
  @IsNotEmpty()
  tenantId: string;

  @IsUUID()
  @IsOptional()
  departmentId?: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  city?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  country?: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  jobTitle?: string;

  @IsString()
  @IsOptional()
  @Matches(/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i, {
    message: 'roleId must be in UUID format',
  })
  roleId?: string;

  @ApiPropertyOptional({
    description: 'Location ID (required when role type is location)',
    example: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  locationId?: string;
}
