import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>E<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'class-validator';
import { USER_VALIDATION } from '../constants';

export class CreateUserByAdminDto {
  @IsString()
  @IsNotEmpty()
  @MinLength(USER_VALIDATION.NAME_MIN_LENGTH)
  @MaxLength(USER_VALIDATION.NAME_MAX_LENGTH)
  fullName: string;

  @IsEmail()
  @IsNotEmpty()
  @MaxLength(USER_VALIDATION.EMAIL_MAX_LENGTH)
  email: string;

  @IsUUID()
  @IsNotEmpty()
  tenantId: string;
}
