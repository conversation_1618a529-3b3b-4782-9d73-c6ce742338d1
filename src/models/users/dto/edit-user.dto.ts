import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  Matches,
} from 'class-validator';
import { USER_VALIDATION, UserStatus } from '../constants/user.constants';

export class EditUserDto {
  @ApiProperty({
    description: 'Full name of the user',
    example: '<PERSON>',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Full name must be a string' })
  @Length(USER_VALIDATION.NAME_MIN_LENGTH, USER_VALIDATION.NAME_MAX_LENGTH, {
    message: `Full name must be between ${USER_VALIDATION.NAME_MIN_LENGTH} and ${USER_VALIDATION.NAME_MAX_LENGTH} characters`,
  })
  fullName?: string;

  @ApiProperty({
    description: 'Phone number with country code',
    example: '+1234567890',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Phone must be a string' })
  @Matches(USER_VALIDATION.PHONE_REGEX, {
    message: 'Phone number must be in a valid format with country code',
  })
  phone?: string;

  @ApiProperty({
    description: 'Time zone of the user (IANA format)',
    example: 'America/New_York',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Time zone must be a string' })
  timeZone?: string;

  @ApiProperty({
    description: 'Role ID to assign to the user',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { message: 'Role ID must be a valid UUID' })
  roleId?: string;

  @ApiProperty({
    description: 'Status of the user',
    example: UserStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserStatus, { message: 'Status must be a valid user status' })
  status?: string;

  @ApiProperty({
    description: 'Location ID (required when role type is location)',
    example: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { message: 'Location ID must be a valid UUID' })
  locationId?: string;
}
