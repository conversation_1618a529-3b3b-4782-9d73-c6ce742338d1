/**
 * Predefined permission data
 */
export const permissions = [
  {
    name: 'view_dashboard_overview',
    description: 'View Dashboard Overview',
    module: 'Dashboard',
  },
  {
    name: 'access_company_wide_metrics',
    description: 'Access Company-Wide Metrics',
    module: 'Dashboard',
  },
  {
    name: 'export_dashboard_reports',
    description: 'Export Dashboard Reports',
    module: 'Dashboard',
  },
  {
    name: 'create_new_job_position',
    description: 'Create New Job Position',
    module: 'Positions',
  },
  {
    name: 'edit_job_position',
    description: 'Edit Job Position',
    module: 'Positions',
  },
  {
    name: 'delete_job_position',
    description: 'Delete Job Position',
    module: 'Positions',
  },
  {
    name: 'view_job_listings',
    description: 'View Job Listings',
    module: 'Positions',
  },
  {
    name: 'duplicate_existing_position',
    description: 'Duplicate Existing Position',
    module: 'Positions',
  },
  {
    name: 'set_job_visibility',
    description: 'Set Job Visibility (Public/Internal)',
    module: 'Positions',
  },
  {
    name: 'set_job_status',
    description: 'Set Job Status (Draft/Published/Closed)',
    module: 'Positions',
  },
  {
    name: 'view_application_analytics',
    description: 'View Application Analytics',
    module: 'Insights',
  },
  {
    name: 'export_insight_reports',
    description: 'Export Insight Reports',
    module: 'Insights',
  },
  {
    name: 'view_all_locations',
    description: 'View All Locations',
    module: 'Locations',
  },
  {
    name: 'create_location',
    description: 'Create Location',
    module: 'Locations',
  },
  {
    name: 'edit_location',
    description: 'Edit Location',
    module: 'Locations',
  },
  {
    name: 'delete_location',
    description: 'Delete Location',
    module: 'Locations',
  },
  {
    name: 'view_all_users',
    description: 'View All Users',
    module: 'Users',
  },
  {
    name: 'create_user',
    description: 'Create User',
    module: 'Users',
  },
  {
    name: 'edit_user',
    description: 'Edit User',
    module: 'Users',
  },
  {
    name: 'delete_user',
    description: 'Delete User',
    module: 'Users',
  },
  {
    name: 'view_roles',
    description: 'View Roles',
    module: 'Roles',
  },
  {
    name: 'edit_roles',
    description: 'Edit Roles',
    module: 'Roles',
  },
  {
    name: 'delete_roles',
    description: 'Delete Roles',
    module: 'Roles',
  },
  {
    name: 'add_roles',
    description: 'Add Roles',
    module: 'Roles',
  },
  {
    name: 'view_jobs',
    description: 'View Jobs',
    module: 'Jobs',
  },
  {
    name: 'create_jobs',
    description: 'Create Jobs',
    module: 'Jobs',
  },
  {
    name: 'edit_jobs',
    description: 'Edit Jobs',
    module: 'Jobs',
  },
  {
    name: 'delete_jobs',
    description: 'Delete Jobs',
    module: 'Jobs',
  },
  {
    name: 'publish_jobs',
    description: 'Publish Jobs',
    module: 'Jobs',
  },
  {
    name: 'view_job_applications',
    description: 'View Job Applications',
    module: 'Candidates',
  },
  {
    name: 'edit_job_applications',
    description: 'Edit Job Applications',
    module: 'Candidates',
  },
  {
    name: 'delete_job_applications',
    description: 'Delete Job Applications',
    module: 'Candidates',
  },
  {
    name: 'create_job_applications',
    description: 'Create Job Applications',
    module: 'Candidates',
  },
];
