import { DataSource } from 'typeorm';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import { Permission } from '@hirenetix/models/roles/entities/permission.entity';
import { RolePermission } from '@hirenetix/models/roles/entities/role-permission.entity';
import { Logger } from '@nestjs/common';
import { SystemRoles } from '@hirenetix/models/roles/constants';
import AppDataSource from '@hirenetix/providers/database/postgres/data-source';

// Create a logger
const logger = new Logger('AssignAdminPermissions');

/**
 * Assigns all permissions to the admin role
 */
export async function assignAdminPermissions(
  dataSource: DataSource,
): Promise<void> {
  // Make sure the datasource is initialized
  if (!dataSource.isInitialized) {
    await dataSource.initialize();
  }

  const queryRunner = dataSource.createQueryRunner();

  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Get admin role
    const adminRole = await queryRunner.manager.findOne(Role, {
      where: { name: SystemRoles.ADMIN },
    });

    if (!adminRole) {
      throw new Error(
        'Admin role not found. Please run the roles seeder first.',
      );
    }

    logger.log(`Found admin role with ID: ${adminRole.id}`);

    // Get all permissions
    const permissions = await queryRunner.manager.find(Permission);

    if (permissions.length === 0) {
      throw new Error(
        'No permissions found. Please run the permissions seeder first.',
      );
    }

    logger.log(
      `Found ${permissions.length} permissions to assign to admin role`,
    );

    // Assign each permission to the admin role
    const rolePermissions = [];
    for (const permission of permissions) {
      const rolePermission = new RolePermission();
      rolePermission.roleId = adminRole.id;
      rolePermission.permissionId = permission.id;

      // Check if permissions are already assigned
      const existingAssignments = await queryRunner.manager.count(
        RolePermission,
        {
          where: { roleId: adminRole.id, permissionId: permission.id },
        },
      );

      if (existingAssignments > 0) {
        logger.log(
          `Admin role already has ${existingAssignments} permissions assigned. Skipping assignment.`,
        );
        continue;
      }

      rolePermissions.push(rolePermission);
    }

    await queryRunner.manager.save(RolePermission, rolePermissions);
    logger.log(`Assigned ${permissions.length} permissions to admin role`);

    // Commit transaction
    await queryRunner.commitTransaction();
    logger.log('Admin permissions assigned successfully!');
  } catch (error) {
    // Rollback transaction in case of error
    await queryRunner.rollbackTransaction();
    logger.error('Error assigning admin permissions:', error);
    throw error;
  } finally {
    // Release the query runner
    await queryRunner.release();
  }
}

/**
 * Standalone script to assign all permissions to the admin role.
 * This can be run directly with ts-node.
 */
async function main() {
  try {
    console.log('Initializing connection to database...');

    // Initialize the data source
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    // Run the assignment function
    await assignAdminPermissions(AppDataSource);
    console.log('Admin permissions assignment completed successfully.');
  } catch (error) {
    console.error('Error assigning admin permissions:', error);
  } finally {
    // Close the connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed.');
    }
  }
}

// Execute the script if it's called directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Unhandled error:', error);
      process.exit(1);
    });
}
