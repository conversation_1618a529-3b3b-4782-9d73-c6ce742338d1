import { DataSource } from 'typeorm';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';
import { Logger } from '@nestjs/common';
import { SystemRoles } from '@hirenetix/models/roles/constants';
import AppDataSource from '@hirenetix/providers/database/postgres/data-source';

// Create a logger
const logger = new Logger('CreateTenantRoles');

/**
 * Creates a tenant-specific admin role for the specified tenant
 * @param dataSource The TypeORM data source
 * @param tenantId The ID of the tenant (organization)
 * @param description Optional description for the role
 * @param isCustom Whether the role is custom (default: true)
 */
export async function createTenantAdminRole(
  dataSource: DataSource,
  tenantId: string,
  description?: string,
  isCustom: boolean = true,
): Promise<Role> {
  if (!tenantId) {
    throw new Error('Tenant ID is required');
  }

  // Make sure the datasource is initialized
  if (!dataSource.isInitialized) {
    await dataSource.initialize();
  }

  const queryRunner = dataSource.createQueryRunner();

  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Verify that the organization exists
    const organisation = await queryRunner.manager.findOne(Organisation, {
      where: { id: tenantId },
    });

    if (!organisation) {
      throw new Error(`Organization with ID ${tenantId} not found`);
    }

    logger.log(`Found organization: ${organisation.name} (${tenantId})`);

    // Check if a role already exists for this tenant
    const existingRole = await queryRunner.manager.findOne(Role, {
      where: {
        name: SystemRoles.ADMIN,
        tenantId: tenantId,
      },
    });

    if (existingRole) {
      logger.log(`Admin role already exists for tenant ${tenantId}`);
      await queryRunner.commitTransaction();
      return existingRole;
    }

    // Create the tenant-specific admin role
    const role = new Role();
    role.name = SystemRoles.ADMIN;
    role.description = description || `Administrator for ${organisation.name}`;
    role.tenantId = tenantId;
    role.custom = isCustom;

    const savedRole = await queryRunner.manager.save(role);
    logger.log(
      `Created admin role for tenant ${organisation.name} (${tenantId})`,
    );

    // Commit transaction
    await queryRunner.commitTransaction();
    return savedRole;
  } catch (error) {
    // Rollback transaction in case of error
    await queryRunner.rollbackTransaction();
    logger.error(`Error creating admin role for tenant ${tenantId}:`, error);
    throw error;
  } finally {
    // Release the query runner
    await queryRunner.release();
  }
}

/**
 * Standalone script to create a tenant-specific admin role.
 * This can be run directly with ts-node.
 *
 * Usage:
 * npx ts-node -r tsconfig-paths/register src/database/seeders/create-tenant-roles.ts <tenantId> [description] [--system]
 *
 * Use the --system flag to create a non-custom (system) role
 */
async function main() {
  try {
    console.log('Initializing connection to database...');

    // Initialize the data source
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    // Get the tenant ID from command line arguments
    const args = process.argv.slice(2);
    const tenantId = args[0];

    // Check if the --system flag is present
    const isSystemRole = args.includes('--system');

    // Remove the --system flag from args if present for description parsing
    const cleanArgs = args.filter((arg) => arg !== '--system');
    const description = cleanArgs.length > 1 ? cleanArgs[1] : undefined;

    if (!tenantId) {
      console.error(
        'Please provide a tenant ID (organization ID) as the first argument',
      );
      process.exit(1);
    }

    // Create the tenant-specific admin role
    const role = await createTenantAdminRole(
      AppDataSource,
      tenantId,
      description,
      !isSystemRole, // If --system flag is present, set custom to false
    );
    console.log(
      `Successfully created role with ID: ${role.id} (custom: ${role.custom})`,
    );
  } catch (error) {
    console.error('Error creating tenant role:', error);
    process.exit(1);
  } finally {
    // Close the connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed.');
    }
  }
}

// Execute the script if it's called directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Unhandled error:', error);
      process.exit(1);
    });
}
