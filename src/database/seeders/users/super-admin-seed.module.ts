import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@hirenetix/models/users/entities/user.entity';
import { UserRoleAssignment } from '@hirenetix/models/roles/entities/user-role-assignment.entity';
import { SuperAdminSeedService } from './super-admin-seed.service';
import { PostgresModule } from '@hirenetix/providers/database/postgres/provider.module';
import { DatabaseConfigModule } from '@hirenetix/config/database/postgres/config.module';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import { Permission } from '@hirenetix/models/roles/entities/permission.entity';
import { RolePermission } from '@hirenetix/models/roles/entities/role-permission.entity';
import { TenantEntityBase } from '@hirenetix/models/shared/entities/tenant-entity.base';
import { Organisation } from '../../../models/organisations/entities/organisation.entity';
import { Department } from '../../../models/departments/entities/department.entity';
/**
 * This module provides the service for seeding the super admin user.
 */
@Module({
  imports: [
    DatabaseConfigModule,
    PostgresModule,
    TypeOrmModule.forFeature([
      User,
      UserRoleAssignment,
      Role,
      Permission,
      RolePermission,
      TenantEntityBase,
      Organisation,
      Department,
    ]),
  ],
  providers: [SuperAdminSeedService],
  exports: [SuperAdminSeedService],
})
export class SuperAdminSeedModule {}
