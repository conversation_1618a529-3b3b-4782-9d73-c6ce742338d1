import { NestFactory } from '@nestjs/core';
import { SuperAdminSeedModule } from './super-admin-seed.module';
import { SuperAdminSeedService } from './super-admin-seed.service';
import { Logger } from '@nestjs/common';

/**
 * Standalone script to seed the super admin user.
 * This can be run directly with ts-node.
 */
async function bootstrap() {
  const logger = new Logger('SuperAdminSeed');

  try {
    logger.log('Creating NestJS application context...');
    const app =
      await NestFactory.createApplicationContext(SuperAdminSeedModule);

    try {
      logger.log('Getting SuperAdminSeedService...');
      const seederService = app.get(SuperAdminSeedService);

      // Get command line arguments
      const args = process.argv.slice(2);

      // Check if custom details were provided
      if (args.length >= 4) {
        logger.log('Seeding custom super admin...');
        await seederService.seedCustom({
          fullName: args[0],
          email: args[1],
          phone: args[2],
          password: args[3],
        });
      } else {
        logger.log('Seeding default super admin...');
        await seederService.seed();
      }

      logger.log('Super admin seeding completed successfully!');
    } catch (error) {
      logger.error('Error during seeding:', error);
      process.exit(1);
    } finally {
      await app.close();
    }
  } catch (error) {
    logger.error('Failed to create application context:', error);
    process.exit(1);
  }
}

// Execute the script if it's called directly
if (require.main === module) {
  bootstrap()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Unhandled error:', error);
      process.exit(1);
    });
}

export { bootstrap };
