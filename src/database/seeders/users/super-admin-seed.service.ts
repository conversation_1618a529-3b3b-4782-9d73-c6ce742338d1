import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { User } from '@hirenetix/models/users/entities/user.entity';
import { UserRoleAssignment } from '@hirenetix/models/roles/entities/user-role-assignment.entity';
import * as bcrypt from 'bcrypt';

@Injectable()
export class SuperAdminSeedService {
  private readonly logger = new Logger(SuperAdminSeedService.name);

  // Super admin user details - these could be moved to a configuration file
  private readonly SUPER_ADMIN = {
    fullName: 'super admin',
    email: '<EMAIL>',
    phone: '+1234567890',
    password: 'admin@123',
  };

  // Super admin role ID from roles-permissions.data.ts
  private readonly SUPER_ADMIN_ROLE_ID = '250e5dcc-979d-463f-a272-2b79265af773';

  constructor(
    @InjectDataSource() private readonly dataSource: DataSource,
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    @InjectRepository(UserRoleAssignment)
    private readonly userRoleAssignmentRepository: Repository<UserRoleAssignment>,
  ) {}

  /**
   * Creates a super admin user with the super_admin role if it doesn't exist
   */
  async seed(): Promise<void> {
    this.logger.log('Starting super admin seeding...');

    try {
      // Check if super admin already exists
      const existingAdmin = await this.userRepository.findOne({
        where: { email: this.SUPER_ADMIN.email },
      });

      if (existingAdmin) {
        this.logger.log(
          'Super admin already exists, checking role assignments...',
        );

        // Check if the role assignment exists
        const existingRoleAssignment =
          await this.userRoleAssignmentRepository.findOne({
            where: {
              userId: existingAdmin.id,
              roleId: this.SUPER_ADMIN_ROLE_ID,
            },
          });

        if (existingRoleAssignment) {
          this.logger.log('Super admin role already assigned.');
        } else {
          this.logger.log('Assigning super_admin role to the existing user...');
          const roleAssignment = new UserRoleAssignment();
          roleAssignment.userId = existingAdmin.id;
          roleAssignment.roleId = this.SUPER_ADMIN_ROLE_ID;
          await this.userRoleAssignmentRepository.save(roleAssignment);
          this.logger.log('Role assigned successfully.');
        }

        return;
      }

      this.logger.log('Creating super admin user...');

      // Create a new super admin user
      const saltRounds = 10;
      const user = new User();

      user.fullName = this.SUPER_ADMIN.fullName;
      user.email = this.SUPER_ADMIN.email;
      user.phone = this.SUPER_ADMIN.phone;
      user.passwordHash = await bcrypt.hash(
        this.SUPER_ADMIN.password,
        saltRounds,
      );

      // Save the user
      const savedUser = await this.userRepository.save(user);
      this.logger.log(`Super admin created with ID: ${savedUser.id}`);

      // Assign the super_admin role
      this.logger.log('Assigning super_admin role...');
      const roleAssignment = new UserRoleAssignment();
      roleAssignment.userId = savedUser.id;
      roleAssignment.roleId = this.SUPER_ADMIN_ROLE_ID;
      await this.userRoleAssignmentRepository.save(roleAssignment);

      this.logger.log(
        'Super admin user created and role assigned successfully!',
      );
      this.logger.log(`Email: ${this.SUPER_ADMIN.email}`);
      this.logger.log(`Password: ${this.SUPER_ADMIN.password}`);
    } catch (error) {
      this.logger.error('Error creating super admin:', error);
      throw error;
    }
  }

  /**
   * Creates a super admin user with custom details
   */
  async seedCustom(customDetails: {
    fullName: string;
    email: string;
    phone: string;
    password: string;
  }): Promise<void> {
    this.logger.log('Starting custom super admin seeding...');

    try {
      // Check if user with this email already exists
      const existingAdmin = await this.userRepository.findOne({
        where: { email: customDetails.email },
      });

      if (existingAdmin) {
        this.logger.log(
          'User with this email already exists, checking role assignments...',
        );

        // Check if the role assignment exists
        const existingRoleAssignment =
          await this.userRoleAssignmentRepository.findOne({
            where: {
              userId: existingAdmin.id,
              roleId: this.SUPER_ADMIN_ROLE_ID,
            },
          });

        if (existingRoleAssignment) {
          this.logger.log('Super admin role already assigned.');
        } else {
          this.logger.log('Assigning super_admin role to the existing user...');
          const roleAssignment = new UserRoleAssignment();
          roleAssignment.userId = existingAdmin.id;
          roleAssignment.roleId = this.SUPER_ADMIN_ROLE_ID;
          await this.userRoleAssignmentRepository.save(roleAssignment);
          this.logger.log('Role assigned successfully.');
        }

        return;
      }

      this.logger.log('Creating custom super admin user...');

      // Create a new super admin user
      const saltRounds = 10;
      const user = new User();

      user.fullName = customDetails.fullName;
      user.email = customDetails.email;
      user.phone = customDetails.phone;
      user.passwordHash = await bcrypt.hash(customDetails.password, saltRounds);

      // Save the user
      const savedUser = await this.userRepository.save(user);
      this.logger.log(`Super admin created with ID: ${savedUser.id}`);

      // Assign the super_admin role
      this.logger.log('Assigning super_admin role...');
      const roleAssignment = new UserRoleAssignment();
      roleAssignment.userId = savedUser.id;
      roleAssignment.roleId = this.SUPER_ADMIN_ROLE_ID;
      await this.userRoleAssignmentRepository.save(roleAssignment);

      this.logger.log(
        'Custom super admin user created and role assigned successfully!',
      );
      this.logger.log(`Email: ${customDetails.email}`);
      this.logger.log(`Password: ${customDetails.password}`);
    } catch (error) {
      this.logger.error('Error creating custom super admin:', error);
      throw error;
    }
  }
}
