/**
 * Standalone script to seed permissions.
 *
 * Usage:
 * npx ts-node -r tsconfig-paths/register src/database/seeders/seed-permissions-standalone.ts
 */

import { Permission } from '@hirenetix/models/roles/entities/permission.entity';
import AppDataSource from '@hirenetix/providers/database/postgres/data-source';
import { seedPermissions } from './seed-permissions';
import { permissions } from '../data/permissions.data';

/**
 * Standalone script to seed permissions without NestJS.
 * This can be run directly with ts-node.
 */
async function main() {
  try {
    console.log('Initializing connection to database...');

    // Initialize the data source
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    // Check if data already exists to avoid duplicates
    const permissionCount =
      await AppDataSource.getRepository(Permission).count();

    console.log(permissionCount, permissions.length);
    if (permissionCount === permissions.length) {
      console.log('Permissions already exist, skipping seed.');
      return;
    }

    // Run the seed function
    await seedPermissions(AppDataSource);
    console.log('Permissions seeding completed successfully.');
  } catch (error) {
    console.error('Error seeding permissions:', error);
  } finally {
    // Close the connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed.');
    }
  }
}

// Execute the script if it's called directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Unhandled error:', error);
      process.exit(1);
    });
}
