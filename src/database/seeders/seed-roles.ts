import { DataSource } from 'typeorm';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import { Permission } from '@hirenetix/models/roles/entities/permission.entity';
import { roles } from '@hirenetix/database/data/roles-permissions.data';
import { permissions } from '@hirenetix/database/data/permissions.data';
import { Logger } from '@nestjs/common';
import { SystemRoles } from '@hirenetix/models/roles/constants';
// Create a logger
const logger = new Logger('SeedRoles');

export async function seedRolesAndPermissions(
  dataSource: DataSource,
): Promise<void> {
  // Make sure the datasource is initialized
  if (!dataSource.isInitialized) {
    await dataSource.initialize();
  }

  const queryRunner = dataSource.createQueryRunner();

  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    logger.log('Seeding roles...');
    // Insert roles
    for (const roleData of roles) {
      const role = new Role();
      role.id = roleData.id;
      role.name = roleData.name as SystemRoles;
      role.description = roleData.description;
      role.tenantId = roleData.tenantId;
      role.custom = roleData.custom !== undefined ? roleData.custom : true;

      await queryRunner.manager.save(role);
    }
    logger.log(`Created ${roles.length} roles`);

    logger.log('Seeding permissions...');
    // Insert permissions
    for (const permissionData of permissions) {
      const permission = new Permission();
      permission.name = permissionData.name;
      permission.description = permissionData.description;
      permission.module = permissionData.module;

      await queryRunner.manager.save(permission);
    }
    logger.log(`Created ${permissions.length} permissions`);

    // Commit transaction
    await queryRunner.commitTransaction();
    logger.log('Roles and permissions seeded successfully!');
  } catch (error) {
    // Rollback transaction in case of error
    await queryRunner.rollbackTransaction();
    logger.error('Error seeding roles and permissions:', error);
    throw error;
  } finally {
    // Release the query runner
    await queryRunner.release();
  }
}
