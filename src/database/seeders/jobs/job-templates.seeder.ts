import { DataSource } from 'typeorm';
import { JobTemplate } from '../../../models/jobs/entities/job-template.entity';
import {
  JobTemplateSection,
  JobTemplateSectionType,
} from '../../../models/jobs/entities/job-template-section.entity';
import { SectionDefinition } from '../../../models/jobs/entities/section-definition.entity';
import { Logger } from '@nestjs/common';
import AppDataSource from '../../../providers/database/postgres/data-source';

const logger = new Logger('SeedJobTemplates');

export async function seedJobTemplates(dataSource: DataSource): Promise<void> {
  logger.log('Starting job templates seeding...');

  try {
    if (!dataSource.isInitialized) {
      logger.log('Initializing data source...');
      await dataSource.initialize();
      logger.log('Data source initialized successfully');
    }

    const queryRunner = dataSource.createQueryRunner();
    logger.log('Created query runner');

    await queryRunner.connect();
    logger.log('Connected to database');

    await queryRunner.startTransaction();
    logger.log('Started transaction');

    const jobTemplateRepository =
      queryRunner.manager.getRepository(JobTemplate);
    const sectionDefinitionRepository =
      queryRunner.manager.getRepository(SectionDefinition);
    const jobTemplateSectionRepository =
      queryRunner.manager.getRepository(JobTemplateSection);

    logger.log('Checking for existing templates...');
    // Check if job templates already exist
    const existingTemplates = await jobTemplateRepository.count();
    if (existingTemplates > 2) {
      logger.log('Job templates already exist. Skipping seeding...');
      return;
    }

    logger.log('Fetching section definitions...');
    // Get section definition IDs
    const sectionDefinitions = await sectionDefinitionRepository.find({
      where: [
        { defaultName: 'Job Details' },
        { defaultName: 'Job Highlights' },
        { defaultName: 'Qualifications' },
        { defaultName: 'Benefits' },
        { defaultName: 'Responsibilities' },
        { defaultName: 'Job Description' },
      ],
    });

    logger.log(`Found ${sectionDefinitions.length} section definitions`);

    // Create a map of section names to their IDs
    const sectionMap = new Map(
      sectionDefinitions.map((def) => [def.defaultName, def.id]),
    );

    // Verify all required sections exist
    const requiredSections = [
      'Job Highlights',
      'Qualifications',
      'Benefits',
      'Responsibilities',
      'Job Description',
    ];

    const missingSections = requiredSections.filter(
      (section) => !sectionMap.has(section),
    );

    if (missingSections.length > 0) {
      logger.log(
        `Missing required section definitions: ${missingSections.join(', ')}. Skipping seeding...`,
      );
      return;
    }

    // Create the Crew Member job template
    logger.log('Creating Crew Member job template...');
    const crewMemberTemplate = await jobTemplateRepository.save({
      templateName: 'Crew Member',
      description: 'Crew Member job template',
      isSystemDefined: true,
    });
    logger.log('Crew Member job template created successfully');

    // Create the Software Engineer job template
    logger.log('Creating Software Engineer job template...');
    const softwareEngineerTemplate = await jobTemplateRepository.save({
      templateName: 'Software Engineer',
      description: 'Software Engineer job template',
      isSystemDefined: true,
    });
    logger.log('Software Engineer job template created successfully');

    logger.log('Creating template sections...');
    // Create template sections for Crew Member
    const crewMemberSections: Partial<JobTemplateSection>[] = [
      // Job Highlights Section
      {
        templateId: crewMemberTemplate.id,
        sectionDefinitionId: sectionMap.get('Job Highlights'),
        title: 'Job Highlights',
        displayOrder: 2,
        isVisibleDefault: true,
        isEditable: true,
        isDeletable: true,
        type: JobTemplateSectionType.HIGHLIGHTS,
        content: [
          {
            key: 'job_type',
            label: 'Job type',
            type: 'select',
            options: ['Full time', 'Part time'],
            value: 'Full time',
          },
          {
            key: 'compensation',
            label: 'Competitive Pay',
            type: 'range',
            currency: 'USD($)',
            min: 11,
            max: 13,
          },
          {
            key: 'age_limit',
            label: 'Age Limit',
            type: 'singleInput',
            value: '16',
          },
          {
            key: 'shift',
            label: 'Shift',
            type: 'select',
            options: ['Day', 'Night'],
            value: 'Day',
          },
        ],
      },
      // Qualifications Section
      {
        templateId: crewMemberTemplate.id,
        sectionDefinitionId: sectionMap.get('Qualifications'),
        title: 'Qualifications',
        displayOrder: 3,
        isVisibleDefault: true,
        isEditable: true,
        isDeletable: true,
        type: JobTemplateSectionType.QUALIFICATIONS,
        content: [
          {
            type: 'text',
            value: `At least 16 years of age to be able to work as a Crew Member at Sushi Maki
            Previous restaurant experience is preferred but not required to have a positive experience
            Proficient in scheduling with Front-of-house lead
            Follow safe food Prep and Management to make sure guests and have fun`,
          },
        ],
      },
      // Benefits Section
      {
        templateId: crewMemberTemplate.id,
        sectionDefinitionId: sectionMap.get('Benefits'),
        title: 'Benefits',
        displayOrder: 4,
        isVisibleDefault: true,
        isEditable: true,
        isDeletable: true,
        type: JobTemplateSectionType.BENEFITS,
        content: [
          {
            type: 'text',
            value:
              'Crew member opportunities are available primarily evening, including: lunch, late nights, weekends',
          },
        ],
      },
      // Responsibilities Section
      {
        templateId: crewMemberTemplate.id,
        sectionDefinitionId: sectionMap.get('Responsibilities'),
        title: 'Responsibilities',
        displayOrder: 5,
        isVisibleDefault: true,
        isEditable: true,
        isDeletable: true,
        type: JobTemplateSectionType.RESPONSIBILITIES,
        content: [
          {
            type: 'text',
            value: `Previous restaurant experience is preferred but not required to have a positive experience.
            Follow safe food Prep and Management to make sure guests and have fun.`,
          },
        ],
      },
      // Job Description Section
      {
        templateId: crewMemberTemplate.id,
        sectionDefinitionId: sectionMap.get('Job Description'),
        title: 'Job Description',
        displayOrder: 6,
        isVisibleDefault: true,
        isEditable: true,
        isDeletable: true,
        type: JobTemplateSectionType.JOB_DESCRIPTION,
        content: [
          {
            type: 'text',
            value: `Thank you for your interest in GT's Food Services and/or Logan's Roadhouse. This opportunity is for the Sushi Maki located at 2040 SW 8th Street in Miami, FL. Please visit our website at www.sushimaki.com to learn more about our company.
            Are you looking for a company that values its team members, is growing and has a culture unique to Sushi Maki USA? This food chain wants a license for our Sushi Maki logo, and retail products, be available when visiting the restaurant. However, this food chain is a well-run company with a separate company from Sushi Maki USA. When you're hired, you'll be employed by GT's Food Services and/or Logan's Roadhouse, Sushi Maki USA. While the food chain is responsible for employment matters at this restaurant, the training offered by GT's Food Services and/or Logan's Roadhouse, Sushi Maki USA is also available to train members at the restaurant. Sushi Maki USA will not directly employ a copy of your employment application and it will be available to Sushi Maki USA at any time, whether you are hired.`,
          },
        ],
      },
    ];

    // Create template sections for Software Engineer
    const softwareEngineerSections: Partial<JobTemplateSection>[] = [
      // Job Details Section
      {
        templateId: softwareEngineerTemplate.id,
        sectionDefinitionId: sectionMap.get('Job Details'),
        title: 'Job Highlights',
        displayOrder: 1,
        isVisibleDefault: true,
        isEditable: true,
        isDeletable: false,
        type: JobTemplateSectionType.HIGHLIGHTS,
        content: [
          {
            key: 'job_type',
            label: 'Job type',
            type: 'select',
            options: ['Full time', 'Part time'],
            value: 'Full time',
          },
          {
            key: 'compensation',
            label: 'Competitive Pay',
            type: 'range',
            currency: 'USD($)',
            min: 11,
            max: 13,
          },
          {
            key: 'age_limit',
            label: 'Age Limit',
            type: 'singleInput',
            value: '16',
          },
          {
            key: 'shift',
            label: 'Shift',
            type: 'select',
            options: ['Day', 'Night'],
            value: 'Day',
          },
        ],
      },
      // Qualifications Section
      {
        templateId: softwareEngineerTemplate.id,
        sectionDefinitionId: sectionMap.get('Qualifications'),
        title: 'Qualifications',
        displayOrder: 2,
        isVisibleDefault: true,
        isEditable: true,
        isDeletable: true,
        type: JobTemplateSectionType.QUALIFICATIONS,
        content: [
          {
            type: 'text',
            value: `Bachelor's degree in Computer Science or related field.
            3+ years of experience in software development
            Strong proficiency in JavaScript/TypeScript and modern frameworks
            Experience with cloud platforms (AWS, GCP, or Azure) 
            Knowledge of agile development practices`,
          },
        ],
      },
      // Benefits Section
      {
        templateId: softwareEngineerTemplate.id,
        sectionDefinitionId: sectionMap.get('Benefits'),
        title: 'Benefits',
        displayOrder: 3,
        isVisibleDefault: true,
        isEditable: true,
        isDeletable: true,
        type: JobTemplateSectionType.BENEFITS,
        content: [
          {
            type: 'text',
            value: `Comprehensive health, dental, and vision insurance
            401(k) matching program
            Generous PTO and paid holidays
            Professional development budget
            Home office setup allowance
            Wellness programs and gym membership`,
          },
        ],
      },
      // Responsibilities Section
      {
        templateId: softwareEngineerTemplate.id,
        sectionDefinitionId: sectionMap.get('Responsibilities'),
        title: 'Responsibilities',
        displayOrder: 4,
        isVisibleDefault: true,
        isEditable: true,
        isDeletable: true,
        type: JobTemplateSectionType.RESPONSIBILITIES,
        content: [
          {
            type: 'text',
            value: `Design and implement scalable software solutions
            Collaborate with cross-functional teams to define and develop new features
            Write clean, maintainable, and efficient code
            Participate in code reviews and provide constructive feedback
            Debug production issues and optimize application performance
            Mentor junior developers and contribute to team growth`,
          },
        ],
      },
      // Job Description Section
      {
        templateId: softwareEngineerTemplate.id,
        sectionDefinitionId: sectionMap.get('Job Description'),
        title: 'Job Description',
        displayOrder: 5,
        isVisibleDefault: true,
        isEditable: true,
        isDeletable: true,
        type: JobTemplateSectionType.JOB_DESCRIPTION,
        content: [
          {
            type: 'text',
            value: `We are seeking a talented Software Engineer to join our dynamic engineering team. As a Software Engineer at our company, you'll be working on challenging problems and building innovative solutions that impact millions of users. You'll be part of a collaborative environment where you can grow your skills, take on new challenges, and make a significant impact.
            We value creativity, initiative, and a passion for building great software. Our engineering team follows best practices in software development, including continuous integration, automated testing, and regular deployments. We encourage experimentation and learning, and provide opportunities for professional growth through mentorship, training, and attendance at technical conferences.
            If you're excited about working with modern technologies, solving complex problems, and being part of a team that values innovation and growth, we'd love to hear from you.`,
          },
        ],
      },
    ];

    // Save all sections
    await jobTemplateSectionRepository.save([
      ...crewMemberSections,
      ...softwareEngineerSections,
    ]);
    logger.log('Template sections created successfully');

    // Commit transaction
    await queryRunner.commitTransaction();
    logger.log('Transaction committed successfully');
  } catch (error) {
    logger.error('Error during seeding:', error);
    throw error;
  } finally {
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      logger.log('Data source destroyed');
    }
  }
}

// Add this at the end of the file
if (require.main === module) {
  const dataSource = AppDataSource;

  seedJobTemplates(dataSource)
    .then(() => {
      logger.log('Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Error during seeding:', error);
      process.exit(1);
    });
}
