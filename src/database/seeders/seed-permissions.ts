import { DataSource } from 'typeorm';
import { Permission } from '@hirenetix/models/roles/entities/permission.entity';
import { permissions } from '@hirenetix/database/data/permissions.data';
import { Logger } from '@nestjs/common';

// Create a logger
const logger = new Logger('SeedPermissions');

export async function seedPermissions(dataSource: DataSource): Promise<void> {
  // Make sure the datasource is initialized
  if (!dataSource.isInitialized) {
    await dataSource.initialize();
  }

  const queryRunner = dataSource.createQueryRunner();

  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    logger.log('Seeding permissions...');

    // Insert permissions
    for (const permissionData of permissions) {
      const permission = new Permission();
      permission.name = permissionData.name;
      permission.description = permissionData.description;
      permission.module = permissionData.module;

      //Check if permission already exists
      const existingPermission = await queryRunner.manager.findOne(Permission, {
        where: { name: permissionData.name },
      });
      if (existingPermission) {
        continue;
      }

      await queryRunner.manager.save(permission);
    }

    logger.log(`Created ${permissions.length} permissions`);

    // Commit transaction
    await queryRunner.commitTransaction();
    logger.log('Permissions seeded successfully!');
  } catch (error) {
    // Rollback transaction in case of error
    await queryRunner.rollbackTransaction();
    logger.error('Error seeding permissions:', error);
    throw error;
  } finally {
    // Release the query runner
    await queryRunner.release();
  }
}
