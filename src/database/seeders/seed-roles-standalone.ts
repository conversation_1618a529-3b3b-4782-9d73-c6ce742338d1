import { Permission } from '@hirenetix/models/roles/entities/permission.entity';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import AppDataSource from '@hirenetix/providers/database/postgres/data-source';
import { seedRolesAndPermissions } from './seed-roles';

/**
 * Standalone script to seed roles and permissions without NestJS or Commander.
 * This can be run directly with ts-node.
 */
async function main() {
  try {
    console.log('Initializing connection to database...');

    // Initialize the data source
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    // Check if data already exists to avoid duplicates
    const permissionCount =
      await AppDataSource.getRepository(Permission).count();
    const roleCount = await AppDataSource.getRepository(Role).count();

    if (permissionCount > 0 && roleCount > 0) {
      console.log('Roles and permissions already exist, skipping seed.');
      return;
    }

    // Run the seed function
    await seedRolesAndPermissions(AppDataSource);
    console.log('Database seeding completed successfully.');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    // Close the connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed.');
    }
  }
}

// Execute the script if it's called directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Unhandled error:', error);
      process.exit(1);
    });
}
