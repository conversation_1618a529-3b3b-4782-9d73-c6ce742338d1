import { DataSource } from 'typeorm';
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import { Permission } from '@hirenetix/models/roles/entities/permission.entity';
import { RolePermission } from '@hirenetix/models/roles/entities/role-permission.entity';
import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';
import { Logger } from '@nestjs/common';
import { SystemRoles } from '@hirenetix/models/roles/constants';
import AppDataSource from '@hirenetix/providers/database/postgres/data-source';

// Create a logger
const logger = new Logger('AssignTenantRolePermissions');

/**
 * Assigns permissions to a tenant-specific admin role
 * @param dataSource The TypeORM data source
 * @param tenantId The ID of the tenant (organization)
 * @param permissionModules Optional array of permission modules to filter permissions by
 */
export async function assignTenantRolePermissions(
  dataSource: DataSource,
  tenantId: string,
  permissionModules?: string[],
): Promise<void> {
  if (!tenantId) {
    throw new Error('Tenant ID is required');
  }

  // Make sure the datasource is initialized
  if (!dataSource.isInitialized) {
    await dataSource.initialize();
  }

  const queryRunner = dataSource.createQueryRunner();

  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Verify that the organization exists
    const organisation = await queryRunner.manager.findOne(Organisation, {
      where: { id: tenantId },
    });

    if (!organisation) {
      throw new Error(`Organization with ID ${tenantId} not found`);
    }

    logger.log(`Found organization: ${organisation.name} (${tenantId})`);

    // Get the tenant-specific admin role
    const tenantRole = await queryRunner.manager.findOne(Role, {
      where: {
        name: SystemRoles.ADMIN,
        tenantId: tenantId,
      },
    });

    if (!tenantRole) {
      throw new Error(
        `Admin role not found for tenant ${organisation.name} (${tenantId})`,
      );
    }

    logger.log(
      `Found admin role for tenant ${organisation.name}: ${tenantRole.id}`,
    );

    // Get permissions to assign
    let permissions: Permission[] = [];

    if (permissionModules && permissionModules.length > 0) {
      // Filter permissions by module
      permissions = await queryRunner.manager.find(Permission, {
        where: permissionModules.map((module) => ({ module })),
      });
      logger.log(
        `Found ${permissions.length} permissions in modules: ${permissionModules.join(', ')}`,
      );
    } else {
      // Get all permissions
      permissions = await queryRunner.manager.find(Permission);
      logger.log(`Found ${permissions.length} permissions to assign`);
    }

    if (permissions.length === 0) {
      logger.log('No permissions found to assign');
      await queryRunner.commitTransaction();
      return;
    }

    // Check if some permissions are already assigned
    const existingAssignments = await queryRunner.manager.find(RolePermission, {
      where: { roleId: tenantRole.id },
    });

    const existingPermissionIds = existingAssignments.map(
      (assignment) => assignment.permissionId,
    );

    // Filter out permissions that are already assigned
    const newPermissions = permissions.filter(
      (permission) => !existingPermissionIds.includes(permission.id),
    );

    if (newPermissions.length === 0) {
      logger.log('All permissions are already assigned to this role');
      await queryRunner.commitTransaction();
      return;
    }

    // Create role permission assignments
    const rolePermissions = newPermissions.map((permission) => {
      const rolePermission = new RolePermission();
      rolePermission.roleId = tenantRole.id;
      rolePermission.permissionId = permission.id;
      return rolePermission;
    });

    await queryRunner.manager.save(RolePermission, rolePermissions);
    logger.log(
      `Assigned ${rolePermissions.length} permissions to tenant role for ${organisation.name}`,
    );

    // Commit transaction
    await queryRunner.commitTransaction();
    logger.log(
      `Tenant role permissions assigned successfully for ${organisation.name}`,
    );
  } catch (error) {
    // Rollback transaction in case of error
    await queryRunner.rollbackTransaction();
    logger.error('Error assigning tenant role permissions:', error);
    throw error;
  } finally {
    // Release the query runner
    await queryRunner.release();
  }
}

/**
 * Standalone script to assign permissions to a tenant-specific admin role.
 * This can be run directly with ts-node.
 *
 * Usage:
 * npx ts-node -r tsconfig-paths/register src/database/seeders/assign-tenant-role-permissions.ts <tenantId> [module1,module2,...]
 *
 * Example:
 * npx ts-node -r tsconfig-paths/register src/database/seeders/assign-tenant-role-permissions.ts 123e4567-e89b-12d3-a456-426614174000 Dashboard,Positions
 */
async function main() {
  try {
    console.log('Initializing connection to database...');

    // Initialize the data source
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    // Get command line arguments
    const args = process.argv.slice(2);
    const tenantId = args[0];

    if (!tenantId) {
      console.error(
        'Please provide a tenant ID (organization ID) as the first argument',
      );
      process.exit(1);
    }

    // Parse modules if provided
    const moduleArg = args[1];
    const permissionModules = moduleArg ? moduleArg.split(',') : undefined;

    // Assign permissions to the tenant's admin role
    await assignTenantRolePermissions(
      AppDataSource,
      tenantId,
      permissionModules,
    );
    console.log('Permission assignment process completed successfully');
  } catch (error) {
    console.error('Error assigning permissions:', error);
    process.exit(1);
  } finally {
    // Close the connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed.');
    }
  }
}

// Execute the script if it's called directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Unhandled error:', error);
      process.exit(1);
    });
}
