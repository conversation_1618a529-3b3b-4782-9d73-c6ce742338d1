import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddQuestionIdAndConversationId1749706017911
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add question_id to job_post_messages table
    await queryRunner.addColumn(
      'messages',
      new TableColumn({
        name: 'question_id',
        type: 'varchar',
        isNullable: true,
      }),
    );

    // Add conversation_id to job_post_conversations table
    await queryRunner.addColumn(
      'conversations',
      new TableColumn({
        name: 'conversation_id',
        type: 'uuid',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove question_id from job_post_messages table
    await queryRunner.dropColumn('job_post_messages', 'question_id');

    // Remove conversation_id from job_post_conversations table
    await queryRunner.dropColumn('job_post_conversations', 'conversation_id');
  }
}
