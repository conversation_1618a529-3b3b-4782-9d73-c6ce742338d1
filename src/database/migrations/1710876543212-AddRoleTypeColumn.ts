import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRoleTypeColumn1710876543212 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum type
    await queryRunner.query(
      `CREATE TYPE "role_type_enum" AS ENUM ('location', 'corporate')`,
    );

    // Add role_type column with default value
    await queryRunner.query(
      `ALTER TABLE "roles" ADD COLUMN "role_type" role_type_enum NOT NULL DEFAULT 'corporate'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "role_type"`);
    await queryRunner.query(`DROP TYPE "role_type_enum"`);
  }
}
