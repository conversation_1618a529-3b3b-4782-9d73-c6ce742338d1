import { MigrationInterface, QueryRunner, Table, TableForeignKey } from "typeorm";

export class CreateFlowConditionsTable1747811930000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: "flow_conditions",
                columns: [
                    {
                        name: "id",
                        type: "serial",
                        isPrimary: true,
                    },
                    {
                        name: "source_question_id",
                        type: "int",
                        isNullable: false,
                    },
                    {
                        name: "trigger_answer_value",
                        type: "varchar",
                        length: "255",
                        isNullable: false,
                    },
                    {
                        name: "next_question_id",
                        type: "int",
                        isNullable: true,
                    },
                    {
                        name: "next_outcome_id",
                        type: "int",
                        isNullable: true,
                    },
                    {
                        name: "priority",
                        type: "int",
                        default: 0,
                        isNullable: false,
                    },
                ],
            }),
            true,
        );

        await queryRunner.createForeignKey(
            "flow_conditions",
            new TableForeignKey({
                columnNames: ["source_question_id"],
                referencedColumnNames: ["id"], // Assuming flow_questions primary key is 'id'
                referencedTableName: "flow_questions",
                onDelete: "CASCADE",
            }),
        );

        await queryRunner.createForeignKey(
            "flow_conditions",
            new TableForeignKey({
                columnNames: ["next_question_id"],
                referencedColumnNames: ["id"], // Assuming flow_questions primary key is 'id'
                referencedTableName: "flow_questions",
                onDelete: "SET NULL",
            }),
        );

        await queryRunner.createForeignKey(
            "flow_conditions",
            new TableForeignKey({
                columnNames: ["next_outcome_id"],
                referencedColumnNames: ["id"], // Assuming flow_outcomes primary key is 'id'
                referencedTableName: "flow_outcomes",
                onDelete: "SET NULL",
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable("flow_conditions");
        const sourceQuestionForeignKey = table.foreignKeys.find(
            (fk) => fk.columnNames.indexOf("source_question_id") !== -1,
        );
        const nextQuestionForeignKey = table.foreignKeys.find(
            (fk) => fk.columnNames.indexOf("next_question_id") !== -1,
        );
        const nextOutcomeForeignKey = table.foreignKeys.find(
            (fk) => fk.columnNames.indexOf("next_outcome_id") !== -1,
        );

        if (sourceQuestionForeignKey) {
            await queryRunner.dropForeignKey("flow_conditions", sourceQuestionForeignKey);
        }
        if (nextQuestionForeignKey) {
            await queryRunner.dropForeignKey("flow_conditions", nextQuestionForeignKey);
        }
        if (nextOutcomeForeignKey) {
            await queryRunner.dropForeignKey("flow_conditions", nextOutcomeForeignKey);
        }

        await queryRunner.dropTable("flow_conditions");
    }
} 