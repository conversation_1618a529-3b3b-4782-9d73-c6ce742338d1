import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTypeToSections1716400000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add type column to job_template_sections
    await queryRunner.query(`
      ALTER TABLE "job_template_sections"
      ADD COLUMN "type" VARCHAR(50) NOT NULL DEFAULT 'default'
    `);

    // Add type column to job_post_sections
    await queryRunner.query(`
      ALTER TABLE "job_post_sections"
      ADD COLUMN "type" VARCHAR(50) NOT NULL DEFAULT 'default'
    `);

    // Update existing job highlights sections
    await queryRunner.query(`
      UPDATE "job_template_sections"
      SET "type" = 'job_highlights'
      WHERE "title" = 'Job Highlights'
    `);

    await queryRunner.query(`
      UPDATE "job_post_sections"
      SET "type" = 'job_highlights'
      WHERE "title" = 'Job Highlights'
    `);

    // Update existing job details sections
    await queryRunner.query(`
      UPDATE "job_template_sections"
      SET "type" = 'job_details'
      WHERE "title" = 'Job Details'
    `);

    await queryRunner.query(`
      UPDATE "job_post_sections"
      SET "type" = 'job_details'
      WHERE "title" = 'Job Details'
    `);

    // Update existing qualifications sections
    await queryRunner.query(`
      UPDATE "job_template_sections"
      SET "type" = 'qualifications'
      WHERE "title" = 'Qualifications'
    `);

    await queryRunner.query(`
      UPDATE "job_post_sections"
      SET "type" = 'qualifications'
      WHERE "title" = 'Qualifications'
    `);

    // Update existing responsibilities sections
    await queryRunner.query(`
      UPDATE "job_template_sections"
      SET "type" = 'responsibilities'
      WHERE "title" = 'Responsibilities'
    `);

    await queryRunner.query(`
      UPDATE "job_post_sections"
      SET "type" = 'responsibilities'
      WHERE "title" = 'Responsibilities'
    `);

    // Update existing job description sections
    await queryRunner.query(`
      UPDATE "job_template_sections"
      SET "type" = 'job_description'
      WHERE "title" = 'Job Description'
    `);

    await queryRunner.query(`
      UPDATE "job_post_sections"
      SET "type" = 'job_description'
      WHERE "title" = 'Job Description'
    `);

    // Update existing benefits sections
    await queryRunner.query(`
      UPDATE "job_template_sections"
      SET "type" = 'benefits'
      WHERE "title" = 'Benefits'
    `);

    await queryRunner.query(`
      UPDATE "job_post_sections"
      SET "type" = 'benefits'
      WHERE "title" = 'Benefits'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove type column from job_template_sections
    await queryRunner.query(`
      ALTER TABLE "job_template_sections"
      DROP COLUMN "type"
    `);

    // Remove type column from job_post_sections
    await queryRunner.query(`
      ALTER TABLE "job_post_sections"
      DROP COLUMN "type"
    `);
  }
}
