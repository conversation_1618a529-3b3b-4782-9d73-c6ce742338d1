import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddProfileFieldsToJobApplicationInsights1750575250305
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE job_application_insights
      ADD COLUMN key_skills jsonb DEFAULT '{
        "technical": [],
        "soft": [],
        "languages": [],
        "certifications": [],
        "tools": []
      }'::jsonb,
      ADD COLUMN years_of_experience integer,
      ADD COLUMN education jsonb DEFAULT '[]'::jsonb,
      ADD COLUMN work_experience jsonb DEFAULT '[]'::jsonb,
      ADD COLUMN professional_summary jsonb DEFAULT '{
        "brief": null,
        "highlights": [],
        "expertise_level": null,
        "industry_focus": [],
        "career_highlights": []
      }'::jsonb,
      ADD COLUMN profile_generated_at timestamp with time zone
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE job_application_insights
      DROP COLUMN key_skills,
      DROP COLUMN years_of_experience,
      DROP COLUMN education,
      DROP COLUMN work_experience,
      DROP COLUMN professional_summary,
      DROP COLUMN profile_generated_at
    `);
  }
}
