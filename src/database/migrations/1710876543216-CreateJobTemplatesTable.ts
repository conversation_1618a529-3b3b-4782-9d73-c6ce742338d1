import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateJobTemplatesTable1710876543216
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE job_templates (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        template_name VARCHAR(255) NOT NULL,
        description TEXT,
        tenant_id UUID REFERENCES organisations(id) ON DELETE CASCADE,
        is_system_defined BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP TABLE IF EXISTS job_templates;
    `);
  }
}
