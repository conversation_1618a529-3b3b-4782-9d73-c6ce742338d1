import {
  MigrationInterface,
  QueryRunner,
  TableC<PERSON>umn,
  TableForeignKey,
} from 'typeorm';

export class CombinedRolesAndPermissionsUpdates1720000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 2. Add tenant_id column to roles table
    await queryRunner.addColumn(
      'roles',
      new TableColumn({
        name: 'tenant_id',
        type: 'uuid',
        isNullable: true,
      }),
    );

    // 3. Add foreign key from roles.tenant_id to organisations.id
    await queryRunner.createForeignKey(
      'roles',
      new TableForeignKey({
        columnNames: ['tenant_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'organisations',
        onDelete: 'CASCADE',
      }),
    );

    // 4. Add custom boolean column to roles table
    await queryRunner.addColumn(
      'roles',
      new TableColumn({
        name: 'custom',
        type: 'boolean',
        default: true,
        isNullable: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 4. Drop custom column from roles table
    await queryRunner.dropColumn('roles', 'custom');

    // 3. Drop foreign key from roles.tenant_id to organisations.id
    const table = await queryRunner.getTable('roles');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('tenant_id') !== -1,
    );
    await queryRunner.dropForeignKey('roles', foreignKey);

    // 2. Drop tenant_id column from roles table
    await queryRunner.dropColumn('roles', 'tenant_id');
  }
}
