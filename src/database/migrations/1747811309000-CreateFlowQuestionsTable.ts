import { MigrationInterface, QueryRunner, <PERSON>, TableF<PERSON><PERSON><PERSON><PERSON>, TableColumn } from "typeorm";

export enum QuestionTypeEnum {
    TEXT_INPUT = 'TEXT_INPUT',
    YES_NO = 'YES_NO',
    MULTI_SELECT = 'MULTI_SELECT',
    SINGLE_SELECT = 'SINGLE_SELECT',
    FILE_UPLOAD = 'FILE_UPLOAD',
    SCHEDULER = 'SCHEDULER',
    VIDEO_INTERVIEW = 'VIDEO_INTERVIEW',
    STATIC_MESSAGE = 'STATIC_MESSAGE',
}

export class CreateFlowQuestionsTable1747811309000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: "flow_questions",
                columns: [
                    {
                        name: "id",
                        type: "serial",
                        isPrimary: true,
                    },
                    {
                        name: "flow_id",
                        type: "int",
                        isNullable: false,
                    },
                    {
                        name: "question_type",
                        type: "enum",
                        enum: Object.values(QuestionTypeEnum),
                        isNullable: false,
                    },
                    {
                        name: "heading",
                        type: "text",
                        isNullable: false,
                    },
                    {
                        name: "sub_text",
                        type: "text",
                        isNullable: true,
                    },
                    {
                        name: "placeholder_text",
                        type: "varchar",
                        length: "255",
                        isNullable: true,
                    },
                    {
                        name: "options",
                        type: "jsonb",
                        isNullable: true,
                    },
                    {
                        name: "is_mandatory",
                        type: "boolean",
                        default: true,
                    },
                    {
                        name: "display_order",
                        type: "int",
                        isNullable: true,
                    },
                    {
                        name: "allowed_file_types",
                        type: "varchar",
                        length: "255",
                        isNullable: true,
                    },
                    {
                        name: "max_file_size_mb",
                        type: "int",
                        isNullable: true,
                    },
                    {
                        name: "message_style",
                        type: "varchar",
                        length: "50",
                        isNullable: true,
                    },
                ],
            }),
            true,
        );

        await queryRunner.createForeignKey(
            "flow_questions",
            new TableForeignKey({
                columnNames: ["flow_id"],
                referencedColumnNames: ["id"], // Assuming conversational_flows primary key is 'id'
                referencedTableName: "conversational_flows",
                onDelete: "CASCADE",
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable("flow_questions");
        const flowForeignKey = table.foreignKeys.find(
            (fk) => fk.columnNames.indexOf("flow_id") !== -1,
        );

        if (flowForeignKey) {
            await queryRunner.dropForeignKey("flow_questions", flowForeignKey);
        }

        await queryRunner.dropTable("flow_questions");
    }
} 