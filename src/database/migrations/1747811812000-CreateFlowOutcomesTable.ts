import { MigrationInterface, QueryRunner, Table, TableForeignKey } from "typeorm";

export enum OutcomeTypeEnum {
    REJECT = 'REJECT',
    PROCEED_TO_REVIEW = 'PROCEED_TO_REVIEW',
    THANK_YOU_COMPLETE = 'THANK_YOU_COMPLETE',
    SCHEDULE_INTERVIEW_REQUESTED = 'SCHEDULE_INTERVIEW_REQUESTED',
}

export class CreateFlowOutcomesTable1747811812000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: "flow_outcomes",
                columns: [
                    {
                        name: "id",
                        type: "serial",
                        isPrimary: true,
                    },
                    {
                        name: "flow_id",
                        type: "int",
                        isNullable: false,
                    },
                    {
                        name: "outcome_type",
                        type: "enum",
                        enum: Object.values(OutcomeTypeEnum),
                        isNullable: false,
                    },
                    {
                        name: "message_text",
                        type: "text",
                        isNullable: true, // Assuming message_text can be optional
                    },
                    {
                        name: "is_default_reject",
                        type: "boolean",
                        default: false,
                    },
                    {
                        name: "is_default_success",
                        type: "boolean",
                        default: false,
                    },
                ],
            }),
            true,
        );

        await queryRunner.createForeignKey(
            "flow_outcomes",
            new TableForeignKey({
                columnNames: ["flow_id"],
                referencedColumnNames: ["id"], // Assuming conversational_flows primary key is 'id'
                referencedTableName: "conversational_flows",
                onDelete: "CASCADE",
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable("flow_outcomes");
        const flowForeignKey = table.foreignKeys.find(
            (fk) => fk.columnNames.indexOf("flow_id") !== -1,
        );

        if (flowForeignKey) {
            await queryRunner.dropForeignKey("flow_outcomes", flowForeignKey);
        }

        await queryRunner.dropTable("flow_outcomes");
    }
} 