import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPublishedToJobPosts1748000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add published column to job_posts table
    await queryRunner.query(`
      ALTER TABLE "job_posts"
      ADD COLUMN "is_active" BOOLEAN NOT NULL DEFAULT true
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove published column from job_posts table
    await queryRunner.query(`
      ALTER TABLE "job_posts"
      DROP COLUMN "is_active"
    `);
  }
}
