import { MigrationInterface, QueryRunner, Table, TableForeignKey } from "typeorm";

export class CreateConversationalFlowsTable1747811040000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: "conversational_flows",
                columns: [
                    {
                        name: "id",
                        type: "serial",
                        isPrimary: true,
                    },
                    {
                        name: "name",
                        type: "varchar",
                        length: "255",
                        isNullable: false,
                    },
                    {
                        name: "description",
                        type: "text",
                        isNullable: true,
                    },
                    {
                        name: "job_post_id",
                        type: "uuid",
                        isNullable: true,
                    },
                    {
                        name: "start_question_id",
                        type: "int",
                        isNullable: true,
                    },
                    {
                        name: "created_at",
                        type: "timestamptz",
                        default: "CURRENT_TIMESTAMP",
                    },
                    {
                        name: "updated_at",
                        type: "timestamptz",
                        default: "CURRENT_TIMESTAMP",
                    },
                ],
            }),
            true,
        );

        await queryRunner.createForeignKey(
            "conversational_flows",
            new TableForeignKey({
                columnNames: ["job_post_id"],
                referencedColumnNames: ["id"],
                referencedTableName: "job_posts",
                onDelete: "SET NULL",
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable("conversational_flows");
        const jobPostForeignKey = table.foreignKeys.find(
            (fk) => fk.columnNames.indexOf("job_post_id") !== -1,
        );
        if (jobPostForeignKey) {
            await queryRunner.dropForeignKey("conversational_flows", jobPostForeignKey);
        } 
        await queryRunner.dropTable("conversational_flows");
    }
} 