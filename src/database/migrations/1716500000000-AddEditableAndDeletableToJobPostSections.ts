import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEditableAndDeletableToJobPostSections1716500000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "job_post_sections"
      ADD COLUMN "is_editable" BOOLEAN NOT NULL DEFAULT TRUE,
      ADD COLUMN "is_deletable" BOOLEAN NOT NULL DEFAULT TRUE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "job_post_sections"
      DROP COLUMN "is_editable",
      DROP COLUMN "is_deletable"
    `);
  }
}
