import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOtpFieldsToUser1690000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "users" 
      ADD COLUMN IF NOT EXISTS "otp_code" varchar(10),
      ADD COLUMN IF NOT EXISTS "otp_expires_at" timestamp
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "users" 
      DROP COLUMN IF EXISTS "otp_code",
      DROP COLUMN IF EXISTS "otp_expires_at"
    `);
  }
}
