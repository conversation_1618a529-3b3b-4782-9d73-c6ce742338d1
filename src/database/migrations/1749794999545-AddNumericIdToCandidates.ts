import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNumericIdToCandidates1749794999545
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add numeric_id column
    await queryRunner.query(`
      ALTER TABLE candidates 
      ADD COLUMN candidate_id SERIAL;
    `);

    // Update existing records with sequential numbers
    await queryRunner.query(`
      UPDATE candidates 
      SET candidate_id = subquery.row_num
      FROM (
        SELECT id, ROW_NUMBER() OVER (ORDER BY created_at) as row_num
        FROM candidates
      ) AS subquery
      WHERE candidates.id = subquery.id;
    `);

    // Make candidate_id NOT NULL after populating existing records
    await queryRunner.query(`
      ALTER TABLE candidates 
      ALTER COLUMN candidate_id SET NOT NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE candidates 
      DROP COLUMN candidate_id;
    `);
  }
}
