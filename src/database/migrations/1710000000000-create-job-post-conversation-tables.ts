import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class CreateJobPostConversationTables1710000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create job_post_conversations table
    await queryRunner.createTable(
      new Table({
        name: 'job_post_conversations',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'user_id',
            type: 'uuid',
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['in_progress', 'completed', 'cancelled'],
            default: "'in_progress'",
          },
          {
            name: 'current_step',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create job_post_messages table
    await queryRunner.createTable(
      new Table({
        name: 'job_post_messages',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'session_id',
            type: 'uuid',
          },
          {
            name: 'step_id',
            type: 'varchar',
          },
          {
            name: 'user_response',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'job_agent_response',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'outcome',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Add foreign key for job_post_messages.session_id
    await queryRunner.createForeignKey(
      'job_post_messages',
      new TableForeignKey({
        columnNames: ['session_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'job_post_conversations',
        onDelete: 'CASCADE',
      }),
    );

    // Add foreign key for job_post_conversations.user_id
    await queryRunner.createForeignKey(
      'job_post_conversations',
      new TableForeignKey({
        columnNames: ['user_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys first
    const jobPostMessagesTable =
      await queryRunner.getTable('job_post_messages');
    const jobPostConversationTable = await queryRunner.getTable(
      'job_post_conversations',
    );

    const messagesForeignKey = jobPostMessagesTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('session_id') !== -1,
    );
    const conversationForeignKey = jobPostConversationTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('user_id') !== -1,
    );

    await queryRunner.dropForeignKey('job_post_messages', messagesForeignKey);
    await queryRunner.dropForeignKey(
      'job_post_conversations',
      conversationForeignKey,
    );

    // Drop tables
    await queryRunner.dropTable('job_post_messages');
    await queryRunner.dropTable('job_post_conversations');
  }
}
