import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddGenesisFieldToConversationalFlows1747824330000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.addColumn(
            "conversational_flows",
            new TableColumn({
                name: "genesis",
                type: "text",
                isNullable: true,
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn("conversational_flows", "genesis");
    }
} 