import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateJobTemplateSectionsTable1710876543217
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE job_template_sections (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        template_id UUID NOT NULL REFERENCES job_templates(id) ON DELETE CASCADE,
        section_definition_id UUID NOT NULL REFERENCES section_definitions(id),
        parent_section_id UUID REFERENCES job_template_sections(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        content JSONB,
        display_order INT NOT NULL,
        is_visible_default BOOLEAN DEFAULT TRUE,
        is_editable BOOLEAN DEFAULT TRUE,
        is_deletable BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP TABLE IF EXISTS job_template_sections;
    `);
  }
}
