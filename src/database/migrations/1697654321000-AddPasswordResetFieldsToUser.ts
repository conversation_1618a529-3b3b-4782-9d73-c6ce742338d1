import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPasswordResetFieldsToUser1697654321000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "users" 
      ADD COLUMN IF NOT EXISTS "password_reset_token" varchar(100),
      ADD COLUMN IF NOT EXISTS "password_reset_expires" timestamp
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "users" 
      DROP COLUMN IF EXISTS "password_reset_token",
      DROP COLUMN IF EXISTS "password_reset_expires"
    `);
  }
}
