import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddConversationIdToJobApplications1718457709451
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'job_applications',
      new TableColumn({
        name: 'conversation_id',
        type: 'uuid',
        isNullable: true,
        comment:
          'UUID of the conversation associated with this job application',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('job_applications', 'conversation_id');
  }
}
