import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';
import { JobPostStatus } from '../../models/jobs/constants/job.constants';

export class CreateJobPostsAndSectionsTable1716200000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create job_posts table
    await queryRunner.createTable(
      new Table({
        name: 'job_posts',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'original_template_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'enum',
            enum: Object.values(JobPostStatus),
            default: `'${JobPostStatus.DRAFT}'`,
          },
          {
            name: 'published_at',
            type: 'timestamptz',
            isNullable: true,
          },
          {
            name: 'tenant_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'created_by_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'location_id',
            type: 'uuid',
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create job_post_sections table
    await queryRunner.createTable(
      new Table({
        name: 'job_post_sections',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'job_post_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'section_definition_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'original_template_section_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'content',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'display_order',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'is_visible',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Add foreign key constraints
    await queryRunner.createForeignKey(
      'job_posts',
      new TableForeignKey({
        columnNames: ['original_template_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'job_templates',
        onDelete: 'SET NULL',
      }),
    );

    await queryRunner.createForeignKey(
      'job_posts',
      new TableForeignKey({
        columnNames: ['tenant_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'organisations',
        onDelete: 'SET NULL',
      }),
    );

    await queryRunner.createForeignKey(
      'job_posts',
      new TableForeignKey({
        columnNames: ['created_by_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'RESTRICT',
      }),
    );

    await queryRunner.createForeignKey(
      'job_posts',
      new TableForeignKey({
        columnNames: ['location_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'locations',
        onDelete: 'RESTRICT',
      }),
    );

    await queryRunner.createForeignKey(
      'job_post_sections',
      new TableForeignKey({
        columnNames: ['job_post_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'job_posts',
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'job_post_sections',
      new TableForeignKey({
        columnNames: ['section_definition_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'section_definitions',
        onDelete: 'RESTRICT',
      }),
    );

    await queryRunner.createForeignKey(
      'job_post_sections',
      new TableForeignKey({
        columnNames: ['original_template_section_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'job_template_sections',
        onDelete: 'SET NULL',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('job_post_sections');
    await queryRunner.dropTable('job_posts');
  }
}
