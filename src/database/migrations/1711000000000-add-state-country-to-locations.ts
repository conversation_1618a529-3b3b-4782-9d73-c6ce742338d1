import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddStateCountryToLocations1711000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('locations', [
      new TableColumn({
        name: 'state',
        type: 'varchar',
        length: '100',
        isNullable: true,
      }),
      new TableColumn({
        name: 'country',
        type: 'varchar',
        length: '100',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns('locations', ['state', 'country']);
  }
}
