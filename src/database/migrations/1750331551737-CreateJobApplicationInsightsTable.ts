import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeign<PERSON>ey,
  TableColumn,
} from 'typeorm';
import { JOB_APPLICATION_TABLE_NAME } from '../../models/jobs/constants/job.constants';

export class CreateJobApplicationInsightsTable1750331551737
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create new job_application_insights table
    await queryRunner.createTable(
      new Table({
        name: 'job_application_insights',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'gen_random_uuid()',
          },
          {
            name: 'job_application_id',
            type: 'uuid',
            isUnique: true,
          },
          {
            name: 'match_percentage',
            type: 'decimal',
            precision: 5,
            scale: 2,
            isNullable: true,
          },
          {
            name: 'matching_keywords',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'summary',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'smallint',
            default: '0',
            comment: '0: pending, 1: processing, 2: completed, 3: failed',
          },
          {
            name: 'generated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Add foreign key constraint
    await queryRunner.createForeignKey(
      'job_application_insights',
      new TableForeignKey({
        columnNames: ['job_application_id'],
        referencedColumnNames: ['id'],
        referencedTableName: JOB_APPLICATION_TABLE_NAME,
        onDelete: 'CASCADE',
      }),
    );

    // Check if the columns exist before trying to drop them
    const table = await queryRunner.getTable(JOB_APPLICATION_TABLE_NAME);
    const columnsToRemove = [
      'ai_insights',
      'ai_insights_generated_at',
      'ai_insights_status',
    ];

    for (const columnName of columnsToRemove) {
      const column = table?.findColumnByName(columnName);
      if (column) {
        await queryRunner.dropColumn(JOB_APPLICATION_TABLE_NAME, columnName);
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Recreate the columns in job_applications table
    await queryRunner.addColumns(JOB_APPLICATION_TABLE_NAME, [
      new TableColumn({
        name: 'ai_insights',
        type: 'jsonb',
        isNullable: true,
      }),
      new TableColumn({
        name: 'ai_insights_generated_at',
        type: 'timestamp',
        isNullable: true,
      }),
      new TableColumn({
        name: 'ai_insights_status',
        type: 'smallint',
        default: '0',
      }),
    ]);

    // Drop the job_application_insights table
    await queryRunner.dropTable('job_application_insights', true);
  }
}
