import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateConversationsAndMessages1749306000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create conversation status enum
    await queryRunner.query(`
      CREATE TYPE conversation_status AS ENUM ('active', 'closed');
    `);

    // Create message type enum
    await queryRunner.query(`
      CREATE TYPE message_type AS ENUM ('text', 'audio', 'image', 'file');
    `);

    // Create conversations table
    await queryRunner.query(`
      CREATE TABLE conversations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        candidate_id UUID REFERENCES candidates(id) ON DELETE CASCADE,
        status conversation_status DEFAULT 'active',
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ended_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      CREATE INDEX idx_conversations_candidate_id ON conversations(candidate_id);
    `);

    // Create messages table
    await queryRunner.query(`
      CREATE TABLE messages (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
        sender_id UUID REFERENCES candidates(id),
        receiver_id UUID REFERENCES candidates(id),
        message_type message_type DEFAULT 'text',
        content TEXT NOT NULL,
        metadata JSONB,
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CHECK (
          sender_id IS NOT NULL OR
          sender_id IS NULL
        ),
        CHECK (
          receiver_id IS NOT NULL OR
          receiver_id IS NULL
        )
      );

      CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
      CREATE INDEX idx_messages_sender_id ON messages(sender_id);
      CREATE INDEX idx_messages_receiver_id ON messages(receiver_id);
    `);

    // Create trigger for updated_at
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';

      CREATE TRIGGER update_conversations_updated_at
        BEFORE UPDATE ON conversations
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_messages_updated_at
        BEFORE UPDATE ON messages
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS update_messages_updated_at ON messages;
      DROP TRIGGER IF EXISTS update_conversations_updated_at ON conversations;
      DROP FUNCTION IF EXISTS update_updated_at_column;
      DROP TABLE IF EXISTS messages;
      DROP TABLE IF EXISTS conversations;
      DROP TYPE IF EXISTS message_type;
      DROP TYPE IF EXISTS conversation_status;
    `);
  }
}
