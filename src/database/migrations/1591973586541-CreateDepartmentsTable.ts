import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class CreateDepartmentsTable1591973586541 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create departments table
    await queryRunner.createTable(
      new Table({
        name: 'departments',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'gen_random_uuid()',
          },
          {
            name: 'organisation_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Add foreign key for organisation_id
    await queryRunner.createForeignKey(
      'departments',
      new TableForeignKey({
        columnNames: ['organisation_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'organisations',
        onDelete: 'CASCADE',
      }),
    );

    // Add trigger for updated_at timestamp
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_timestamp()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER update_departments_timestamp
      BEFORE UPDATE ON departments
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop trigger
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS update_departments_timestamp ON departments;
    `);

    // Drop foreign key
    const table = await queryRunner.getTable('departments');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('organisation_id') !== -1,
    );
    await queryRunner.dropForeignKey('departments', foreignKey);

    // Drop table
    await queryRunner.dropTable('departments');
  }
}
