import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';
import { JOB_APPLICATION_TABLE_NAME } from '../../models/jobs/constants/job.constants';

export class CreateJobApplicationsTable1749700227854
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: JOB_APPLICATION_TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'gen_random_uuid()',
          },
          {
            name: 'candidate_id',
            type: 'uuid',
          },
          {
            name: 'job_id',
            type: 'uuid',
          },
          {
            name: 'resume_url',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'enum',
            enum: [
              'applied',
              'shortlisted',
              'incomplete',
              'interview_scheduled',
              'hired',
              'rejected',
            ],
            default: "'incomplete'",
          },
          {
            name: 'organisation_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Add foreign key constraints
    await queryRunner.createForeignKey(
      JOB_APPLICATION_TABLE_NAME,
      new TableForeignKey({
        columnNames: ['candidate_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'candidates',
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      JOB_APPLICATION_TABLE_NAME,
      new TableForeignKey({
        columnNames: ['job_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'job_posts',
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      JOB_APPLICATION_TABLE_NAME,
      new TableForeignKey({
        columnNames: ['organisation_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'organisations',
        onDelete: 'SET NULL',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable(JOB_APPLICATION_TABLE_NAME);
    if (table) {
      const foreignKeys = table.foreignKeys;
      for (const foreignKey of foreignKeys) {
        await queryRunner.dropForeignKey(
          JOB_APPLICATION_TABLE_NAME,
          foreignKey,
        );
      }
    }
    await queryRunner.dropTable(JOB_APPLICATION_TABLE_NAME);
  }
}
