import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateSectionDefinitionsTable1710876543214
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop existing table if it exists
    const existingTable = await queryRunner.getTable('section_definitions');
    if (existingTable) {
      await queryRunner.dropTable('section_definitions');
    }

    // Create the table
    await queryRunner.createTable(
      new Table({
        name: 'section_definitions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'gen_random_uuid()',
          },
          {
            name: 'default_name',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'is_system_defined',
            type: 'boolean',
            default: true,
          },
          {
            name: 'tenant_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'default_values',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Insert default section definitions
    await queryRunner.query(`
      INSERT INTO section_definitions (default_name, is_system_defined) VALUES
        ('Job Details', true),
        ('Job Highlights', true),
        ('Qualifications', true),
        ('Benefits', true),
        ('Responsibilities', true),
        ('Job Description', true);

      INSERT INTO section_definitions (default_name, is_system_defined, default_values)
        VALUES ('Job Type', true, '["Full Time", "Part Time"]');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('section_definitions');
    if (table) {
      await queryRunner.dropTable('section_definitions');
    }
  }
}
