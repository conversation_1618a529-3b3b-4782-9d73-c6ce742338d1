import { DocumentBuilder, SwaggerCustomOptions } from '@nestjs/swagger';
import { API_TAGS } from '../../common/constants/swagger.constants';

/**
 * Swagger document configuration
 * @returns DocumentBuilder instance
 */
export const createSwaggerConfig = () => {
  const NODE_ENV = process.env.NODE_ENV || 'local';

  const serverUrls = {
    local: `http://localhost:${process.env.PORT}`,
    development: 'http://ec2-43-204-242-190.ap-south-1.compute.amazonaws.com',
  };

  return new DocumentBuilder()
    .setTitle('HireNetix API')
    .setDescription('The HireNetix API documentation')
    .setVersion('1.0.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addTag(API_TAGS.AUTH)
    .addTag(API_TAGS.USERS)
    .addTag(API_TAGS.ORGANISATIONS)
    .addTag(API_TAGS.ROLES)
    .addServer(serverUrls[NODE_ENV])
    .build();
};

/**
 * Swagger UI custom options
 */
export const swaggerCustomOptions: SwaggerCustomOptions = {
  swaggerOptions: {
    persistAuthorization: true,
    authAction: {
      'JWT-auth': {
        name: 'JWT-auth',
        schema: {
          type: 'http',
          in: 'header',
          name: 'Authorization',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
        value: 'Bearer <your_token_here>',
      },
    },
    docExpansion: 'none',
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
    deepLinking: true,
  },
  customCssUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.12.0/swagger-ui.min.css',
  customJs: [
    'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.12.0/swagger-ui-bundle.js',
    'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.12.0/swagger-ui-standalone-preset.js',
  ],
};
