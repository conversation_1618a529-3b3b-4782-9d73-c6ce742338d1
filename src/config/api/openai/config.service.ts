import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class OpenAIConfigService {
  constructor(private configService: ConfigService) {}

  get apiUrl(): string {
    return this.configService.get<string>('OPENAI_API_URL');
  }

  get apiKey(): string {
    return this.configService.get<string>('OPENAI_API_KEY');
  }

  get model(): string {
    return this.configService.get<string>('OPENAI_MODEL');
  }
}
