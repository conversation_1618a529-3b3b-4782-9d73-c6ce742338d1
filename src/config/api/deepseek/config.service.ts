import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class DeepseekConfigService {
  constructor(private configService: ConfigService) {}

  get apiUrl(): string {
    return (
      this.configService.get<string>('DEEPSEEK_API_URL') ||
      'https://api.deepseek.ai/v1/chat/completions'
    );
  }

  get apiKey(): string {
    return this.configService.get<string>('DEEPSEEK_API_KEY');
  }
}
