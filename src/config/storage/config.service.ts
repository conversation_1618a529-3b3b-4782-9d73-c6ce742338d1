import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class StorageConfigService {
  constructor(private configService: ConfigService) {}

  get region(): string {
    return this.configService.get<string>('storage.s3.region');
  }

  get accessKeyId(): string {
    return this.configService.get<string>('storage.s3.accessKeyId');
  }

  get secretAccessKey(): string {
    return this.configService.get<string>('storage.s3.secretAccessKey');
  }

  get bucket(): string {
    return this.configService.get<string>('storage.s3.bucket');
  }

  get endpoint(): string | undefined {
    return this.configService.get<string | undefined>('storage.s3.endpoint');
  }
}
