import { applyDecorators, Type } from '@nestjs/common';
import {
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiProperty,
  ApiPropertyOptions,
  getSchemaPath,
} from '@nestjs/swagger';

/**
 * Standard API operation decorator with improved documentation
 * @param summary Summary of the operation
 * @param description Detailed description of the operation
 */
export const ApiOperationDoc = (summary: string, description: string) => {
  return applyDecorators(
    ApiOperation({
      summary,
      description,
    }),
  );
};

/**
 * API Success response decorator with model schema reference
 * @param statusCode HTTP status code
 * @param description Response description
 * @param model Response model class
 * @param isArray Whether the response is an array
 */
export const ApiSuccessResponse = <T extends Type<any>>(
  statusCode: number,
  description: string,
  model?: T,
  isArray = false,
) => {
  if (!model) {
    return applyDecorators(
      ApiResponse({
        status: statusCode,
        description,
      }),
    );
  }

  return applyDecorators(
    ApiExtraModels(model),
    ApiResponse({
      status: statusCode,
      description,
      schema: {
        allOf: [
          {
            properties: {
              statusCode: {
                type: 'number',
                example: statusCode,
              },
              message: {
                type: 'string',
                example: description,
              },
              data: isArray
                ? {
                    type: 'array',
                    items: { $ref: getSchemaPath(model) },
                  }
                : {
                    $ref: getSchemaPath(model),
                  },
            },
          },
        ],
      },
    }),
  );
};

/**
 * API Error response decorator
 * @param statusCode HTTP status code
 * @param description Error description
 * @param errorExample Example error response
 */
export const ApiErrorResponse = (
  statusCode: number,
  description: string,
  errorExample?: Record<string, any>,
) => {
  return applyDecorators(
    ApiResponse({
      status: statusCode,
      description,
      schema: {
        properties: {
          statusCode: {
            type: 'number',
            example: statusCode,
          },
          message: {
            type: 'string',
            example: description,
          },
          error: {
            type: 'string',
            example: description,
          },
          ...(errorExample || {}),
        },
      },
    }),
  );
};

/**
 * Standard model property with improved documentation
 * Combines ApiProperty with class-validator decorators
 * @param options ApiPropertyOptions with additional validation options
 */
export const ApiModelProperty = (options: ApiPropertyOptions) => {
  return applyDecorators(ApiProperty(options));
};
