import { SetMetadata } from '@nestjs/common';
import {
  PermissionActions,
  PermissionModules,
} from '@hirenetix/models/roles/constants/role.constants';
import { PERMISSIONS_KEY } from '@hirenetix/common/constants';

export interface RequiredPermission {
  module: PermissionModules;
  action: PermissionActions;
}

export const RequirePermissions = (...permissions: RequiredPermission[]) =>
  SetMetadata(PERMISSIONS_KEY, permissions);
