import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { SystemRoles } from '@hirenetix/models/roles/constants';

@Injectable()
export class OrganizationRestrictionGuard implements CanActivate {
  constructor() {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // If no user info, deny access
    if (!user || !user.id) {
      return false;
    }

    // Super admins can access any organization
    if (user.roles && user.roles.includes(SystemRoles.SUPER_ADMIN)) {
      return true;
    }

    // If there's no organization restriction flag set by the PermissionsGuard
    // then we don't need to enforce organization restrictions
    if (!request.hasOrgRestriction) {
      return true;
    }

    // Get the target organization ID from the request body
    const targetOrgId = request.body.organisationId;

    // If no organization ID in the request, allow (will be handled by validation)
    if (!targetOrgId) {
      return true;
    }

    // Check if user is trying to access their own organization
    if (user.tenantId !== targetOrgId) {
      throw new ForbiddenException(
        'You can only perform actions within your own organization',
      );
    }

    return true;
  }
}
