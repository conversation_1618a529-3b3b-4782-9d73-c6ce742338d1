import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { SystemRoles } from '@hirenetix/models/roles/constants';
import { RolesRepository } from '@hirenetix/models/roles/repositories/roles.repository';

@Injectable()
export class RoleAssignmentRestrictionGuard implements CanActivate {
  constructor(private readonly rolesRepository: RolesRepository) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // If no user info, deny access
    if (!user || !user.id) {
      return false;
    }

    // Super admins can assign any role
    if (user.roles && user.roles.includes(SystemRoles.SUPER_ADMIN)) {
      return true;
    }

    // Get the target role ID from the request body
    const roleId = request.body.roleId;

    // If no role is being assigned, allow
    if (!roleId) {
      return true;
    }

    // Find role by ID
    const role = await this.rolesRepository.findById(roleId);

    // If role not found, allow (will be handled by validation)
    if (!role) {
      return true;
    }

    // Only super admins can assign admin roles
    if (
      role.name === SystemRoles.ADMIN ||
      role.name === SystemRoles.SUPER_ADMIN
    ) {
      throw new ForbiddenException(
        'Only super administrators can assign admin roles',
      );
    }

    return true;
  }
}
