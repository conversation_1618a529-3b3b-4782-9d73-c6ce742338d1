import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/metadata/roles.decorator';
import { SystemRoles } from '@hirenetix/models/roles/constants';
import { UserRoleAssignmentsRepository } from '@hirenetix/models/roles/repositories/user-role-assignments.repository';

@Injectable()
export class RolesGuard implements CanActivate {
  private readonly logger = new Logger(RolesGuard.name);

  constructor(
    private reflector: Reflector,
    private userRoleAssignmentsRepository: UserRoleAssignmentsRepository,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredRoles = this.reflector.getAllAndOverride<SystemRoles[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredRoles || requiredRoles.length === 0) {
      return true; // No roles required, allow access
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.id) {
      throw new UnauthorizedException('User not authenticated');
    }

    this.logger.debug(`Required roles: ${requiredRoles}`);
    this.logger.debug(`User from JWT: ${JSON.stringify(user)}`);

    // First check if roles are directly included in the JWT token
    if (user.roles && Array.isArray(user.roles)) {
      const hasRequiredRoleFromToken = requiredRoles.some((role) =>
        user.roles.includes(role),
      );

      if (hasRequiredRoleFromToken) {
        return true;
      }
    }

    // If not, check roles from the database
    const userRoleAssignments =
      await this.userRoleAssignmentsRepository.getUserRoles(user.id);

    if (!userRoleAssignments || userRoleAssignments.length === 0) {
      this.logger.debug('No roles assigned to the user in the database');
      return false; // No roles assigned to the user
    }

    const userRoles = userRoleAssignments.map(
      (userRoleAssignment) => userRoleAssignment.role.name,
    );

    this.logger.debug(`User roles from DB: ${userRoles}`);

    // Check if the user has any of the required roles
    const hasRequiredRole = requiredRoles.some((role) =>
      userRoles.includes(role),
    );

    return hasRequiredRole;
  }
}
