import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRoleAssignmentsRepository } from '@hirenetix/models/roles/repositories/user-role-assignments.repository';
import { SystemRoles } from '@hirenetix/models/roles/constants';
import { RequiredPermission } from '../decorators/metadata/permissions.decorator';
import { PERMISSIONS_KEY } from '../constants';
import { PermissionsRepository } from '@hirenetix/models/roles/repositories/permissions.repository';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly userRoleAssignmentsRepository: UserRoleAssignmentsRepository,
    private readonly permissionRepository: PermissionsRepository,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const requiredPermissions = this.reflector.getAllAndOverride<
      RequiredPermission[]
    >(PERMISSIONS_KEY, [context.getHandler(), context.getClass()]);

    console.log('requiredPermissions :', requiredPermissions);
    // If no permissions are required, allow access
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // If no user or no user ID, deny access
    if (!user || !user.id) {
      return false;
    }

    // Get user role assignments with related data
    const userRoleAssignments =
      await this.userRoleAssignmentsRepository.getUserRoles(user.id);

    // If no roles found, deny access
    if (!userRoleAssignments || userRoleAssignments.length === 0) {
      return false;
    }

    // Extract roles
    const roles = userRoleAssignments.map((assignment) => assignment.role);

    // Super admin has all permissions
    if (roles.some((role) => role?.name === SystemRoles.SUPER_ADMIN)) {
      return true;
    }

    //Get permission id from database
    const permissionId = await this.permissionRepository.findByName(
      requiredPermissions[0].action,
    );

    //Check if user has permission
    const hasPermission = userRoleAssignments.some((assignment) => {
      return assignment.role.rolePermissions.some((permission) => {
        return permission.permission.name === permissionId.name;
      });
    });

    if (!hasPermission) {
      throw new ForbiddenException(
        'You do not have the required permissions for this action',
      );
    }

    return true;
  }
}
