import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  TextractClient,
  StartDocumentTextDetectionCommand,
  GetDocumentTextDetectionCommand,
} from '@aws-sdk/client-textract';
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import * as fs from 'fs';

@Injectable()
export class TextractService {
  private readonly textractClient: TextractClient;
  private readonly s3Client: S3Client;

  constructor(private readonly configService: ConfigService) {
    const region = this.configService.get<string>('AWS_REGION');
    const credentials = {
      accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
      secretAccessKey: this.configService.get<string>('AWS_SECRET_ACCESS_KEY'),
    };

    this.textractClient = new TextractClient({ region, credentials });
    this.s3Client = new S3Client({ region, credentials });
  }

  async extractText(file: { path: string }): Promise<string> {
    try {
      const fileBuffer = fs.readFileSync(file.path);

      // For asynchronous processing (files over 5MB)
      return this.processAsynchronously(fileBuffer);
    } catch (error) {
      throw new BadRequestException(`Failed to extract text: ${error.message}`);
    }
  }

  private async processAsynchronously(fileBuffer: Buffer): Promise<string> {
    const bucket = this.configService.get<string>('AWS_S3_BUCKET');
    const key = `temp-${Date.now()}.pdf`;

    try {
      // Upload file to S3
      await this.s3Client.send(
        new PutObjectCommand({
          Bucket: bucket,
          Key: key,
          Body: fileBuffer,
        }),
      );

      // Start async job
      const startCommand = new StartDocumentTextDetectionCommand({
        DocumentLocation: {
          S3Object: {
            Bucket: bucket,
            Name: key,
          },
        },
      });

      const startResponse = await this.textractClient.send(startCommand);
      if (!startResponse.JobId) {
        throw new Error('Failed to start text detection job');
      }

      // Poll for completion
      let isCompleted = false;
      let result = '';
      while (!isCompleted) {
        const getCommand = new GetDocumentTextDetectionCommand({
          JobId: startResponse.JobId,
        });

        const response = await this.textractClient.send(getCommand);

        if (response.JobStatus === 'SUCCEEDED') {
          result = this.concatenateBlocks(response.Blocks);
          isCompleted = true;
        } else if (response.JobStatus === 'FAILED') {
          throw new Error('Text detection job failed');
        } else {
          // Wait before polling again
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
      console.log('result :', result);

      return result;
    } finally {
      // Clean up: Delete the temporary file from S3
      try {
        await this.s3Client.send(
          new DeleteObjectCommand({
            Bucket: bucket,
            Key: key,
          }),
        );
      } catch (error) {
        console.error('Failed to delete temporary file from S3:', error);
      }
    }
  }

  async extractTextFromResume(fileName: string): Promise<string> {
    const bucket = this.configService.get<string>('AWS_S3_BUCKET');

    try {
      // Start async job
      const startCommand = new StartDocumentTextDetectionCommand({
        DocumentLocation: {
          S3Object: {
            Bucket: bucket,
            Name: fileName,
          },
        },
      });
      const startResponse = await this.textractClient.send(startCommand);
      if (!startResponse.JobId) {
        throw new Error('Failed to start text detection job');
      }

      // Poll for completion
      let isCompleted = false;
      let result = '';
      while (!isCompleted) {
        const getCommand = new GetDocumentTextDetectionCommand({
          JobId: startResponse.JobId,
        });

        const response = await this.textractClient.send(getCommand);

        if (response.JobStatus === 'SUCCEEDED') {
          result = this.concatenateBlocks(response.Blocks);
          isCompleted = true;
        } else if (response.JobStatus === 'FAILED') {
          throw new Error('Text detection job failed');
        } else {
          // Wait before polling again
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      return result;
    } catch (error) {
      throw new BadRequestException(`Failed to extract text: ${error.message}`);
    }
  }

  private concatenateBlocks(blocks: any[]): string {
    if (!blocks) return '';

    return blocks
      .filter((block) => block.BlockType === 'LINE')
      .map((block) => block.Text)
      .join('\n');
  }
}
