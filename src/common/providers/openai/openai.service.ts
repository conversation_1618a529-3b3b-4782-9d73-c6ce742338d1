import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { OpenAIConfigService } from '../../../config/api/openai/config.service';

@Injectable()
export class OpenAIService {
  private readonly logger = new Logger(OpenAIService.name);

  constructor(private readonly openaiConfig: OpenAIConfigService) {
    if (!this.openaiConfig.apiKey) {
      this.logger.warn(
        'OPENAI_API_KEY environment variable is not set. OpenAIService will not function properly.',
      );
    }
  }

  /**
   * Sends the API request to OpenAI
   */
  private async sendApiRequest(
    prompt: string,
    model: string = this.openaiConfig.model,
    temperature: number = 0.2,
  ) {
    if (!this.openaiConfig.apiKey) {
      throw new Error('OpenAI API key is not configured');
    }
    try {
      const response = await axios.post(
        this.openaiConfig.apiUrl,
        {
          model: model || this.openaiConfig.model,
          input: prompt,
          temperature: temperature,
          max_output_tokens: 4000,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.openaiConfig.apiKey}`,
          },
        },
      );

      this.logger.log(
        `Received response from OpenAI API with status: ${response.status}`,
      );
      this.logger.debug(
        'OpenAI API response structure:',
        JSON.stringify(response.data).substring(0, 200) + '...',
      );

      return response;
    } catch (error) {
      this.logger.error(`Error in OpenAI service: ${error}`, error.stack);
      throw new Error(`Failed to send API request: ${error.message}`);
    }
  }

  /**
   * Generate post job content using prompt
   */
  async generateJobPostContent(jobPostContentPrompt: string) {
    const response = await this.sendApiRequest(jobPostContentPrompt, null, 0.6);
    return response?.data?.output?.[0]?.content?.[0]?.text || '';
  }

  /**
   * Validates the user response using OpenAI
   */
  async validateUserResponse(prompt: string) {
    const response = await this.sendApiRequest(prompt, null, 0.4);
    this.logger.log('response :', response.data?.output);
    return JSON.parse(response?.data?.output?.[0]?.content?.[0]?.text || '');
  }

  /**
   * Generates a conversational job application flow based on job post content
   */
  async generateConversationalFlow(sectionContent: Record<string, any>) {
    try {
      if (!this.openaiConfig.apiKey) {
        throw new Error('OpenAI API key is not configured');
      }

      // Validate and prepare job content
      const jobInfoText = this.prepareJobContent(sectionContent);
      const prompt = this.buildPrompt(jobInfoText);

      // Make API request to OpenAI
      const response = await this.sendApiRequest(prompt, null, 0.3);
      const aiResponse = response.data.output[0].content[0].text;
      return this.parseAiResponse(aiResponse);
    } catch (error) {
      this.handleApiError(error);
      throw new Error(
        `Failed to generate conversational flow: ${error.message}`,
      );
    }
  }

  /**
   * Prepares job content for the prompt
   */
  private prepareJobContent(sectionContent: Record<string, any>): string {
    // Validate sectionContent
    if (!sectionContent || typeof sectionContent !== 'object') {
      this.logger.warn('Invalid sectionContent provided, using empty object');
      return 'No detailed job information available. Please create a general application flow.';
    }

    // Log received section content for debugging
    this.logger.debug(
      'Processing job content from sections:',
      Object.keys(sectionContent).join(', '),
    );

    // Process and format each section
    let jobInfoText = '';
    Object.entries(sectionContent).forEach(([title, content]) => {
      // Format content based on type
      let formattedContent = '';
      if (Array.isArray(content)) {
        formattedContent = content
          .map((item) => this.formatContentItem(item))
          .join('\n- ');
        if (formattedContent) formattedContent = '- ' + formattedContent;
      } else if (typeof content === 'object' && content !== null) {
        formattedContent = JSON.stringify(content, null, 2);
      } else {
        formattedContent = String(content || '');
      }

      if (formattedContent.trim()) {
        jobInfoText += `### ${title}:\n${formattedContent}\n\n`;
      }
    });

    return (
      jobInfoText.trim() ||
      'No detailed job information available. Please create a general application flow.'
    );
  }

  /**
   * Format a content item to a readable string
   */
  private formatContentItem(item: any): string {
    if (typeof item === 'object' && item !== null) {
      return JSON.stringify(item);
    }
    return String(item || '');
  }

  /**
   * Builds a detailed prompt for the AI model based on job post content
   */
  private buildPrompt(jobInfoText: string): string {
    return `
Generate a conversational job application flow with highly specific questions directly related to this job. Create a screening questionnaire that effectively identifies qualified candidates.

${jobInfoText}

IMPORTANT REQUIREMENTS:

1. CREATE JOB-SPECIFIC QUESTIONS: Each question must directly reference specific skills, qualifications, or experiences mentioned in the job details.
2. USE EXACT TERMINOLOGY: Use the exact skills, technologies, and qualifications from the job post in your questions and options.
3. INCLUDE ONE YES/NO QUALIFICATION QUESTION: Ask about a specific qualification or experience threshold mentioned in the job description.
4. INCLUDE ONE TEXT_INPUT EXPERIENCE QUESTION: Ask for specific examples related to key responsibilities.
5. NO PERSONAL INFORMATION: Do not ask for name, email, phone, address, or other personal details.
6. ADD REJECTION PATHS: Create appropriate rejection paths for unqualified candidates.

7. END WITH FILE_UPLOAD AND CONFIRMATION: End with resume upload followed by a success message.

RESPONSE FORMAT: Valid JSON containing only an array of questions structured as follows:

{
  "questions": [
    {
      "client_ref": "q_react_experience",
      "question_type": "YES_NO",
      "heading": "Do you have 5+ years of professional experience with React.js?",
      "is_mandatory": true,
      "display_order": 1,
      "options": [
        { "value": "yes" },
        { "value": "no" }
      ],
      "conditions": [
        {
          "trigger_answer_value": "yes",
          "next_question_client_ref": "q_react_projects",
          "next_outcome_client_ref": null
        },
        {
          "trigger_answer_value": "no",
          "next_question_client_ref": null,
          "next_outcome_client_ref": "o_reject"
        }
      ]
    },
    {
      "client_ref": "q_react_projects",
      "question_type": "TEXT_INPUT",
      "heading": "Describe your most complex React project. What challenges did you face and how did you solve them?",
      "sub_text": "Include details about state management, component architecture, and performance optimizations.",
      "placeholder_text": "In my role at XYZ, I built a complex dashboard that...",
      "is_mandatory": true,
      "display_order": 2,
      "conditions": [
        {
          "trigger_answer_value": "*",
          "next_question_client_ref": "q_tech_stack",
          "next_outcome_client_ref": null
        }
      ]
    },
    {
      "client_ref": "q_tech_stack",
      "question_type": "SINGLE_SELECT",
      "heading": "Which of the following technologies have you worked with professionally?",
      "is_mandatory": true,
      "display_order": 3,
      "options": [
        { "value": "GraphQL" },
        { "value": "AWS Lambda" },
        { "value": "DynamoDB" },
        { "value": "S3" },
        { "value": "CloudFront" },
        { "value": "Redux" }
      ],
      "conditions": [
        {
          "trigger_answer_value": "*",
          "next_question_client_ref": "q_cv_upload",
          "next_outcome_client_ref": null
        }
      ]
    },
    {
      "client_ref": "q_cv_upload",
      "question_type": "FILE_UPLOAD",
      "heading": "Upload your resume/CV",
      "sub_text": "PDF, DOCX formats. Max 10MB.",
      "is_mandatory": true,
      "display_order": 4,
      "allowed_file_types": "pdf,docx",
      "max_file_size_mb": 10,
      "conditions": [
        {
          "trigger_answer_value": "*",
          "next_question_client_ref": "q_thank_you",
          "next_outcome_client_ref": null
        }
      ]
    },
    {
      "client_ref": "q_thank_you",
      "question_type": "STATIC_MESSAGE",
      "heading": "Application Submitted Successfully!",
      "sub_text": "Thank you for applying to the Senior Software Developer position.",
      "message_style": "SUCCESS",
      "is_mandatory": true,
      "display_order": 5,
      "conditions": [
        {
          "trigger_answer_value": "*",
          "next_question_client_ref": null,
          "next_outcome_client_ref": "o_thank_you"
        }
      ]
    }
  ]
}

FORMAT YOUR RESPONSE AS VALID JSON WITHOUT EXPLANATIONS OR ADDITIONAL TEXT.
`;
  }

  /**
   * Handles API errors with proper logging
   */
  private handleApiError(error: any) {
    this.logger.error(`Error in OpenAI service: ${error.message}`, error.stack);

    if (error.response) {
      this.logger.error(
        `API Error Response: Status ${error.response.status}`,
        error.response.data
          ? JSON.stringify(error.response.data)
          : 'No response data',
      );
    } else if (error.request) {
      this.logger.error('API Request Error: No response received');
    }
  }

  /**
   * Parses and transforms the AI response into the required format
   */
  private parseAiResponse(aiResponse: string): any {
    try {
      const jsonMatch = aiResponse.match(/```(?:json)?([\s\S]*?)```/) || [
        null,
        aiResponse,
      ];
      const jsonContent = jsonMatch[1].trim();
      const parsedResponse = JSON.parse(jsonContent);
      this.validateResponse(parsedResponse);

      return {
        questions: parsedResponse.questions,
        outcomes: [
          {
            client_ref: 'o_reject',
            outcome_type: 'REJECT',
            message_text:
              'Thank you for your interest. We are looking for candidates with different qualifications.',
            is_default_reject: true,
            is_default_success: false,
          },
          {
            client_ref: 'o_thank_you',
            outcome_type: 'THANK_YOU_COMPLETE',
            message_text:
              'Thank you for your application! We will review your information.',
            is_default_reject: false,
            is_default_success: true,
          },
        ],
      };
    } catch (error) {
      this.logger.error(
        `Error parsing AI response: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to parse AI response: ${error.message}`);
    }
  }

  /**
   * Validates the parsed response structure
   */
  private validateResponse(parsedResponse: any) {
    if (!parsedResponse.questions || !Array.isArray(parsedResponse.questions)) {
      throw new Error('Invalid response: questions array is missing');
    }

    parsedResponse.questions.forEach((question: any, index: number) => {
      if (!question.client_ref) {
        throw new Error(`Question at index ${index} is missing client_ref`);
      }
      if (!question.question_type) {
        throw new Error(`Question at index ${index} is missing question_type`);
      }
    });
  }

  /**
   * Generates a job post using OpenAI
   */
  async generateJobPost(jobPostContent: string) {
    const prompt = `You are a structured job post generator.

Your task is to extract job information from the provided content and generate a strictly structured JSON for use in a job board system.

  Validation Rules:
  - If the input does not clearly describe a job posting (e.g., it's a receipt, booking confirmation, advertisement, or lacks job-specific details), you must return:
    { "error": "The uploaded document is invalid. Required fields for job templates are missing." }

  Required Fields Check:
  - If any of the following job highlights cannot be confidently derived, return the above error message:
    - job_type ("Full time" or "Part time")
    - compensation (both min and max salary)
    - age_limit

  Strict Formatting Rules:
  - Use the exact JSON structure provided — do not add, remove, or rename any keys.
  - All content.value fields must be plain strings.
  - Use newline-separated bullet points (\n) for list-type fields (e.g., qualifications, responsibilities, benefits).
  - Do not use arrays for content values.
  - If the shift is not clearly "Day" or "Night", set it to null.
  - Summarize the job in 1–2 sentences in the description field, highlighting the candidate's role and impact.

  Use the following strict output format:

{
  "jobTemplateId": null,
  "title": <job title>,
  "description": <summary>,
  "createdAt": null,
  "updatedAt": null,
  "location": {
    "id": null,
    "name": null,
    "city": <city>,
    "address": null,
    "country": <country>
  },
  "sections": [
    {
      "jobTemplateSectionId": null,
      "title": "Job Highlights",
      "content": [
        {
          "key": "job_type",
          "type": "select",
          "label": "Job type",
          "value": <"Full time" or "Part time" or null>,
          "options": ["Full time", "Part time"]
        },
        {
          "key": "compensation",
          "max": <max salary>,
          "min": <min salary>,
          "type": "range",
          "label": "Competitive Pay",
          "currency": "USD($)"
        },
        {
          "key": "age_limit",
          "type": "singleInput",
          "label": "Age Limit",
          "value": <age limit or null>
        },
        {
          "key": "shift",
          "type": "select",
          "label": "Shift",
          "value": <"Day", "Night" or null>,
          "options": ["Day", "Night"]
        }
      ],
      "displayOrder": 1,
      "isVisibleDefault": true,
      "isEditable": true,
      "isDeletable": false,
      "sectionDefinitionId": null,
      "type": "job_highlights"
    },
    {
      "jobTemplateSectionId": null,
      "title": "Qualifications",
      "content": [
        {
          "type": "text",
          "value": <newline-separated qualifications as a string>
        }
      ],
      "displayOrder": 2,
      "isVisibleDefault": true,
      "isEditable": true,
      "isDeletable": true,
      "sectionDefinitionId": null,
      "type": "qualifications"
    },
    {
      "jobTemplateSectionId": null,
      "title": "Benefits",
      "content": [
        {
          "type": "text",
          "value": <newline-separated benefits as a string>
        }
      ],
      "displayOrder": 3,
      "isVisibleDefault": true,
      "isEditable": true,
      "isDeletable": true,
      "sectionDefinitionId": null,
      "type": "benefits"
    },
    {
      "jobTemplateSectionId": null,
      "title": "Responsibilities",
      "content": [
        {
          "type": "text",
          "value": <newline-separated responsibilities as a string>
        }
      ],
      "displayOrder": 4,
      "isVisibleDefault": true,
      "isEditable": true,
      "isDeletable": true,
      "sectionDefinitionId": null,
      "type": "responsibilities"
    },
    {
      "jobTemplateSectionId": null,
      "title": "Job Description",
      "content": [
        {
          "type": "text",
          "value": <role summary from input>
        }
      ],
      "displayOrder": 5,
      "isVisibleDefault": true,
      "isEditable": true,
      "isDeletable": true,
      "sectionDefinitionId": null,
      "type": "job_description"
    }
  ]
}

Only return the final JSON output or the exact error message above. Do not include any commentary, notes, or explanations.

Input:
Extract the job details from the following content:
${jobPostContent}`;

    console.log('prompt :', prompt);
    const response = await this.sendApiRequest(prompt, null, 0.5);
    return JSON.parse(response?.data?.output?.[0]?.content?.[0]?.text || '');
  }

  /**
   * Generates a job post using OpenAI with default values
   * 
   * You are a structured job post generator.
    Your task is to extract job information from the provided content and generate a strictly structured JSON for use in a job board system. 
    Do not leave any 'content.value' as null if it can be reasonably inferred.
    If all required values are present, return the JSON in the exact format specified below — without adding or removing any keys. All value fields must be intelligently populated. All fields must be present even if the values are null.

    
   */
  async generateJobPostUsingAI(jobPostContent: string) {
    const prompt = `
    You are a structured job post generator.

Your task is to extract job information from the provided content and generate a strictly structured JSON for use in a job board system.

Rules:
- Use the exact JSON structure provided below — do not add, remove, or rename any keys.
- Every content.value must be a single string. Use **newline-separated bullet points (\n)** for list-type fields such as qualifications, responsibilities, and benefits. **Do not use arrays.**
- Do not leave any 'content.value' as null if it can be reasonably inferred.
- If any **required fields under the "Job Highlights" section** (job_type, compensation, age_limit) are missing or unclear, return this **exact error message**:
{ "error": "The uploaded document is invalid. Required fields for job templates are missing." }
- If the shift is not clearly "Day" or "Night", set it to **null**.
- Provide a short, clear summary (1–2 sentences) in the description field, highlighting the candidate's role and impact.

Use the following strict output format:
    {
      "jobTemplateId": null,
      "title": <user response>,
      "description": <add a small summary based on the user's role or position>,
      "createdAt": null,
      "updatedAt": null,
      "location": {
        "id": null,
        "name": null,
        "city": null,
        "address": null,
        "country": null
      },
      "sections": [
        {
          "jobTemplateSectionId": null,
          "title": "Job Highlights",
          "content": [
            {
              "key": "job_type",
              "type": "select",
              "label": "Job type",
              "value": <"Full time" or "Part time" based on user input, if not present then default to "Full time">,
              "options": ["Full time", "Part time"]
            },
            {
              "key": "compensation",
              "max": <maximum salary based on user input, if not present then default to a reasonable maximum salary>,
              "min": <minimum salary based on user input, if not present then default to a reasonable minimum salary>,
              "type": "range",
              "label": "Competitive Pay",
              "currency": "USD($)"
            },
            {
              "key": "age_limit",
              "type": "singleInput",
              "label": "Age Limit",
              "value": <age value, if not present then default to a reasonable age limit>
            },
            {
              "key": "shift",
              "type": "select",
              "label": "Shift",
              "value": <"Day" or "Night" based on user input, if not present then default to "Day">,
              "options": ["Day", "Night"]
            }
          ],
          "displayOrder": 1,
          "isVisibleDefault": true,
          "isEditable": true,
          "isDeletable": false,
          "sectionDefinitionId": null,
          "type": "job_highlights"
        },
        {
          "jobTemplateSectionId": null,
          "title": "Qualifications",
          "content": [{ "type": "text", "value": <bulleted list of qualifications from user input, if not present then generate intelligently based on the user's response> }],
          "displayOrder": 2,
          "isVisibleDefault": true,
          "isEditable": true,
          "isDeletable": true,
          "sectionDefinitionId": null,
          "type": "qualifications"
        },
        {
          "jobTemplateSectionId": null,
          "title": "Benefits",
          "content": [{ "type": "text", "value": <bulleted list of benefits from user input, if not present then generate intelligently based on the user's response> }],
          "displayOrder": 3,
          "isVisibleDefault": true,
          "isEditable": true,
          "isDeletable": true,
          "sectionDefinitionId": null,
          "type": "benefits"
        },
        {
          "jobTemplateSectionId": null,
          "title": "Responsibilities",
          "content": [{ "type": "text", "value": <responsibilities listed or summarized from user input, if not present then generate intelligently based on the user's response> }],
          "displayOrder": 4,
          "isVisibleDefault": true,
          "isEditable": true,
          "isDeletable": true,
          "sectionDefinitionId": null,
          "type": "responsibilities"
        },
        {
          "jobTemplateSectionId": null,
          "title": "Job Description",
          "content": [{ "type": "text", "value": <summary or detail of the role based on user input, if not present then generate intelligently based on the user's response> }],
          "displayOrder": 5,
          "isVisibleDefault": true,
          "isEditable": true,
          "isDeletable": true,
          "sectionDefinitionId": null,
          "type": "job_description"
        }
      ]
    }

    Only return the final JSON object. Do not include explanations or notes.

    Input:
    Extract the job details from the following content:
    ${jobPostContent}`;

    const response = await this.sendApiRequest(prompt, null, 0.6);
    const aiResponse = response.data.choices[0].message.content;
    const jsonMatch = aiResponse.match(/```(?:json)?([\s\S]*?)```/) || [
      null,
      aiResponse,
    ];
    const jsonContent = jsonMatch[1].trim();
    return JSON.parse(jsonContent);
  }

  async generateAIInsightsForApplication(prompt: string) {
    const response = await this.sendApiRequest(prompt);
    return JSON.parse(response?.data?.output?.[0]?.content?.[0]?.text || '');
  }

  async generateAIProfileFromResume(prompt: string) {
    try {
      const response = await this.sendApiRequest(prompt);

      // Extract JSON from the response text
      const responseText =
        response?.data?.output?.[0]?.content?.[0]?.text || '';

      // Remove any markdown code block syntax if present
      const jsonString = responseText
        .replace(/```json\n|\n```|```/g, '')
        .trim();

      // Parse the cleaned JSON string
      const parsedResponse = JSON.parse(jsonString);

      this.logger.debug('Parsed AI profile response:', parsedResponse);
      return parsedResponse;
    } catch (error) {
      this.logger.error('Error generating AI profile from resume:', {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }
}
