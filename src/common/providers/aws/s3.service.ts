import { Injectable } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { ConfigService } from '@nestjs/config';

export interface PreSignedUrlOptions {
  fileName: string;
  contentType: string;
  folder?: string;
  expiresIn?: number; // in seconds
}

export interface GetPreSignedUrlOptions {
  fileKey: string;
  expiresIn?: number; // in seconds
}

@Injectable()
export class S3Service {
  private readonly s3Client: S3Client;
  private readonly bucketName: string;

  constructor(private readonly configService: ConfigService) {
    this.s3Client = new S3Client({
      region: this.configService.get<string>('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
        ),
      },
    });
    this.bucketName = this.configService.get<string>('AWS_S3_BUCKET');
  }

  async generatePreSignedUrl(options: PreSignedUrlOptions): Promise<{
    preSignedUrl: string;
    fileKey: string;
  }> {
    const { fileName, contentType, folder = '', expiresIn = 3600 } = options;

    // Generate a unique file key using timestamp
    const timestamp = new Date().getTime();
    const fileKey = folder
      ? `${folder}/${timestamp}-${fileName}`
      : `${timestamp}-${fileName}`;

    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: fileKey,
      ContentType: contentType,
    });

    const preSignedUrl = await getSignedUrl(this.s3Client, command, {
      expiresIn,
    });

    return {
      preSignedUrl,
      fileKey,
    };
  }

  async generateGetPreSignedUrl(
    options: GetPreSignedUrlOptions,
  ): Promise<string> {
    const { fileKey, expiresIn = 3600 } = options;

    const command = new GetObjectCommand({
      Bucket: this.bucketName,
      Key: fileKey,
    });

    return await getSignedUrl(this.s3Client, command, {
      expiresIn,
    });
  }
}
