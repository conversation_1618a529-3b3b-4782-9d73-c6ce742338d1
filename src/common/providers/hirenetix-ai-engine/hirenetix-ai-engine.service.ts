import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { JobPost } from '../../../models/jobs/entities/job-post.entity';
import { JobPostSection } from '../../../models/jobs/entities/job-post-section.entity';
import { JobPostSectionType } from '../../../models/jobs/entities/job-post-section.entity';

@Injectable()
export class HirenetixAiEngineService {
  private readonly baseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get<string>(
      'HIRENETIX_AI_ENGINE_BASE_URL',
    );
  }

  private findSectionByType(
    sections: JobPostSection[],
    type: JobPostSectionType,
  ): string {
    const section = sections?.find((s) => s.type === type);
    return section?.content?.[0]?.value || '';
  }

  private transformSections(
    sections: JobPostSection[],
  ): Record<string, string> {
    return sections.reduce(
      (acc, section) => {
        if (section.type === JobPostSectionType.HIGHLIGHTS) {
          return acc;
        }
        const contentValue =
          Array.isArray(section.content) &&
          section.content.length > 0 &&
          section.content[0]?.value
            ? section.content[0].value
            : '';

        acc[section.title] = contentValue;
        return acc;
      },
      {} as Record<string, string>,
    );
  }

  async addJobToVectorStore(jobPost: JobPost): Promise<void> {
    try {
      const sections = this.transformSections(jobPost.sections);

      const jobHighlightsText = jobPost.sections?.find(
        (s) => s.type === JobPostSectionType.HIGHLIGHTS,
      )?.content;

      const competitive_pay = jobHighlightsText?.find(
        (h: any) => h.key === 'compensation',
      );

      const payload = {
        id: jobPost.id,
        title: jobPost.title,
        tenant_id: jobPost.tenantId,
        location: jobPost.location
          ? `${jobPost.location.address}, ${jobPost.location.city}`
          : '',
        shift: jobHighlightsText?.find((h: any) => h.key === 'shift')?.value,
        min_age: jobHighlightsText?.find((h: any) => h.key === 'age_limit')
          ?.value,
        competitive_pay: `${competitive_pay?.currency} ${competitive_pay?.min} - ${competitive_pay?.currency} ${competitive_pay?.max}`,
        sections: sections,
      };

      const response = await firstValueFrom(
        this.httpService.post(`${this.baseUrl}/vector-store/add-job`, payload),
      );
      console.log('Job added to vector store successfully', response);
    } catch (error) {
      console.error('Failed to add job to vector store:', error.message);
      throw new HttpException(
        'Failed to add job to vector store',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteJobFromVectorStore(
    jobPostId: string,
    tenantId: string,
  ): Promise<void> {
    await firstValueFrom(
      this.httpService.post(`${this.baseUrl}/vector-store/delete-job`, {
        id: jobPostId,
        tenant_id: tenantId,
      }),
    );
  }
}
