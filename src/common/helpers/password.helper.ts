import * as bcrypt from 'bcrypt';

/**
 * Helper functions for password operations
 */
export class PasswordHelper {
  /**
   * Default salt rounds for bcrypt hashing
   */
  private static readonly SALT_ROUNDS = 10;

  /**
   * Hash a plain text password using bcrypt
   * @param plainPassword - The plain text password to hash
   * @param saltRounds - Optional salt rounds (defaults to 10)
   * @returns A promise that resolves to the hashed password
   */
  static async hashPassword(
    plainPassword: string,
    saltRounds: number = this.SALT_ROUNDS,
  ): Promise<string> {
    // Generate a salt
    const salt = await bcrypt.genSalt(saltRounds);

    // Hash the password with the salt
    const hashedPassword = await bcrypt.hash(plainPassword, salt);

    return hashedPassword;
  }

  /**
   * Compare a plain text password with a hashed password
   * @param plainPassword - The plain text password to check
   * @param hashedPassword - The hashed password to compare against
   * @returns A promise that resolves to true if passwords match, false otherwise
   */
  static async comparePasswords(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }
}
