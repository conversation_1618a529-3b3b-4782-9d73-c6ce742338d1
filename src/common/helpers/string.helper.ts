/**
 * Generate a random numeric string of specified length
 * @param length - The length of the random numeric string
 * @returns A random string containing only numbers
 */
export function generateRandomNumericString(length: number): string {
  const digits = '0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * digits.length);
    result += digits.charAt(randomIndex);
  }

  return result;
}
