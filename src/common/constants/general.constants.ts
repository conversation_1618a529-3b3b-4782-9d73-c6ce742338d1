export const APP_NAME = 'HireNetix Backend';

export enum EnvironmentType {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
}

export const DEFAULT_PORT = 8000;

export const PERMISSIONS_KEY = 'permissions';

export const JWT_AUTH_NAME = 'JWT-auth';

export const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
export const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'text/plain',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
];
export const ALLOWED_FILE_EXTENSIONS = ['.pdf', '.doc', '.txt', '.docx'];
