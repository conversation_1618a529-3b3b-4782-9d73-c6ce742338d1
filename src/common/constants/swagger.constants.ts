export const API_TAGS = {
  AUTH: 'Authentication',
  USERS: 'Users',
  ORGANISATIONS: 'Organisations',
  ROLES: 'Roles',
};

export const API_OPERATIONS = {
  LOGIN: 'Login to the application',
  VERIFY_OTP: 'Verify OTP',
  GENERATE_PRESIGNED_URL: 'Generate a presigned URL for S3 operations',
  GET_USER_DETAILS: 'Get details of a user',
  CHECK_USER_STATUS: 'Check user verification status',
  CHANGE_PASSWORD: 'Change user password',
  FORGOT_PASSWORD: 'Request a password reset link',
  CHECK_RESET_TOKEN: 'Check if the reset token is valid or expired',
  RESET_PASSWORD: 'Reset password using token received via email',
  LOGOUT: 'Logout from the application and invalidate the token',
  GET_ALL_ROLES_FOR_A_SPECIFIC_TENANT:
    'Get all roles for a specific tenant with pagination, filtering by module, and searching across role names, descriptions, and permissions',
  GET_ROLE_BY_ID_WITH_PERMISSIONS: 'Get a specific role with its permissions',
  CREATE_A_NEW_ROLE_FOR_THE_CURRENT_TENANT:
    'Create a new role for the current tenant',
  CREATE_A_NEW_ROLE_WITH_PERMISSIONS:
    'Create a new role with permissions. The permissions are specified by their names.',
  UPDATE_ROLE: "Update a role's description and/or permissions",
  GET_ALL_PERMISSIONS_IN_THE_SYSTEM: 'Get all permissions in the system',
  CHECK_ROLE_NAME_UNIQUENESS:
    'Check if a role name is unique within the organization',
  DELETE_ROLE: 'Delete a role by ID',
  ADD_USER_TO_ORGANIZATION: 'Add a user to an organization',
};

export const API_RESPONSES = {
  PRESIGNED_URL_SUCCESS: 'Presigned URL generated successfully',
  UNPROCESSABLE_ENTITY: 'Unprocessable Entity - Invalid request parameters',
  SUCCESS: 'Request completed successfully',
  ERROR: 'Internal Server Error',
  INVALID_CREDENTIALS: 'Invalid credentials',
  INVALID_OTP: 'Invalid OTP',
  TOKEN_VALID: 'Token is valid',
  OTP_VERIFIED: 'OTP verified successfully',
  USER_STATUS_RETRIEVED: 'User verification status retrieved successfully',
  PASSWORD_CHANGED: 'Password changed successfully',
  USER_NOT_FOUND: 'User not found',
  PASSWORD_RESET_EMAIL_SENT: 'Password reset email sent if the email exists',
  PASSWORD_RESET_SUCCESS: 'Password reset successfully',
  INVALID_OR_EXPIRED_TOKEN: 'The link has already been used or has expired',
  LOGOUT_SUCCESS: 'Logged out successfully',
  LIST_OF_ROLES_FOR_THE_TENANT:
    'Paginated list of roles for the tenant with filtering and search capabilities',
  ROLE_WITH_PERMISSIONS_RETRIEVED:
    'Role with permissions retrieved successfully',
  FORBIDDEN_INSUFFICIENT_PERMISSIONS: 'Forbidden - insufficient permissions',
  NO_ROLES_FOUND: 'No roles found for the tenant',
  ROLE_CREATED_SUCCESSFULLY: 'Role created successfully',
  ROLE_UPDATED_SUCCESSFULLY: 'Role successfully updated',
  LIST_OF_PERMISSIONS: 'List of permissions in the system',
  ROLE_NAME_UNIQUENESS_RESULT: 'Result of the role name uniqueness check',
  TENANT_ID_REQUIRED: 'Bad request - Tenant ID is required',
  ROLE_DELETED_SUCCESSFULLY: 'Role successfully deleted',
  ROLE_NOT_FOUND: 'Role not found',
  ROLE_DELETION_FAILED: 'Failed to delete role',
  ROLE_UPDATE_FAILED: 'Failed to update role',
  SYSTEM_ROLE_DELETION_NOT_ALLOWED: 'System roles cannot be deleted',
  SYSTEM_ROLE_UPDATE_NOT_ALLOWED: 'System roles cannot be updated',
  NO_CHANGES_DETECTED: 'No changes detected in the update request',
  EMAIL_OR_PHONE_NUMBER_ALREADY_REGISTERED:
    'Email or phone number already registered',
  INVALID_INPUT_DATA: 'Invalid input data',
  SECTION_DEFINITION_CREATED_SUCCESSFULLY:
    'The section definition has been successfully created.',
  SECTION_DEFINITION_UPDATED_SUCCESSFULLY:
    'The section definition has been successfully updated.',
  SECTION_DEFINITION_DELETED_SUCCESSFULLY:
    'The section definition has been successfully deleted.',
  LIST_OF_SECTION_DEFINITIONS: 'List of section definitions',
  SECTION_DEFINITION_DETAILS: 'Section definition details',
};

export const API_PROPERTY = {
  EMAIL_ADDRESS: 'Email address of the user',
  NEW_PASSWORD_THAT_MEETS_SECURITY_REQUIREMENTS:
    'New password that meets the security requirements',
  CONFIRM_PASSWORD_THAT_MUST_MATCH_PASSWORD:
    'Confirm the new password (must match password)',
};

export const API_QUERY_PARAMETERS = {
  FILTER_ROLES_BY_SPECIFIC_MODULE: 'Filter roles by specific module(s)',
  SEARCH_TEXT_THAT_WILL_BE_MATCHED_AGAINST_ROLE_NAMES_AND_DESCRIPTIONS:
    'Search text that will be matched against role names, role descriptions, permission names, and permission descriptions. Case-insensitive partial matching is supported.',
  PAGE_NUMBER: 'Page number (starting from 1)',
  NUMBER_OF_ITEMS_PER_PAGE: 'Number of items per page',
};
