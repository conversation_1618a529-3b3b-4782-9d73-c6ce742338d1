export const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  MIN_LENGTH: (length: number) => `Minimum ${length} characters required`,
  MAX_LENGTH: (length: number) => `Maximum ${length} characters allowed`,
  EMAIL: 'Invalid email format',
  PASSWORD:
    'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
  PASSWORD_MATCH: 'Passwords do not match',
  UNIQUE: (field: string) => `${field} already exists`,
  INVALID_FORMAT: 'Invalid format',
  DATE_FORMAT: 'Invalid date format. Use YYYY-MM-DD',
  INVALID_CREDENTIALS: 'Invalid credentials',
  ACCOUNT_INACTIVE: 'Account is inactive',
  NOT_FOUND: (entity: string) => `${entity} not found`,
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Forbidden action',
  ACCOUNT_LOCKED: 'Account is locked. Please try again later',
};

export const VALIDATION_CONSTANTS = {
  MIN_NAME_LENGTH: 2,
  MAX_NAME_LENGTH: 100,
  MIN_PASSWORD_LENGTH: 8,
  MAX_PASSWORD_LENGTH: 50,
  MAX_EMAIL_LENGTH: 255,
  MAX_TITLE_LENGTH: 200,
  MAX_DESCRIPTION_LENGTH: 5000,
};
