export const AUTH_MESSAGES = {
  INVALID_CREDENTIALS: 'Invalid email or password',
  ACCOUNT_LOCKED: 'Account is locked due to too many failed login attempts',
  ACCOUNT_INACTIVE:
    'Your account is currently inactive. Please contact support for assistance.',
  EMAIL_NOT_VERIFIED: 'Email is not verified',
  TOKEN_EXPIRED: 'Token has expired',
  TOKEN_INVALID: 'Invalid token',
  LINK_EXPIRED: 'This link has already been used or has expired',
  LINK_VALID: 'Link is valid',
  OTP_VERIFIED_SUCCESSFULLY: 'OTP verified successfully',
  PASSWORD_CHANGED_SUCCESSFULLY: 'Password changed successfully',
  PASSWORD_MUST_CONTAINS:
    'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
  PASSWORD_DO_NOT_MATCH: 'Passwords do not match',
  PASSWORD_RESET_EMAIL_SENT: 'Password reset email sent successfully',
  A_PASSWORD_RESET_LINK_HAS_BEEN_SENT_TO_YOUR_EMAIL:
    'A password reset link has been sent to your email',
  LOGOUT_SUCCESS: 'Logged out successfully',
};

export const USER_MESSAGES = {
  PASSWORD_CHANGE_FAILED: 'Failed to update password',
  USER_NOT_FOUND: (email: string) => `User with email ${email} not found`,
  EMAIL_NOT_VERIFIED: 'Email is not verified',
  PASSWORD_NOT_ALLOWED_TO_CHANGED: 'Password is not allowed to changed',
  PASSWORD_RESET_FAILED: 'Failed to initiate password reset',
  EMAIL_ALREADY_REGISTERED: 'Email already registered',
  PHONE_ALREADY_REGISTERED: 'Phone number already registered',
  USER_ADDED_SUCCESSFULLY: 'User added to organization successfully',
  CANNOT_DELETE_CURRENT_USER: 'Cannot delete current user',
};

export const ORGANISATION_MESSAGES = {
  ORGANISATION_ID_REQUIRED: 'Organisation ID is required for users',
  ORGANISATION_NOT_FOUND: 'Organisation not found',
  ORGANISATION_NAME_EXISTS: (name: string) =>
    `Organisation with name ${name} already exists`,
  TENANT_ID_REQUIRED: 'Tenant id is required',
  TENANT_NOT_FOUND: 'Tenant not found or is not active',
};

export const ROLES_MESSAGES = {
  ROLE_ID_REQUIRED: 'Role ID is required',
  ROLE_NOT_FOUND: `Role ID not found`,
  ROLE_NAME_EXISTS: 'Role Title is already taken!',
  FAILED_TO_RETRIEVE_NEWLY_CREATED_ROLE:
    'Failed to retrieve newly created role',
  FAILED_TO_CREATE_ROLE: 'Failed to create role',
  ROLE_DELETED_SUCCESSFULLY: 'Role successfully deleted',
  FAILED_TO_DELETE_ROLE: 'Failed to delete role',
  SYSTEM_ROLE_DELETION_NOT_ALLOWED: 'System roles cannot be deleted',
  ROLE_UPDATED_SUCCESSFULLY: 'Role successfully updated',
  FAILED_TO_UPDATE_ROLE: 'Failed to update role',
  SYSTEM_ROLE_UPDATE_NOT_ALLOWED: 'System roles cannot be updated',
  NO_CHANGES_DETECTED: 'No changes detected in the update request',
  ROLE_ASSIGNED_TO_USERS:
    'This role is currently assigned to one or more users. Please reassign or remove users before deleting.',
};

export const PERMISSIONS_MESSAGES = {
  PERMISSION_NOT_FOUND: 'Permission not found',
};

export const LOCATION_MESSAGES = {
  LOCATION_NOT_FOUND: 'Location not found',
  LOCATION_CODE_EXISTS: 'Location code already exists',
  ORGANISATION_ID_REQUIRED: 'Organisation ID is required',
  INVALID_LOCATION_CODE: 'Invalid location code format',
  LOCATION_CREATED: 'Location created successfully',
  LOCATION_UPDATED: 'Location updated successfully',
  LOCATION_DELETED: 'Location deleted successfully',
  LOCATION_DOES_NOT_BELONG_TO_ORGANIZATION:
    'Location does not belong to the organization',
  LOCATION_ID_REQUIRED: 'Location ID is required',
  LOCATION_ID_NOT_ALLOWED_FOR_NON_LOCATION_ROLES:
    'Location ID is not allowed for non-location roles',
  LOCATION_IN_USE: 'This location is currently in use and cannot be deleted.',
};

export const LANDING_PAGE_MESSAGES = {
  CONTACT_RESPONSE_CREATED_SUCCESSFULLY: 'Contact response sent successfully',
};

export const CANDIDATE_MESSAGES = {
  CANDIDATE_CREATED_SUCCESSFULLY: 'Candidate created successfully',
  CANDIDATE_UPDATED_SUCCESSFULLY: 'Candidate updated successfully',
  CANDIDATE_DELETED_SUCCESSFULLY: 'Candidate deleted successfully',
  CANDIDATE_EMAIL_ALREADY_EXISTS: 'Candidate with this email already exists',
  CANDIDATE_NOT_FOUND: 'Candidate not found',
};

export const SECTION_DEFINITION_MESSAGES = {
  SECTION_DEFINITION_NOT_FOUND: 'Section definition not found',
  SECTION_DEFINITION_CREATED_SUCCESSFULLY:
    'Section definition created successfully',
  SECTION_DEFINITION_UPDATED_SUCCESSFULLY:
    'Section definition updated successfully',
  SECTION_DEFINITION_DELETED_SUCCESSFULLY:
    'Section definition deleted successfully',
  SYSTEM_DEFINED_SECTION_MODIFICATION_NOT_ALLOWED:
    'System-defined sections cannot be modified',
  SYSTEM_DEFINED_SECTION_DELETION_NOT_ALLOWED:
    'System-defined sections cannot be deleted',
};

export const JOB_POST_MESSAGES = {
  JOB_POST_NOT_FOUND: (id: string) => `Job post with ID "${id}" not found`,
  JOB_POST_NOT_ACTIVE: (id: string) => `Job post with ID "${id}" is not active`,
  JOB_POST_CREATED_SUCCESSFULLY: 'Job post created successfully',
  JOB_POST_UPDATED_SUCCESSFULLY: 'Job post updated successfully',
  JOB_POST_DELETED_SUCCESSFULLY: 'Job post deleted successfully',
  JOB_POST_ACTIVE_STATUS_UPDATED_SUCCESSFULLY:
    'Job post active status updated successfully',
  JOB_POST_NOT_FOUND_FOR_ORGANISATION: 'Job post not found in the organisation',
};
