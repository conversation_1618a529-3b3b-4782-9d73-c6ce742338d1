import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, IsOptional, Min } from 'class-validator';

/**
 * Pagination query parameters DTO
 * @example
 * {
 *   "page": 1,
 *   "limit": 10
 * }
 */
export class PaginationDto {
  /**
   * Page number (1-based indexing)
   * @example 1
   * @default 1
   * @minimum 1
   */
  @ApiProperty({
    description: 'Page number (1-based indexing)',
    example: 1,
    default: 1,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  /**
   * Number of items per page
   * @example 10
   * @default 10
   * @minimum 1
   */
  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    default: 10,
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 10;
}
