import { ApiProperty } from '@nestjs/swagger';

/**
 * Standard API Response format
 * @template T - The data type
 */
export class ApiResponseDto<T> {
  /**
   * HTTP Status code
   * @example 200
   */
  @ApiProperty({
    description: 'HTTP Status code',
    example: 200,
  })
  statusCode: number;

  /**
   * Response message
   * @example "Success"
   */
  @ApiProperty({
    description: 'Response message',
    example: 'Success',
  })
  message: string;

  /**
   * Response data
   */
  @ApiProperty({
    description: 'Response data',
    nullable: true,
    type: Object,
  })
  data?: T;

  /**
   * Constructor
   * @param statusCode HTTP Status code
   * @param message Response message
   * @param data Response data
   */
  constructor(statusCode: number, message: string, data?: T) {
    this.statusCode = statusCode;
    this.message = message;
    this.data = data;
  }
}

/**
 * Pagination data interface
 * @template T - The data item type
 */
export class PaginationData<T> {
  /**
   * Array of data items
   */
  @ApiProperty({
    description: 'Array of data items',
    isArray: true,
  })
  items: T[];

  /**
   * Total number of items
   * @example 100
   */
  @ApiProperty({
    description: 'Total number of items',
    example: 100,
  })
  total: number;

  /**
   * Current page number
   * @example 1
   */
  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  /**
   * Number of items per page
   * @example 10
   */
  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  /**
   * Total number of pages
   * @example 10
   */
  @ApiProperty({
    description: 'Total number of pages',
    example: 10,
  })
  totalPages: number;
}

/**
 * Paginated API Response format
 * @template T - The data item type
 */
export class PaginatedResponseDto<T> {
  /**
   * HTTP Status code
   * @example 200
   */
  @ApiProperty({
    description: 'HTTP Status code',
    example: 200,
  })
  statusCode: number;

  /**
   * Response message
   * @example "Success"
   */
  @ApiProperty({
    description: 'Response message',
    example: 'Success',
  })
  message: string;

  /**
   * Response data with pagination info
   */
  @ApiProperty({
    description: 'Response data with pagination info',
  })
  data: PaginationData<T>;

  /**
   * Constructor
   * @param statusCode HTTP Status code
   * @param message Response message
   * @param data Response data with pagination info
   */
  constructor(statusCode: number, message: string, data: PaginationData<T>) {
    this.statusCode = statusCode;
    this.message = message;
    this.data = data;
  }
}
