import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { UsersRepository } from '@hirenetix/modules/users/users.repository';
import { generateRandomNumericString } from '../../helpers/string.helper';

@Injectable()
export class OTPService {
  private readonly logger = new Logger(OTPService.name);

  constructor(
    @Inject(forwardRef(() => UsersRepository))
    private readonly usersRepository: UsersRepository,
  ) {}

  /**
   * Generate a new OTP for a user
   * @param userId - User ID to generate OTP for
   * @returns The generated OTP
   */
  async generateOTP(userId: string): Promise<string> {
    // Generate a random numeric OTP of specified length

    const otp = generateRandomNumericString(6);

    // Calculate expiry time
    const expiresAt = new Date();
    const expiryTime = parseInt(process.env.OTP_EXPIRY_TIME || '5', 10);
    expiresAt.setMinutes(expiresAt.getMinutes() + expiryTime);

    // Store OTP in the user record
    await this.usersRepository.update(userId, {
      otpCode: otp,
      otpExpiresAt: expiresAt,
    });

    this.logger.log(`Generated OTP for user ${userId}`);
    return otp;
  }

  /**
   * Verify an OTP for a user
   * @param userId - User ID to verify OTP for
   * @param otpToVerify - OTP code to verify
   * @returns true if the OTP is valid, false otherwise
   */
  async verifyOTP(email: string, otpToVerify: string): Promise<boolean> {
    const user = await this.usersRepository.findByEmail(email);

    if (!user || !user.otpCode || !user.otpExpiresAt) {
      this.logger.warn(`No OTP found for user ${email}`);
      return false;
    }

    // Check if OTP is expired
    if (new Date() > user.otpExpiresAt) {
      this.logger.warn(`OTP expired for user ${email}`);
      // Clear the expired OTP
      await this.clearOTP(user.id);
      return false;
    }

    // Check if OTP matches
    if (user.otpCode !== otpToVerify) {
      this.logger.warn(`Invalid OTP for user ${email}`);
      return false;
    }

    // Mark user as email verified and clear OTP
    await this.usersRepository.update(user.id, {
      isEmailVerified: true,
      otpCode: null,
      otpExpiresAt: null,
    });

    return true;
  }

  /**
   * Clear OTP record for a user
   * @param userId - User ID to clear OTP for
   */
  async clearOTP(userId: string): Promise<void> {
    await this.usersRepository.update(userId, {
      otpCode: null,
      otpExpiresAt: null,
    });
  }
}
