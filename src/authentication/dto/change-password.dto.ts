import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'class-validator';
import { USER_VALIDATION } from '@hirenetix/models/users/constants/user.constants';
import { ApiProperty } from '@nestjs/swagger';
import { Match } from '../../common/decorators/match.decorator';
import { AUTH_MESSAGES } from '../../common/constants/message.constants';
import { API_PROPERTY } from '../../common/constants/swagger.constants';
import { Transform } from 'class-transformer';
export class ChangePasswordDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: API_PROPERTY.EMAIL_ADDRESS,
  })
  @IsEmail()
  @IsNotEmpty()
  @MaxLength(USER_VALIDATION.EMAIL_MAX_LENGTH)
  @Transform(({ value }) => value.toLowerCase())
  email: string;

  @ApiProperty({
    example: 'NewPassword123!',
    description: API_PROPERTY.NEW_PASSWORD_THAT_MEETS_SECURITY_REQUIREMENTS,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]).*$/,
    {
      message: AUTH_MESSAGES.PASSWORD_MUST_CONTAINS,
    },
  )
  password: string;

  @ApiProperty({
    example: 'NewPassword123!',
    description: API_PROPERTY.CONFIRM_PASSWORD_THAT_MUST_MATCH_PASSWORD,
  })
  @IsString()
  @IsNotEmpty()
  @Match('password', { message: AUTH_MESSAGES.PASSWORD_DO_NOT_MATCH })
  confirmPassword: string;
}
