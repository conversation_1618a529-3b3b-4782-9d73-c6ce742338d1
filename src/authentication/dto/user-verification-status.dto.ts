import { ApiProperty } from '@nestjs/swagger';

export class UserVerificationStatusDto {
  @ApiProperty({
    description: 'Whether the user email is verified',
    example: false,
  })
  isEmailVerified: boolean;

  @ApiProperty({
    description: 'Whether the user is required to reset their password',
    example: false,
  })
  isPasswordResetRequired: boolean;

  @ApiProperty({
    description: 'Whether the user exists in the system',
    example: true,
  })
  userExists: boolean;
}
