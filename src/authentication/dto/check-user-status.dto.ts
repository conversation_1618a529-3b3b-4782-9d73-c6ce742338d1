import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { USER_VALIDATION } from '@hirenetix/models/users/constants/user.constants';

export class CheckUserStatusDto {
  @ApiProperty({
    description: 'The email of the user to check',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  @MaxLength(USER_VALIDATION.EMAIL_MAX_LENGTH)
  email: string;
}
