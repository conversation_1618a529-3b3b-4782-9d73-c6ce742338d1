import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { USER_VALIDATION } from '@hirenetix/models/users/constants/user.constants';

export class LoginDto {
  @IsEmail()
  @IsNotEmpty()
  @MaxLength(USER_VALIDATION.EMAIL_MAX_LENGTH)
  email: string;

  @IsString()
  @IsNotEmpty()
  password: string;
}
