import { ApiProperty } from '@nestjs/swagger';
import { UserSerializer } from '@hirenetix/models/users/serializers/user.serializer';

export class LoginResponseDto {
  @ApiProperty({ description: 'JWT access token' })
  token: { accessToken: string; expiresIn: string };

  @ApiProperty({ description: 'User details', type: UserSerializer })
  user: UserSerializer;

  @ApiProperty({
    description: 'User permissions grouped by module',
    example: {
      users: ['create_user', 'view_all_users', 'edit_user'],
      locations: ['create_location', 'view_all_locations'],
      roles: ['view_all_roles'],
    },
    type: 'object',
    additionalProperties: {
      type: 'array',
      items: {
        type: 'string',
      },
    },
  })
  permissions: Record<string, string[]>;
}
