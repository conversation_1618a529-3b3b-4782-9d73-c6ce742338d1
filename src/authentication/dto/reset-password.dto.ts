import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Matches, MinLength } from 'class-validator';
import { Match } from '../../common/decorators/match.decorator';
import { AUTH_MESSAGES } from '../../common/constants/message.constants';
import { API_PROPERTY } from '../../common/constants/swagger.constants';

/**
 * Data Transfer Object for reset password requests
 */
export class ResetPasswordDto {
  @ApiProperty({
    description: 'Password reset token received via email',
    example: '1a2b3c4d5e6f7g8h9i0j',
  })
  @IsString()
  @IsNotEmpty({ message: 'Token is required' })
  token: string;

  @ApiProperty({
    example: 'NewPassword123!',
    description: API_PROPERTY.NEW_PASSWORD_THAT_MEETS_SECURITY_REQUIREMENTS,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]).*$/,
    {
      message: AUTH_MESSAGES.PASSWORD_MUST_CONTAINS,
    },
  )
  password: string;

  @ApiProperty({
    example: 'NewPassword123!',
    description: API_PROPERTY.CONFIRM_PASSWORD_THAT_MUST_MATCH_PASSWORD,
  })
  @IsString()
  @IsNotEmpty()
  @Match('password', { message: AUTH_MESSAGES.PASSWORD_DO_NOT_MATCH })
  confirmPassword: string;
}
