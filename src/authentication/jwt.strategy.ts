import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { AUTH_MESSAGES } from '@hirenetix/common/constants/message.constants';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('auth.jwtSecret'),
    });
  }

  async validate(payload: JwtPayload, request: any): Promise<any> {
    // Check if request is defined
    if (request && request.headers) {
      // Extract the token from Authorization header
      const token = ExtractJwt.fromAuthHeaderAsBearerToken()(request);

      // Check if token is blacklisted
      if (token) {
        const isBlacklisted = await this.cacheManager.get(`blacklist:${token}`);
        if (isBlacklisted) {
          throw new UnauthorizedException(AUTH_MESSAGES.TOKEN_INVALID);
        }
      }
    }

    return {
      id: payload.sub,
      email: payload.email,
      role: payload.role,
      tenantId: payload.tenantId,
    };
  }
}
