import {
  Injectable,
  BadRequestException,
  Inject,
  forwardRef,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { UsersService } from '@hirenetix/modules/users/users.service';
import { LoginDto } from './dto/login.dto';
import { Token } from './interfaces/token.interface';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { UserSerializer } from '@hirenetix/models/users/serializers/user.serializer';
import { UserRoleAssignmentsRepository } from '@hirenetix/models/roles/repositories/user-role-assignments.repository';
import { AUTH_MESSAGES } from '@hirenetix/common/constants/message.constants';
import { OTPService } from '@hirenetix/common/module/otp/otp.service';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { API_RESPONSES } from '../common/constants/swagger.constants';
import { CheckUserStatusDto } from './dto/check-user-status.dto';
import { UserVerificationStatusDto } from './dto/user-verification-status.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { LogoutDto } from './dto/logout.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { PermissionsService } from '@hirenetix/modules/roles-permissions/permissions.service';
import { User } from '@hirenetix/models/users/entities/user.entity';
import * as bcrypt from 'bcrypt';
import { UserStatus } from '@hirenetix/models/users/constants/user.constants';
import { CheckResetTokenDto } from './dto/check-reset-token.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly permissionsService: PermissionsService,
    private readonly configService: ConfigService,
    private readonly userRoleAssignmentsRepository: UserRoleAssignmentsRepository,
    @Inject(forwardRef(() => OTPService))
    private readonly otpService: OTPService,
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
  ) {}

  async validateUser(email: string, pass: string): Promise<User | null> {
    try {
      const user = await this.usersService.findByEmail(email);
      if (!user) return null;

      const isPasswordValid = await bcrypt.compare(pass, user.passwordHash);
      if (!isPasswordValid) return null;

      return user;
    } catch (error) {
      console.error('Error validating user:', error);
      return null;
    }
  }

  async login(loginDto: LoginDto): Promise<LoginResponseDto> {
    const user = await this.validateUser(loginDto.email, loginDto.password);

    if (!user) {
      throw new UnauthorizedException(AUTH_MESSAGES.INVALID_CREDENTIALS);
    }

    if (user.status === UserStatus.INACTIVE) {
      throw new UnauthorizedException(AUTH_MESSAGES.ACCOUNT_INACTIVE);
    }

    // Load user with role assignments
    const userWithRoles = await this.usersService.findById(user.id);

    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: userWithRoles.roleDetails?.[0]
        ? [
            {
              id: userWithRoles.roleDetails[0].id,
              name: userWithRoles.roleDetails[0].name,
            },
          ]
        : [
            {
              id: null,
              name: null,
            },
          ],
      tenantId: user.tenantId || null,
    };

    const accessToken = this.jwtService.sign(payload);

    // Get user permissions grouped by module
    const { permissions } =
      await this.permissionsService.getUserPermissionsByModule(user.id);

    return {
      token: {
        accessToken,
        expiresIn: this.configService.get<string>('auth.jwtExpiresIn'),
      },
      user: userWithRoles,
      permissions,
    };
  }

  /**
   * Verify an OTP code
   * @param verifyOtpDto - The DTO containing userId and otpCode
   * @returns Object with success status and message
   */
  async verifyOtp(
    verifyOtpDto: VerifyOtpDto,
  ): Promise<{ success: boolean; message: string }> {
    const { email, otpCode } = verifyOtpDto;

    // Verify the OTP
    const isValid = await this.otpService.verifyOTP(email, otpCode);

    if (!isValid) {
      throw new BadRequestException(API_RESPONSES.INVALID_OTP);
    }

    return {
      success: true,
      message: AUTH_MESSAGES.OTP_VERIFIED_SUCCESSFULLY,
    };
  }

  /**
   * Check user verification status
   * @param checkUserStatusDto - The DTO containing the email of the user to check
   * @returns Object with user verification status information
   */
  async checkUserStatus(
    checkUserStatusDto: CheckUserStatusDto,
  ): Promise<UserVerificationStatusDto> {
    const { email } = checkUserStatusDto;

    try {
      // Try to find the user by email
      const user = await this.usersService.findByEmail(email);

      // Return user status information
      return {
        isEmailVerified: user.isEmailVerified,
        isPasswordResetRequired: user.isPasswordResetRequired,
        userExists: true,
      };
    } catch (error) {
      // If user is not found (or other error occurs), return default values
      console.log(`Error checking user status: ${error.message}`);
      return {
        isEmailVerified: false,
        isPasswordResetRequired: false,
        userExists: false,
      };
    }
  }

  /**
   * Change a user's password and generate a new JWT token
   * @param changePasswordDto - The DTO containing email, password and confirmation
   * @returns Object with user and token information
   */
  async changePassword(
    changePasswordDto: ChangePasswordDto,
  ): Promise<{ user: UserSerializer; token: Token; permissions: any }> {
    const { email, password } = changePasswordDto;

    const user = await this.usersService.changePassword(email, password);

    const userRoleAssignments =
      await this.userRoleAssignmentsRepository.getUserRoles(user.id);

    const primaryRole =
      userRoleAssignments.length > 0 ? userRoleAssignments[0].role : null;
    const roleId = primaryRole ? primaryRole.id : null;
    const roleName = primaryRole ? primaryRole.name : null;

    const userWithRoles = {
      ...user,
      roleId,
      roleName,
    };

    // Generate new token
    const token = this.generateToken(userWithRoles);

    // Get user permissions grouped by module
    const { permissions } =
      await this.permissionsService.getUserPermissionsByModule(user.id);

    return { user, token, permissions };
  }

  /**
   * Initiate the password reset process
   * @param forgotPasswordDto - The DTO containing user's email
   * @returns Object with success status and message
   */
  async forgotPassword(
    forgotPasswordDto: ForgotPasswordDto,
  ): Promise<{ success: boolean; message: string }> {
    const { email } = forgotPasswordDto;
    return this.usersService.createPasswordResetToken(email);
  }

  /**
   * Reset a user's password using a reset token
   * @param resetPasswordDto - The DTO containing the token and new password
   * @returns Object with user and token information
   */
  async resetPassword(
    resetPasswordDto: ResetPasswordDto,
  ): Promise<{ user: UserSerializer; token: Token }> {
    const { token, password } = resetPasswordDto;

    // Validate token and get user
    const user = await this.usersService.resetPassword(token, password);

    // Fetch user roles
    const userRoleAssignments =
      await this.userRoleAssignmentsRepository.getUserRoles(user.id);

    // Get primary role ID and name (first role in the list)
    const primaryRole =
      userRoleAssignments.length > 0 ? userRoleAssignments[0].role : null;
    const roleId = primaryRole ? primaryRole.id : null;
    const roleName = primaryRole ? primaryRole.name : null;

    // Add roles to the user object
    const userWithRoles = {
      ...user,
      roleId,
      roleName,
    };

    // Generate token
    const jwtToken = this.generateToken(userWithRoles);

    return { user, token: jwtToken };
  }

  async checkResetToken(
    checkResetTokenDto: CheckResetTokenDto,
  ): Promise<{ message: string; data: any }> {
    const { token } = checkResetTokenDto;

    const isTokenValid = await this.usersService.validateResetToken(token);

    const user = isTokenValid?.user
      ? {
          email: isTokenValid.user.email,
          fullName: isTokenValid.user.fullName,
          userId: isTokenValid.user.id,
          tenantId: isTokenValid.user.tenantId,
        }
      : null;

    return {
      message: 'API executed successfully',
      data: {
        isValid: isTokenValid.isValid,
        user,
      },
    };
  }

  /**
   * Logout a user by invalidating their JWT token
   * @param logoutDto - The DTO containing the access token to invalidate
   * @returns Object with success status and message
   */
  async logout(
    logoutDto: LogoutDto,
  ): Promise<{ success: boolean; message: string }> {
    const { accessToken } = logoutDto;

    try {
      // Verify and decode the token
      const decodedToken = this.jwtService.verify(accessToken) as JwtPayload;

      // Calculate token's remaining time to live (TTL)
      const expiryDate = new Date((decodedToken.exp || 0) * 1000);
      const now = new Date();
      const ttl = Math.max(
        0,
        Math.floor((expiryDate.getTime() - now.getTime()) / 1000),
      );

      if (ttl <= 0) {
        // Token is already expired, no need to blacklist
        return {
          success: true,
          message: AUTH_MESSAGES.LOGOUT_SUCCESS,
        };
      }

      // Add token to blacklist in Redis cache
      // Use the token itself as a key for direct lookup
      const blacklistKey = `blacklist:${accessToken}`;
      await this.cacheManager.set(blacklistKey, true, ttl);

      return {
        success: true,
        message: AUTH_MESSAGES.LOGOUT_SUCCESS,
      };
    } catch (error) {
      // If token is invalid, we can consider the logout successful as the token can't be used anyway
      console.log(`Error during logout: ${error.message}`);
      return {
        success: true,
        message: AUTH_MESSAGES.LOGOUT_SUCCESS,
      };
    }
  }

  private generateToken(user: any): Token {
    const expiresIn =
      this.configService.get<string>('auth.jwtExpiresIn') || '1d';

    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: [
        {
          id: user.roleId || null,
          name: user.roleName || null,
        },
      ],
      tenantId: user.tenantId || null,
    };

    const accessToken = this.jwtService.sign(payload);

    return {
      accessToken,
      expiresIn,
    };
  }
}
