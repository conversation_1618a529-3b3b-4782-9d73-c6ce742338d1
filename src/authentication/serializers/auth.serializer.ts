import { UserSerializer } from '@hirenetix/models/users/serializers/user.serializer';
import { Token } from '../interfaces/token.interface';

export class AuthSerializer {
  user: UserSerializer;
  token: Token;
  permissions: any;

  static serialize(
    user: UserSerializer,
    token: Token,
    permissions: any,
  ): AuthSerializer {
    const serialized = new AuthSerializer();
    serialized.user = user;
    serialized.token = token;
    serialized.permissions = permissions;
    return serialized;
  }
}
