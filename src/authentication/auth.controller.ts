import {
  Body,
  Controller,
  Post,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { AuthSerializer } from './serializers/auth.serializer';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  API_TAGS,
  API_OPERATIONS,
  API_RESPONSES,
} from '@hirenetix/common/constants';
import { CheckUserStatusDto } from './dto/check-user-status.dto';
import { UserVerificationStatusDto } from './dto/user-verification-status.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { LogoutDto } from './dto/logout.dto';
import { CheckResetTokenDto } from './dto/check-reset-token.dto';
import { ThrottlerGuard } from '@nestjs/throttler';

@ApiTags(API_TAGS.AUTH)
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @ApiOperation({ summary: API_OPERATIONS.LOGIN })
  @ApiResponse({
    status: HttpStatus.OK,
    description: API_RESPONSES.SUCCESS,
    type: AuthSerializer,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: API_RESPONSES.INVALID_CREDENTIALS,
  })
  async login(@Body() loginDto: LoginDto): Promise<AuthSerializer> {
    const { user, token, permissions } = await this.authService.login(loginDto);
    return AuthSerializer.serialize(user, token, permissions);
  }

  @Post('verify-otp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: API_OPERATIONS.VERIFY_OTP })
  @ApiResponse({
    status: HttpStatus.OK,
    description: API_RESPONSES.OTP_VERIFIED,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: API_RESPONSES.INVALID_OTP,
  })
  async verifyOtp(
    @Body() verifyOtpDto: VerifyOtpDto,
  ): Promise<{ success: boolean; message: string }> {
    return this.authService.verifyOtp(verifyOtpDto);
  }

  @Post('check-user-status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: API_OPERATIONS.CHECK_USER_STATUS })
  @ApiResponse({
    status: HttpStatus.OK,
    description: API_RESPONSES.USER_STATUS_RETRIEVED,
    type: UserVerificationStatusDto,
  })
  async checkUserStatus(
    @Body() checkUserStatusDto: CheckUserStatusDto,
  ): Promise<UserVerificationStatusDto> {
    return this.authService.checkUserStatus(checkUserStatusDto);
  }

  @Post('change-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: API_OPERATIONS.CHANGE_PASSWORD })
  @ApiResponse({
    status: HttpStatus.OK,
    description: API_RESPONSES.PASSWORD_CHANGED,
    type: AuthSerializer,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: API_RESPONSES.USER_NOT_FOUND,
  })
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
  ): Promise<AuthSerializer> {
    console.log(changePasswordDto);
    const { user, token, permissions } =
      await this.authService.changePassword(changePasswordDto);
    return AuthSerializer.serialize(user, token, permissions);
  }

  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: API_OPERATIONS.FORGOT_PASSWORD })
  @ApiResponse({
    status: HttpStatus.OK,
    description: API_RESPONSES.PASSWORD_RESET_EMAIL_SENT,
  })
  async forgotPassword(
    @Body() forgotPasswordDto: ForgotPasswordDto,
  ): Promise<{ success: boolean; message: string }> {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: API_OPERATIONS.RESET_PASSWORD })
  @ApiResponse({
    status: HttpStatus.OK,
    description: API_RESPONSES.PASSWORD_RESET_SUCCESS,
    type: AuthSerializer,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: API_RESPONSES.INVALID_OR_EXPIRED_TOKEN,
  })
  async resetPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
  ): Promise<AuthSerializer> {
    const { user, token } =
      await this.authService.resetPassword(resetPasswordDto);
    return AuthSerializer.serialize(user, token, null);
  }

  @Post('validate-reset-token')
  @UseGuards(ThrottlerGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: API_OPERATIONS.CHECK_RESET_TOKEN })
  @ApiResponse({
    status: HttpStatus.OK,
    description: API_RESPONSES.TOKEN_VALID,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: API_RESPONSES.INVALID_OR_EXPIRED_TOKEN,
  })
  async checkResetToken(
    @Body() checkResetTokenDto: CheckResetTokenDto,
  ): Promise<{ message: string; data: any }> {
    return this.authService.checkResetToken(checkResetTokenDto);
  }

  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: API_OPERATIONS.LOGOUT })
  @ApiResponse({
    status: HttpStatus.OK,
    description: API_RESPONSES.LOGOUT_SUCCESS,
  })
  async logout(
    @Body() logoutDto: LogoutDto,
  ): Promise<{ success: boolean; message: string }> {
    return this.authService.logout(logoutDto);
  }
}
