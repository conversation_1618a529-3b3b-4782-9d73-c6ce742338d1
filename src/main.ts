import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DEFAULT_PORT, API_PREFIX, API_VERSION } from './common/constants';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule } from '@nestjs/swagger';
import {
  createSwaggerConfig,
  swaggerCustomOptions,
} from './config/swagger/swagger.config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS
  app.enableCors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  // Setup API prefix
  app.setGlobalPrefix(`${API_PREFIX}/${API_VERSION}`);

  // Enable validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Setup Swagger
  const config = createSwaggerConfig();
  const document = SwaggerModule.createDocument(app, config);

  // Add swagger JSON output for API documentation tools
  SwaggerModule.setup(
    `${API_PREFIX}/docs`,
    app,
    document,
    swaggerCustomOptions,
  );

  await app.listen(process.env.PORT || DEFAULT_PORT);
  console.log(`Application is running on: ${await app.getUrl()}`);
}
bootstrap();
