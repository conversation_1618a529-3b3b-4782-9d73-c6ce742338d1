import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';

import { AppController } from '@hirenetix/app.controller';
import { AppService } from '@hirenetix/app.service';
import { DatabaseConfigModule } from '@hirenetix/config/database/postgres/config.module';
import { PostgresModule } from '@hirenetix/providers/database/postgres/provider.module';
import { MongodbConfigModule } from '@hirenetix/config/database/mongodb/config.module';
import { MongodbModule } from '@hirenetix/providers/database/mongodb/provider.module';
import { RedisConfigModule } from '@hirenetix/config/cache/redis/config.module';
import { RedisCacheModule } from '@hirenetix/providers/cache/redis/provider.module';
import { AuthModule } from '@hirenetix/authentication/auth.module';
import { UsersModule } from '@hirenetix/modules/users/users.module';
import { AuthConfigModule } from '@hirenetix/config/auth/config.module';
import { CommonModule } from '@hirenetix/common/common.module';
import { OrganisationsModule } from '@hirenetix/modules/organisations/organisations.module';
import { DepartmentsModule } from '@hirenetix/modules/departments/departments.module';
import { SwaggerModule } from '@hirenetix/config/swagger/swagger.module';
import { TenantModule } from '@hirenetix/modules/tenant/tenant.module';
import { StorageConfigModule } from '@hirenetix/config/storage/config.module';
import { SuperAdminModule } from '@hirenetix/modules/super-admin/super-admin.module';
import { LocationsModule } from '@hirenetix/modules/locations/locations.module';
import { JobsModule } from '@hirenetix/modules/jobs/jobs.module';
import { AiJobPostModule } from '@hirenetix/modules/jobs/ai-job-post/ai-job-post.module';
import { CandidatesModule } from '@hirenetix/modules/candidates/candidates.module';
import { ConversationsModule } from '@hirenetix/modules/conversations/conversations.module';
import { UploadsModule } from './modules/s3-uploads/uploads.module';
import { ContactModule } from './modules/contact/contact.module';
import { AiInsightsModule } from './modules/jobs/ai-insights/ai-insights.module';
import { InterviewSlotsModule } from './modules/interviews/interview-slots.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ScheduleModule.forRoot(),
    AiInsightsModule,
    DatabaseConfigModule,
    PostgresModule,
    MongodbConfigModule,
    MongodbModule,
    RedisConfigModule,
    RedisCacheModule,
    AuthConfigModule,
    AuthModule,
    UsersModule,
    CommonModule,
    OrganisationsModule,
    DepartmentsModule,
    SwaggerModule,
    TenantModule,
    StorageConfigModule,
    SuperAdminModule,
    LocationsModule,
    JobsModule,
    AiJobPostModule,
    CandidatesModule,
    ConversationsModule,
    UploadsModule,
    ContactModule,
    InterviewSlotsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
