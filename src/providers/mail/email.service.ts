import { Injectable, Logger } from '@nestjs/common';
import { SesMailService } from './ses/ses-mail.service';
import { EmailTemplateService } from './templates/email-template.service';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(
    private readonly sesMailService: SesMailService,
    private readonly emailTemplateService: EmailTemplateService,
  ) {}

  /**
   * Send a custom email
   * @param to - Recipient email or array of emails
   * @param subject - Email subject
   * @param html - HTML content
   * @param text - Optional plain text version
   * @param from - Optional sender email
   */
  async sendEmail(
    to: string | string[],
    subject: string,
    html: string,
    text?: string,
    from?: string,
  ): Promise<void> {
    try {
      if (text) {
        await this.sesMailService.sendMultipartEmail(
          to,
          subject,
          html,
          text,
          from,
        );
      } else {
        await this.sesMailService.sendEmail(to, subject, html, from);
      }
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send a text-only email
   * @param to - Recipient email or array of emails
   * @param subject - Email subject
   * @param text - Plain text content
   * @param from - Optional sender email
   */
  async sendTextEmail(
    to: string | string[],
    subject: string,
    text: string,
    from?: string,
  ): Promise<void> {
    try {
      await this.sesMailService.sendTextEmail(to, subject, text, from);
    } catch (error) {
      this.logger.error(
        `Failed to send text email: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Send a welcome email
   * @param to - Recipient email
   * @param username - User's name
   * @param otp - Optional OTP code for verification
   */
  async sendWelcomeEmail(
    to: string,
    username: string,
    otp?: string,
  ): Promise<void> {
    try {
      const subject = 'Welcome to HireNetix';
      const html = this.emailTemplateService.generateWelcomeEmail(
        username,
        otp,
        to,
      );
      await this.sesMailService.sendEmail(to, subject, html);
      this.logger.log(`Welcome email sent to ${to}`);
    } catch (error) {
      this.logger.error(
        `Failed to send welcome email: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Send a password reset email
   * @param to - Recipient email
   * @param username - User's name
   * @param resetLink - Password reset URL
   */
  async sendPasswordResetEmail(
    to: string,
    username: string,
    resetLink: string,
  ): Promise<void> {
    try {
      const subject = 'Reset Your Password';
      const html = this.emailTemplateService.generatePasswordResetEmail(
        username,
        resetLink,
      );
      await this.sesMailService.sendEmail(to, subject, html);
      this.logger.log(`Password reset email sent to ${to}`);
    } catch (error) {
      this.logger.error(
        `Failed to send password reset email: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Send a password reset email
   * @param to - Recipient email
   * @param username - User's name
   * @param resetLink - Password reset URL
   */
  async sendInterviewScheduledAdminEmail(to: string, data: any): Promise<void> {
    try {
      const subject = `Candidate Interview Scheduled: ${data?.candidateName} for ${data?.jobTitle} on ${data?.interviewDate} at ${data?.interviewTime}`;
      const html =
        this.emailTemplateService.generateInterviewScheduledAdminEmail(
          data?.adminName,
          data?.candidateName,
          data?.jobTitle,
          data?.interviewDate,
          data?.interviewTime,
          data?.interviewLink,
          data?.duration,
          data?.interviewMethod,
          data?.interviewLocation,
          data?.candidatePhoneNumber,
          data?.candidateProfileLink,
        );
      await this.sesMailService.sendEmail(to, subject, html);
      this.logger.log(`Interview scheduled admin email sent to ${to}`);
    } catch (error) {
      this.logger.error(
        `Failed to send interview scheduled admin email: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async sendInterviewScheduledCandidateEmail(
    to: string,
    data: any,
  ): Promise<void> {
    try {
      const subject = `Interview Confirmation: Your ${data?.jobTitle} Interview at ${data?.companyName} - ${data?.interviewDate}`;
      const html =
        this.emailTemplateService.generateInterviewScheduledCandidateEmail(
          data?.adminName,
          data?.candidateName,
          data?.jobTitle,
          data?.companyName,
          data?.interviewDate,
          data?.interviewTime,
          data?.interviewMethod,
          data?.interviewLocation,
          data?.duration,
          data?.reachBeforeTime,
          data?.candidatePhoneNumber,
          data?.interviewLink,
        );
      await this.sesMailService.sendEmail(to, subject, html);
      this.logger.log(`Interview scheduled candidate email sent to ${to}`);
    } catch (error) {
      this.logger.error(
        `Failed to send interview scheduled candidate email: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
