import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  SESClient,
  SendEmailCommand,
  SendEmailCommandInput,
} from '@aws-sdk/client-ses';

@Injectable()
export class SesMailService {
  private readonly sesClient: SESClient;
  private readonly logger = new Logger(SesMailService.name);
  private readonly defaultFromEmail: string;

  constructor(private readonly configService: ConfigService) {
    // Initialize the SES client with AWS config
    this.sesClient = new SESClient({
      region: this.configService.get<string>('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
        ),
      },
    });

    // Get the default sender email from config
    this.defaultFromEmail = this.configService.get<string>('SES_EMAIL_FROM');
  }

  /**
   * Send an email using AWS SES
   * @param to - recipient email address(es)
   * @param subject - email subject
   * @param html - email content as HTML
   * @param from - optional sender email, defaults to configured SES_EMAIL_FROM
   * @returns Promise that resolves with the SES response
   */
  async sendEmail(
    to: string | string[],
    subject: string,
    html: string,
    from?: string,
  ): Promise<void> {
    try {
      const toAddresses = Array.isArray(to) ? to : [to];

      // Prepare email parameters
      const params: SendEmailCommandInput = {
        Source: from || this.defaultFromEmail,
        Destination: {
          ToAddresses: toAddresses,
        },
        Message: {
          Subject: {
            Data: subject,
            Charset: 'UTF-8',
          },
          Body: {
            Html: {
              Data: html,
              Charset: 'UTF-8',
            },
          },
        },
      };

      // Send the email
      const command = new SendEmailCommand(params);
      await this.sesClient.send(command);

      this.logger.log(`Email sent successfully to ${toAddresses.join(', ')}`);
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send a text-only email
   * @param to - recipient email address(es)
   * @param subject - email subject
   * @param text - email content as plain text
   * @param from - optional sender email
   */
  async sendTextEmail(
    to: string | string[],
    subject: string,
    text: string,
    from?: string,
  ): Promise<void> {
    try {
      const toAddresses = Array.isArray(to) ? to : [to];

      const params: SendEmailCommandInput = {
        Source: from || this.defaultFromEmail,
        Destination: {
          ToAddresses: toAddresses,
        },
        Message: {
          Subject: {
            Data: subject,
            Charset: 'UTF-8',
          },
          Body: {
            Text: {
              Data: text,
              Charset: 'UTF-8',
            },
          },
        },
      };

      const command = new SendEmailCommand(params);
      await this.sesClient.send(command);

      this.logger.log(
        `Text email sent successfully to ${toAddresses.join(', ')}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send text email: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Send an email with HTML and plain text alternatives
   * @param to - recipient email address(es)
   * @param subject - email subject
   * @param html - HTML version of the email
   * @param text - plain text version of the email
   * @param from - optional sender email
   */
  async sendMultipartEmail(
    to: string | string[],
    subject: string,
    html: string,
    text: string,
    from?: string,
  ): Promise<void> {
    try {
      const toAddresses = Array.isArray(to) ? to : [to];

      const params: SendEmailCommandInput = {
        Source: from || this.defaultFromEmail,
        Destination: {
          ToAddresses: toAddresses,
        },
        Message: {
          Subject: {
            Data: subject,
            Charset: 'UTF-8',
          },
          Body: {
            Html: {
              Data: html,
              Charset: 'UTF-8',
            },
            Text: {
              Data: text,
              Charset: 'UTF-8',
            },
          },
        },
      };

      const command = new SendEmailCommand(params);
      await this.sesClient.send(command);

      this.logger.log(
        `Multipart email sent successfully to ${toAddresses.join(', ')}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send multipart email: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
