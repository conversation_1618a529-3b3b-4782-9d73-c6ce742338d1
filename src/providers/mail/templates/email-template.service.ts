import { Injectable } from '@nestjs/common';

@Injectable()
export class EmailTemplateService {
  /**
   * Generate a simple HTML email template
   * @param title - The email title or subject
   * @param content - The main content of the email (can be HTML)
   * @param buttonText - Optional button text
   * @param buttonUrl - Optional button URL
   * @param footerText - Optional footer text
   */
  generateTemplate(
    title: string,
    content: string,
    buttonText?: string,
    buttonUrl?: string,
    footerText?: string,
  ): string {
    // Basic styling for the email template
    const styles = `
      body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
      .header { text-align: center; margin-bottom: 30px; }
      .title { font-size: 24px; font-weight: bold; color: #2c3e50; margin-bottom: 20px; }
      .content { margin-bottom: 30px; }
      .button-container { text-align: center; margin: 30px 0; }
      .button { display: inline-block; background-color: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; }
      .footer { border-top: 1px solid #eee; padding-top: 20px; font-size: 12px; color: #777; text-align: center; }
    `;

    // Optional button HTML
    const buttonHtml =
      buttonText && buttonUrl
        ? `
         <div class="button-container">
           <a href="${buttonUrl}" class="button">${buttonText}</a>
         </div>
       `
        : '';

    // Optional footer HTML
    const footerHtml = footerText
      ? `
         <div class="footer">
           ${footerText}
         </div>
       `
      : '';

    // The complete HTML template
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${title}</title>
          <style>${styles}</style>
        </head>
        <body>
          <div class="header">
            <div class="title">${title}</div>
          </div>
          <div class="content">
            ${content}
          </div>
          ${buttonHtml}
          ${footerHtml}
        </body>
      </html>
    `;
  }

  /**
   * Generate a welcome email template
   * @param username - The user's name
   * @param otp - The one-time password for user verification
   */
  generateWelcomeEmail(username: string, otp?: string, email?: string): string {
    const title = 'Welcome to HireNetix';
    let content = `
      <p>Hello ${username},</p>
      <p>Welcome to HireNetix! We're excited to have you join our platform.</p>
      <p>Get started by completing your profile and exploring our services.</p>
    `;

    if (otp) {
      content += `
        <p style="font-size: 18px; font-weight: bold; margin-top: 20px;">Your verification code: <span style="color: #3498db; letter-spacing: 2px;">${otp}</span></p>
        <p>Please use this code to verify your account. This code will expire in 24 hours.</p>
      `;
    }

    const buttonText = 'Go to Dashboard';
    const buttonUrl =
      process.env.FRONTEND_URL + `?email=${encodeURIComponent(email)}`;
    const footerText = '© HireNetix. All rights reserved.';

    return this.generateTemplate(
      title,
      content,
      buttonText,
      buttonUrl,
      footerText,
    );
  }

  /**
   * Generate a password reset email template
   * @param username - The user's name
   * @param resetLink - The password reset link
   */
  generatePasswordResetEmail(username: string, resetLink: string): string {
    const title = 'Reset Your Password';
    const content = `
      <p>Hello ${username},</p>
      <p>We received a request to reset your password. Please click the button below to create a new password.</p>
      <p>If you did not request a password reset, you can safely ignore this email.</p>
      <p>This link will expire in 1 hour.</p>
    `;
    const buttonText = 'Reset Password';
    const footerText = '© HireNetix. All rights reserved.';

    return this.generateTemplate(
      title,
      content,
      buttonText,
      resetLink,
      footerText,
    );
  }

  generateInterviewScheduledAdminEmail(
    adminName: string,
    candidateName: string,
    jobTitle: string,
    interviewDate: string,
    interviewTime: string,
    interviewLink: string,
    duration: string,
    interviewMethod: string,
    interviewLocation: string,
    candidatePhoneNumber: string,
    candidateProfileLink: string,
  ): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8" />
      <title>Candidate Interview Scheduled</title>
      <style>
        body {
          font-family: 'Segoe UI', sans-serif;
          background-color: #f4f6f8;
          margin: 0;
          padding: 0;
        }
        .email-wrapper {
          width: 100%;
          padding: 20px;
        }
        .container {
          max-width: 700px;
          margin: 0 auto;
          background: #ffffff;
          border-radius: 8px;
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
          overflow: hidden;
        }
        .header {
          background-color: #003366;
          color: #ffffff;
          padding: 20px 30px;
          text-align: center;
        }
        .header h2 {
          margin: 0;
          font-size: 20px;
        }
        .content {
          padding: 30px;
        }
        h3 {
          color: #003366;
          margin-top: 30px;
          margin-bottom: 10px;
        }
        p {
          line-height: 1.6;
          margin: 10px 0;
        }
        .section {
          border-top: 1px solid #eee;
          padding-top: 20px;
          margin-top: 20px;
        }
        .footer {
          font-size: 0.9em;
          color: #777;
          text-align: center;
          padding: 20px 30px;
          background-color: #f1f1f1;
        }
        a {
          color: #1a73e8;
          text-decoration: none;
        }
      </style>
    </head>
    <body>
      <div class="email-wrapper">
        <div class="container">
          <div class="header">
            <h2>Interview Scheduled: ${candidateName} for ${jobTitle}</h2>
          </div>
          <div class="content">
            <p>Hi <strong>${adminName}</strong>,</p>
  
            <p>This email confirms an interview scheduled on your calendar with <strong>${candidateName}</strong> for the <strong>${jobTitle}</strong> position.</p>
  
            <p>They were shortlisted based on their application and profile review.</p>
  
            <div class="section">
              <h3>Interview Details</h3>
              <p><strong>Candidate:</strong> ${candidateName}</p>
              <p><strong>Position:</strong> ${jobTitle}</p>
              <p><strong>Date:</strong> ${interviewDate}</p>
              <p><strong>Time:</strong> ${interviewTime}</p>
              <p><strong>Duration:</strong> Approximately ${duration} minutes</p>
              <p><strong>Method:</strong> ${interviewMethod}</p>
            </div>
  
            <div class="section">
              <h3>Connection Info</h3>
              <p>
                ${
                  interviewMethod === 'Video Call'
                    ? `Join the meeting here: <a href="${interviewLink}" target="_blank">${interviewLink}</a>`
                    : ''
                }
                ${
                  interviewMethod === 'In-Person'
                    ? `Location: ${interviewLocation}`
                    : ''
                }
                ${
                  interviewMethod === 'Phone Call'
                    ? `Please call the candidate at <strong>${candidatePhoneNumber}</strong> at the scheduled time.`
                    : ''
                }
              </p>
            </div>
  
            <div class="section">
              <h3>Candidate Profile & Resources</h3>
              <p>
                View <strong>${candidateName}</strong>'s complete profile, including resume, application, AI insights, and interview Q&A here:<br />
                <a href="${candidateProfileLink}" target="_blank">${candidateProfileLink}</a>
              </p>
            </div>
  
            <p>Please take a moment to review their profile before the interview.</p>
            <p>If you have any questions or need to reschedule, please notify the team promptly.</p>
  
            <p>Thanks,<br /><strong>Team HireNetix</strong></p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply directly to this email.</p>
          </div>
        </div>
      </div>
    </body>
    </html>
    `;
  }

  generateInterviewScheduledCandidateEmail(
    adminName: string,
    candidateName: string,
    jobTitle: string,
    companyName: string,
    interviewDate: string,
    interviewTime: string,
    interviewMethod: string,
    interviewLocation: string,
    duration: string,
    reachBeforeTime: string,
    candidatePhoneNumber: string,
    interviewLink: string,
  ): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8" />
      <title>Interview Confirmation</title>
      <style>
        body {
          font-family: 'Segoe UI', sans-serif;
          background-color: #f4f6f8;
          margin: 0;
          padding: 0;
          color: #333;
        }
        .email-wrapper {
          width: 100%;
          padding: 20px;
        }
        .container {
          max-width: 700px;
          margin: 0 auto;
          background: #ffffff;
          border-radius: 8px;
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
          overflow: hidden;
        }
        .header {
          background-color: #003366;
          color: #ffffff;
          padding: 20px 30px;
          text-align: center;
        }
        .header h2 {
          margin: 0;
          font-size: 20px;
        }
        .content {
          padding: 30px;
        }
        h3 {
          color: #003366;
          margin-top: 30px;
          margin-bottom: 10px;
        }
        p {
          line-height: 1.6;
          margin: 10px 0;
        }
        .section {
          border-top: 1px solid #eee;
          padding-top: 20px;
          margin-top: 20px;
        }
        a {
          color: #1a73e8;
          text-decoration: none;
        }
        .footer {
          font-size: 0.9em;
          color: #777;
          text-align: center;
          padding: 20px 30px;
          background-color: #f1f1f1;
        }
      </style>
    </head>
    <body>
      <div class="email-wrapper">
        <div class="container">
          <div class="header">
            <h2>Interview Confirmation: Your ${jobTitle} Interview at ${companyName} - ${interviewDate}</h2>
          </div>
          <div class="content">
            <p>Dear <strong>${candidateName}</strong>,</p>
  
            <p>Thank you for applying for the <strong>${jobTitle}</strong> position at <strong>${companyName}</strong>. We truly appreciate your interest and the time you took to share your qualifications.</p>
  
            <p>We're excited to invite you to an interview to learn more about your background and to share details about the role and our team.</p>
  
            <div class="section">
              <h3>Interview Details</h3>
              <p><strong>Position:</strong> ${jobTitle}</p>
              <p><strong>Date:</strong> ${interviewDate}</p>
              <p><strong>Time:</strong> ${interviewTime}</p>
              <p><strong>Interview Method:</strong> ${interviewMethod}</p>
            </div>
  
            <div class="section">
              <h3>Connection Info</h3>
              <p>
                ${
                  interviewMethod === 'Video Call'
                    ? `Please join the meeting here: <a href="${interviewLink}" target="_blank">${interviewLink}</a><br>`
                    : ''
                }
                ${
                  interviewMethod === 'In-Person'
                    ? `Our office address is: ${interviewLocation}. Please check in at reception upon arrival.<br>`
                    : ''
                }
                ${
                  interviewMethod === 'Phone Call'
                    ? `${adminName} will call you at <strong>${candidatePhoneNumber}</strong> at the scheduled time.<br>`
                    : ''
                }
              </p>
            </div>
  
            <p>The interview is expected to last approximately <strong>${duration}</strong>.</p>
  
            <div class="section">
              <h3>What to Expect / Prepare</h3>
              <p><strong>For Video Call:</strong> Ensure a stable internet connection and a quiet environment. It's a good idea to test the link a few minutes in advance.</p>
              <p><strong>For In-Person:</strong> Please arrive <strong>${reachBeforeTime}</strong> minutes before your scheduled interview time.</p>
            </div>
  
            <p>You're welcome to bring any questions about the role, our company, or team — we're happy to help.</p>
  
            <p>We look forward to connecting with you soon!</p>
  
            <p>If you need to reschedule or have any questions, simply reply to this email.</p>
  
            <p>Sincerely,<br><strong>The Hiring Team</strong><br>HireNetix</p>
          </div>
          <div class="footer">
            <p>This is an automated message from HireNetix. Please do not reply directly to this email.</p>
          </div>
        </div>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Generate a contact form submission email template
   * @param userObj - The user's contact form response
   */
  generateContactFormSubmissionEmail(userObj: any): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Contact Form Submission</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          background: #f9f9f9;
          margin: 0;
          padding: 20px;
        }
        .container {
          background: #ffffff;
          padding: 24px;
          border-radius: 8px;
          max-width: 600px;
          margin: auto;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        h2 {
          margin-top: 0;
          color: #333333;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 16px;
        }
        td {
          padding: 12px 8px;
          border-bottom: 1px solid #eaeaea;
          vertical-align: top;
        }
        td.label {
          font-weight: bold;
          color: #555555;
          width: 160px;
        }
        td.value {
          color: #333333;
        }
        .footer {
          font-size: 12px;
          color: #888888;
          margin-top: 24px;
          text-align: center;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h2 style="text-align: center;">New Contact Form Submission</h2>
        <table>
          <tr>
            <td class="label">Full Name:</td>
            <td class="value">${userObj.fullName}</td>
          </tr>
          <tr>
            <td class="label">Business Email:</td>
            <td class="value">${userObj.businessEmail}</td>
          </tr>
          <tr>
            <td class="label">Company Name:</td>
            <td class="value">${userObj.companyName}</td>
          </tr>
          <tr>
            <td class="label">Description:</td>
            <td class="value">${userObj.description}</td>
          </tr>
          <tr>
            <td class="label">Requested Demo:</td>
            <td class="value">${userObj.hasRequestedDemo ? 'Yes' : 'No'}</td>
          </tr>
        </table>
        <div class="footer">
          This message was generated from the contact form on your website.
        </div>
      </div>
    </body>
    </html>
  `;
  }
}
