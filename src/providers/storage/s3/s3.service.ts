import { Injectable } from '@nestjs/common';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { StorageConfigService } from '../../../config/storage/config.service';

@Injectable()
export class S3Service {
  private s3Client: S3Client;

  constructor(private readonly storageConfigService: StorageConfigService) {
    this.s3Client = new S3Client({
      region: this.storageConfigService.region,
      credentials: {
        accessKeyId: this.storageConfigService.accessKeyId,
        secretAccessKey: this.storageConfigService.secretAccessKey,
      },
      endpoint: this.storageConfigService.endpoint,
    });
  }

  /**
   * Generates a presigned URL for uploading a file to S3
   *
   * @param key - The object key (file path in S3 bucket)
   * @param contentType - The content type of the file
   * @param expiresIn - URL expiration time in seconds (default: 3600s = 1 hour)
   * @returns Promise with the presigned URL
   */
  async getPresignedUploadUrl(
    key: string,
    contentType: string,
    expiresIn = 3600,
  ): Promise<string> {
    const command = new PutObjectCommand({
      Bucket: this.storageConfigService.bucket,
      Key: key,
      ContentType: contentType,
    });

    return getSignedUrl(this.s3Client, command, { expiresIn });
  }
}
