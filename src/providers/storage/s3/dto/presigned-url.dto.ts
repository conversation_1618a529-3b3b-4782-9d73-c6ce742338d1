import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, Matches } from 'class-validator';

export class PresignedUrlRequestDto {
  @ApiProperty({
    description: 'The file name of the file to be uploaded',
    example: 'logo.png',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-Z0-9!_.*'()-\/]+$/, {
    message: 'File key contains invalid characters',
  })
  filename: string;

  @ApiProperty({
    description: 'Content type of the file (required for upload)',
    example: 'image/jpeg',
    required: false,
  })
  @IsString()
  @IsOptional()
  contentType?: string;
}

export class PresignedUrlResponseDto {
  @ApiProperty({
    description: 'The presigned URL',
    example: 'https://bucket.s3.amazonaws.com/key?signature...',
  })
  url: string;

  @ApiProperty({
    description: 'The file key in the S3 bucket',
    example: 'organisations/logo/logo.png',
  })
  key: string;

  @ApiProperty({
    description: 'URL expiration time in seconds',
    example: 3600,
  })
  expiresIn: number;
}
