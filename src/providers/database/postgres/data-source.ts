import { DataSource } from 'typeorm';
import 'dotenv/config';

// Entities
import { Role } from '@hirenetix/models/roles/entities/role.entity';
import { Permission } from '@hirenetix/models/roles/entities/permission.entity';
import { RolePermission } from '@hirenetix/models/roles/entities/role-permission.entity';
import { UserRoleAssignment } from '@hirenetix/models/roles/entities/user-role-assignment.entity';
import { User } from '@hirenetix/models/users/entities/user.entity';
import { Organisation } from '@hirenetix/models/organisations/entities/organisation.entity';
import { Department } from '@hirenetix/models/departments/entities/department.entity';
import { Location } from '@hirenetix/models/locations/entities/location.entity';
import { JobTemplate } from '@hirenetix/models/jobs/entities/job-template.entity';
import { SectionDefinition } from '@hirenetix/models/jobs/entities/section-definition.entity';
import { JobTemplateSection } from '@hirenetix/models/jobs/entities/job-template-section.entity';
import { JobPost } from '@hirenetix/models/jobs/entities/job-post.entity';
import { JobPostSection } from '@hirenetix/models/jobs/entities/job-post-section.entity';
import { ConversationalFlow } from '@hirenetix/models/jobs/entities/conversational-flow.entity';
import { FlowQuestion } from '@hirenetix/models/jobs/entities/flow-question.entity';
import { FlowOutcome } from '@hirenetix/models/jobs/entities/flow-outcome.entity';
import { FlowCondition } from '@hirenetix/models/jobs/entities/flow-condition.entity';
import { JobPostConversation } from '@hirenetix/models/jobs/entities/job-post-conversation.entity';
import { JobPostMessage } from '@hirenetix/models/jobs/entities/job-post-message.entity';
import { Conversation } from '@hirenetix/models/conversations/entities/conversation.entity';
import { Message } from '@hirenetix/models/conversations/entities/message.entity';
import { Candidate } from '@hirenetix/models/candidates/entities/candidate.entity';
import { JobApplication } from '@hirenetix/models/jobs/entities/job-application.entity';
import { JobApplicationInsights } from '@hirenetix/models/jobs/entities/job-application-insights.entity';

const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
  username: process.env.POSTGRES_USER || 'admin',
  password: process.env.POSTGRES_PASSWORD || 'admin',
  database: process.env.POSTGRES_DB || 'hirenetix_dev',
  synchronize: false,
  logging: true,
  ssl: {
    rejectUnauthorized: false,
  },
  entities: [
    Role,
    Permission,
    RolePermission,
    UserRoleAssignment,
    User,
    Organisation,
    Department,
    Location,
    JobTemplate,
    JobTemplateSection,
    SectionDefinition,
    JobPost,
    JobPostSection,
    ConversationalFlow,
    FlowQuestion,
    FlowOutcome,
    FlowCondition,
    JobPostConversation,
    JobPostMessage,
    Conversation,
    Message,
    Candidate,
    JobApplication,
    JobApplicationInsights,
  ],
  migrations: [__dirname + '/../../../database/migrations/*.ts'],
});

export default AppDataSource;
